#!/usr/bin/env python3

# Test pour reproduire le problème de pic de gain

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

print("=== Test de reproduction du problème ===")

# Votre netlist exacte
netlist_problem = """V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 2 0 3 100k 1M 10M
R2 2 3 2k"""

print("Netlist problématique:")
print(netlist_problem)
print()

try:
    solve_circuit(netlist_problem, 
                 frequency_hz=1000000.0,  # Test à 1MHz
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='3', 
                 freq_min=1000.0,         # 1kHz à 100MHz
                 freq_max=100000000.0, 
                 laplace_domain=True)
    print("✓ Test terminé - vérifiez les résultats")
    
except Exception as e:
    print(f"✗ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
