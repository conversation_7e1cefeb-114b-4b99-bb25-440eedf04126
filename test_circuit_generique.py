#!/usr/bin/env python3
"""
Test des corrections génériques sur un nouveau circuit
pour vérifier la cohérence symbolique/numérique
"""

# Importer le module test.py modifié
import test
import sys
from io import StringIO

def test_circuit_generique():
    """Test du nouveau circuit avec les corrections génériques"""
    
    print("🧪 === TEST DES CORRECTIONS GÉNÉRIQUES ===")
    print("Circuit testé:")
    
    # Nouveau circuit de test
    netlist_str = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print(netlist_str)
    print("\n" + "="*50)
    
    # Paramètres d'analyse
    frequency_hz = 1000.0  # 1 kHz
    do_transfer_function = True
    input_node = "1"
    output_node = "7"
    freq_min = 1.0
    freq_max = 1e9
    
    print(f"📊 Analyse de la fonction de transfert H(s) = V{output_node}/V{input_node}")
    print(f"   Fréquence AC: {frequency_hz} Hz")
    print(f"   Plage: {freq_min} Hz à {freq_max} Hz")
    
    try:
        # Capturer la sortie pour analyser les résultats
        old_stdout = sys.stdout
        captured_output = StringIO()
        
        # Exécuter l'analyse avec les corrections
        print("\n🔧 Exécution avec les corrections génériques...")
        
        # Rediriger temporairement la sortie
        sys.stdout = captured_output
        
        # Appeler la fonction solve_circuit modifiée
        test.solve_circuit(
            netlist_str=netlist_str,
            frequency_hz=frequency_hz,
            do_transfer_function=do_transfer_function,
            input_node=input_node,
            output_node=output_node,
            freq_min=freq_min,
            freq_max=freq_max,
            laplace_domain=True
        )
        
        # Récupérer la sortie
        output = captured_output.getvalue()
        sys.stdout = old_stdout
        
        print("✅ Analyse terminée avec succès!")
        
        # Analyser les résultats pour vérifier la cohérence
        print("\n🔍 === ANALYSE DES RÉSULTATS ===")
        
        # Chercher les expressions symboliques et numériques
        lines = output.split('\n')
        
        # Extraire les paramètres BJT calculés
        bjt_params_found = {}
        for line in lines:
            if 'beta_Q1' in line and '=' in line:
                param_line = line.strip()
                if not param_line.startswith('---'):
                    print(f"📋 {param_line}")
                    
            elif 'gm_Q1' in line and '=' in line:
                param_line = line.strip()
                if not param_line.startswith('---'):
                    print(f"📋 {param_line}")
                    
            elif 'rpi_Q1' in line and '=' in line:
                param_line = line.strip()
                if not param_line.startswith('---'):
                    print(f"📋 {param_line}")
                    
            elif 'ro_Q1' in line and '=' in line:
                param_line = line.strip()
                if not param_line.startswith('---'):
                    print(f"📋 {param_line}")
        
        # Chercher les fonctions de transfert
        symbolic_tf = None
        numeric_tf = None
        
        for i, line in enumerate(lines):
            if f"H(s) = V{output_node}/V{input_node} =" in line:
                # Fonction symbolique trouvée
                symbolic_tf = line.split('=', 2)[-1].strip()
                print(f"\n📈 Fonction symbolique trouvée:")
                print(f"   H(s) = {symbolic_tf[:100]}..." if len(symbolic_tf) > 100 else f"   H(s) = {symbolic_tf}")
                
            elif "Fonction de transfert numérique:" in line:
                # Fonction numérique trouvée
                numeric_tf = line.split(':', 1)[-1].strip()
                print(f"\n📊 Fonction numérique trouvée:")
                print(f"   H_num(s) = {numeric_tf[:100]}..." if len(numeric_tf) > 100 else f"   H_num(s) = {numeric_tf}")
        
        # Vérification de cohérence
        print(f"\n🎯 === VÉRIFICATION DE COHÉRENCE ===")
        
        if symbolic_tf and numeric_tf:
            print("✅ Les deux fonctions ont été générées")
            
            # Test simple: vérifier que les fonctions ne sont pas identiques par défaut
            # (ce qui indiquerait un problème dans l'ancien code)
            if symbolic_tf != numeric_tf:
                print("✅ Les fonctions sont différentes (attendu avant substitution)")
            else:
                print("⚠️  Les fonctions sont identiques (inattendu)")
            
            # Vérifier la présence de paramètres dans la fonction symbolique
            bjt_params_in_symbolic = []
            for param in ['beta_Q1', 'gm_Q1', 'rpi_Q1', 'ro_Q1', 'Cbc_Q1', 'Cbe_Q1']:
                if param in symbolic_tf:
                    bjt_params_in_symbolic.append(param)
            
            if bjt_params_in_symbolic:
                print(f"✅ Paramètres BJT trouvés dans la fonction symbolique: {bjt_params_in_symbolic}")
            else:
                print("⚠️  Aucun paramètre BJT trouvé dans la fonction symbolique")
            
            # Vérifier que la fonction numérique ne contient plus de paramètres symboliques
            remaining_symbols = []
            for param in ['beta_Q1', 'gm_Q1', 'rpi_Q1', 'ro_Q1', 'R1', 'R2', 'C1']:
                if param in numeric_tf:
                    remaining_symbols.append(param)
            
            if not remaining_symbols:
                print("✅ Fonction numérique entièrement substituée (aucun symbole restant)")
            else:
                print(f"❌ Symboles restants dans la fonction numérique: {remaining_symbols}")
        
        else:
            print("❌ Impossible de trouver les fonctions de transfert dans la sortie")
        
        # Vérifier la cohérence des paramètres BJT
        print(f"\n🔧 === VÉRIFICATION DES PARAMÈTRES BJT ===")
        
        # Les paramètres devraient être cohérents avec la netlist
        expected_beta = "100"  # De la netlist: Q1 5 3 6 100 NPN
        
        if f"beta_Q1 = {expected_beta}" in output:
            print(f"✅ beta_Q1 cohérent avec la netlist: {expected_beta}")
        else:
            print(f"⚠️  beta_Q1 pourrait ne pas être cohérent avec la netlist")
        
        # Vérifier que gm et rpi sont calculés de manière cohérente
        if "gm_Q1" in output and "rpi_Q1" in output:
            print("✅ Paramètres gm_Q1 et rpi_Q1 calculés")
        else:
            print("⚠️  Paramètres gm_Q1 ou rpi_Q1 manquants")
        
        print(f"\n📁 Résultats détaillés sauvegardés dans le fichier de log")
        print(f"🎯 Test des corrections génériques: {'✅ RÉUSSI' if symbolic_tf and numeric_tf else '❌ ÉCHEC'}")
        
    except Exception as e:
        sys.stdout = old_stdout
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_circuit_generique()
    if success:
        print("\n🎉 Test des corrections génériques terminé!")
    else:
        print("\n💥 Test des corrections génériques échoué!")
