# Icône personnalisée dans la barre des tâches Windows

## Problème résolu

**Avant :** L'application affichait l'icône Python par défaut (console) dans la barre des tâches Windows

**Après :** L'application affiche maintenant une icône personnalisée représentant un diagramme de Bode

## Solution implémentée

### 1. App User Model ID pour Windows

```python
# IMPORTANT: Pour Windows, définir l'App User Model ID pour grouper correctement dans la barre des tâches
try:
    import ctypes
    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("AugmentCode.BodeAnalyzer.1.0")
except:
    pass  # Ignore si pas sur Windows ou si erreur
```

**Pourquoi c'est important :**
- Windows utilise l'App User Model ID pour identifier l'application
- Sans cela, Windows groupe l'application avec Python
- Avec cela, l'application a sa propre identité dans la barre des tâches

### 2. Icône personnalisée

L'application crée automatiquement une icône représentant un diagramme de Bode :

```python
def create_application_icon():
    # Crée une icône 64x64 avec :
    # - Fond bleu avec bordure arrondie
    # - Courbes blanches (magnitude) et jaunes (phase)
    # - Axes de coordonnées
    # - Lettre "B" pour "Bode"
    # - Sauvegarde automatique en "bode_icon.png"
```

### 3. Application de l'icône

```python
# Définir l'icône pour l'application ET la fenêtre
set_application_icon(app)  # Pour l'application
set_application_icon(app, window)  # Pour la fenêtre principale
```

## Comment tester

### Test rapide avec le fichier de test

1. **Exécutez le fichier de test :**
   ```bash
   python test_icone.py
   ```

2. **Regardez la barre des tâches Windows :**
   - Vous devriez voir un cercle bleu avec "B" au lieu de l'icône Python
   - Si vous voyez encore l'icône Python, fermez complètement et relancez

### Test avec l'application principale

1. **Lancez l'application principale :**
   ```bash
   python bode_H1_final.py
   ```

2. **Vérifiez la barre des tâches :**
   - L'icône devrait être un carré bleu avec des courbes de Bode
   - Un fichier `bode_icon.png` sera créé automatiquement

## Apparence de l'icône

L'icône générée automatiquement représente :

- 🔵 **Fond bleu** avec bordure arrondie
- 📊 **Axes gris** : horizontal et vertical
- 📈 **Courbe blanche** : magnitude (décroissante)
- 📉 **Courbe jaune** : phase (en S)
- 🔤 **Lettre "B"** : pour "Bode" (coin supérieur droit)

## Priorité de chargement d'icône

L'application essaie de charger une icône dans cet ordre :

1. `icon.ico`
2. `icon.png`
3. `bode_icon.ico`
4. `bode_icon.png`
5. `app_icon.ico`
6. **Icône générée automatiquement** (si aucun fichier trouvé)

## Utilisation d'une icône personnalisée

Pour utiliser votre propre icône :

1. **Créez votre icône** (format PNG ou ICO, 32x32 ou 64x64 pixels)
2. **Nommez-la** `bode_icon.png` ou `icon.png`
3. **Placez-la** dans le même dossier que `bode_H1_final.py`
4. **Lancez l'application** - elle chargera automatiquement votre icône

## Messages informatifs

L'application affiche dans la console :

- `"App User Model ID défini pour Windows"` (si sur Windows)
- `"Icône chargée depuis: bode_icon.png"` (si fichier trouvé)
- `"Icône générée automatiquement (diagramme de Bode stylisé)"` (si générée)
- `"Icône sauvegardée: bode_icon.png"` (quand l'icône est créée)

## Compatibilité Windows

✅ **Windows 10/11** : Icône visible dans la barre des tâches
✅ **Alt+Tab** : Icône visible dans le sélecteur d'applications
✅ **Menu Démarrer** : Icône visible si épinglé
✅ **Gestionnaire des tâches** : Icône visible dans la liste des processus

## Dépannage

### Si l'icône Python apparaît encore :

1. **Fermez complètement l'application**
2. **Attendez quelques secondes**
3. **Relancez l'application**
4. **Vérifiez les messages dans la console**

### Si l'icône ne change pas :

1. **Vérifiez que vous êtes sur Windows**
2. **Vérifiez les messages d'erreur dans la console**
3. **Essayez de placer un fichier `bode_icon.png` dans le dossier**
4. **Redémarrez l'application**

### Création manuelle d'icône :

Si vous voulez créer votre propre icône :

```python
# Utilisez le fichier test_icone.py comme exemple
# Ou créez un fichier PNG 32x32 ou 64x64 pixels
# Nommez-le bode_icon.png et placez-le dans le dossier
```

## Avantages

✅ **Identification facile** : L'application est reconnaissable dans la barre des tâches
✅ **Professionnel** : Plus d'icône Python générique
✅ **Automatique** : Fonctionne sans configuration
✅ **Personnalisable** : Possibilité d'utiliser sa propre icône
✅ **Persistant** : L'icône reste même après redémarrage

L'application a maintenant sa propre identité visuelle dans Windows !
