#!/usr/bin/env python3
"""
Script pour tracer les diagrammes de Bode des fonctions de transfert 
symbolique et numérique du fichier vue.txt
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re
import os

def extract_parameters_from_vue():
    """Extrait les paramètres numériques du fichier vue.txt"""
    
    filename = "vue.txt"
    if not os.path.exists(filename):
        print(f"Erreur: Le fichier {filename} n'existe pas.")
        return {}
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    parameters = {}
    
    # Extraire la netlist pour les composants passifs
    netlist_match = re.search(r'Netlist originale:(.*?)(?=Vbe_on_Q1|$)', content, re.DOTALL)
    if netlist_match:
        netlist = netlist_match.group(1)
        
        # Résistances
        r_matches = re.findall(r'R(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[kmMG]?)', netlist)
        for r_num, r_val in r_matches:
            val = parse_value(r_val)
            parameters[f'R{r_num}'] = val
            print(f"R{r_num} = {val} Ohm")
        
        # Capacités
        c_matches = re.findall(r'C(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[pnumkMG]?[Ff]?)', netlist)
        for c_num, c_val in c_matches:
            val = parse_value(c_val)
            parameters[f'C{c_num}'] = val
            print(f"C{c_num} = {val} F")
    
    # Extraire les paramètres BJT
    bjt_patterns = {
        'Vbe_on_Q1': r'Vbe_on_Q1 = ([0-9.e-]+)',
        'beta_Q1': r'beta_Q1 = ([0-9.e-]+)',
        'Cbc_Q1': r'Cbc_Q1 = ([0-9.e-]+)',
        'ro_Q1': r'ro_Q1 = ([0-9.e-]+[kmMG]?)',
        'rpi_Q1': r'rpi_Q1 = ([0-9.e-]+)',
        'Cbe_Q1': r'Cbe_Q1 = ([0-9.e-]+)'
    }
    
    for param_name, pattern in bjt_patterns.items():
        match = re.search(pattern, content)
        if match:
            val_str = match.group(1)
            if 'k' in val_str.lower():
                val = float(val_str.replace('k', '').replace('K', '')) * 1000
            elif 'M' in val_str:
                val = float(val_str.replace('M', '')) * 1e6
            elif 'G' in val_str:
                val = float(val_str.replace('G', '')) * 1e9
            else:
                val = float(val_str)
            parameters[param_name] = val
            print(f"{param_name} = {val}")
    
    # Calculer gm_Q1 = beta_Q1 / rpi_Q1
    if 'beta_Q1' in parameters and 'rpi_Q1' in parameters:
        gm_val = parameters['beta_Q1'] / parameters['rpi_Q1']
        parameters['gm_Q1'] = gm_val
        print(f"gm_Q1 = {gm_val} (calculé)")
    
    return parameters

def parse_value(value_str):
    """Parse une valeur avec unité (k, M, G, p, n, u, m)"""
    value_str = value_str.strip().upper()
    
    # Supprimer les unités de base (F, H, Ω)
    value_str = re.sub(r'[FHΩ]$', '', value_str)
    
    multipliers = {
        'P': 1e-12, 'N': 1e-9, 'U': 1e-6, 'M': 1e-3,
        'K': 1e3, 'MEG': 1e6, 'G': 1e9
    }
    
    # Vérifier les multiplicateurs
    for mult, factor in multipliers.items():
        if value_str.endswith(mult):
            base_val = float(value_str[:-len(mult)])
            return base_val * factor
    
    return float(value_str)

def extract_transfer_functions():
    """Extrait les fonctions de transfert symbolique et numérique du fichier vue.txt"""
    
    filename = "vue.txt"
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire la fonction symbolique H1(s) = V7/V1
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert numérique|$)', content, re.DOTALL)
    
    if not h1_symbolic_match:
        print("Erreur: Fonction de transfert symbolique H1(s) = V7/V1 non trouvée")
        return None, None
    
    h1_symbolic = h1_symbolic_match.group(1).strip()
    print(f"Fonction de transfert symbolique trouvée: {len(h1_symbolic)} caractères")
    
    # Extraire la fonction numérique
    h1_numeric_match = re.search(r'Fonction de transfert numérique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    
    if not h1_numeric_match:
        print("Erreur: Fonction de transfert numérique non trouvée")
        return h1_symbolic, None
    
    h1_numeric = h1_numeric_match.group(1).strip()
    print(f"Fonction de transfert numérique trouvée: {len(h1_numeric)} caractères")
    
    return h1_symbolic, h1_numeric

def parse_transfer_function(tf_expr):
    """Parse une expression de fonction de transfert sous forme de fraction"""
    
    tf_expr = tf_expr.strip()
    
    # Chercher le pattern (numérateur)/(dénominateur)
    if ')/(' in tf_expr:
        parts = tf_expr.split(')/(')
        if len(parts) == 2:
            num_expr = parts[0].strip('(')
            den_expr = parts[1].strip(')')
            return num_expr, den_expr
    
    print("Format de fonction de transfert non reconnu")
    return None, None

def create_scipy_transfer_function(tf_expr, parameters=None, is_numeric=False):
    """Crée une fonction de transfert scipy à partir d'une expression avec logique cohérente"""
    
    print(f"\n=== Création de la fonction de transfert ({'numérique' if is_numeric else 'symbolique'}) ===")
    
    # Parser l'expression
    num_expr, den_expr = parse_transfer_function(tf_expr)
    
    if num_expr is None or den_expr is None:
        print("Erreur: Impossible de parser la fonction de transfert")
        return None
    
    try:
        s = sp.Symbol('s')
        
        if is_numeric:
            # Pour la version numérique, les coefficients sont déjà numériques
            print("Traitement de la fonction de transfert numérique...")
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
        else:
            # Pour la version symbolique, substituer les paramètres avec la même logique que test.py
            if parameters is None:
                parameters = {}
            
            print("Substitution des paramètres dans la fonction symbolique...")
            
            # Identifier les résistances de compensation BJT (même logique que test.py)
            bjt_compensation_resistors = set()
            for var, val in parameters.items():
                if var.startswith('comp_BJT_res_') and str(val) == 'true':
                    resistor_name = var.replace('comp_BJT_res_', '')
                    bjt_compensation_resistors.add(resistor_name)
            
            # Parser les expressions symboliques
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
            
            # Substitution manuelle identique à test.py
            for var, val in parameters.items():
                if var != 'k' and not var.startswith('comp_BJT_'):
                    # Forcer la substitution numérique pour les résistances de compensation BJT
                    if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                        try:
                            if var in str(num_sympy):
                                num_sympy = num_sympy.subs(sp.Symbol(var), val)
                            if var in str(den_sympy):
                                den_sympy = den_sympy.subs(sp.Symbol(var), val)
                        except:
                            continue
            
            # Substitutions finales
            num_sympy = num_sympy.subs(sp.Symbol('k'), 1000)
            den_sympy = den_sympy.subs(sp.Symbol('k'), 1000)
            num_sympy = num_sympy.subs(sp.Symbol('pi'), sp.pi)
            den_sympy = den_sympy.subs(sp.Symbol('pi'), sp.pi)
        
        # Extraire les coefficients du polynôme
        num_coeffs = extract_polynomial_coefficients(num_sympy, s)
        den_coeffs = extract_polynomial_coefficients(den_sympy, s)
        
        # Créer la fonction de transfert scipy
        tf = signal.TransferFunction(num_coeffs, den_coeffs)
        
        return tf
        
    except Exception as e:
        print(f"Erreur lors de la création de la fonction de transfert: {e}")
        return None
def extract_polynomial_coefficients(poly_expr, s_symbol):
    """Extrait les coefficients du polynôme"""
    
    # Développer l'expression
    expanded = sp.expand(poly_expr)
    
    # Déterminer le degré maximum
    degree = sp.degree(expanded, s_symbol)
    
    # Extraire les coefficients (ordre décroissant)
    coeffs = []
    for power in range(degree, -1, -1):
        coeff = expanded.coeff(s_symbol, power)
        if coeff is None:
            coeff = 0
        coeffs.append(float(coeff))
    
    print(f"Polynôme de degré {degree}")
    for i, coeff in enumerate(coeffs):
        power = degree - i
        if power > 0:
            print(f"s^{power}: {coeff:.2e}")
        else:
            print(f"s^0 (constant): {coeff:.2e}")
    
    return coeffs

def plot_bode_comparison(tf_symbolic, tf_numeric):
    """Trace les diagrammes de Bode en comparaison (symbolique vs numérique)"""
    
    # Gamme de fréquences (1 Hz à 1 THz)
    frequencies = np.logspace(0, 12, 10000)
    omega = 2 * np.pi * frequencies
    
    # Création du graphique
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    
    # Tracer la fonction symbolique si disponible
    if tf_symbolic is not None:
        try:
            _, h_sym = signal.freqresp(tf_symbolic, omega)
            magnitude_db_sym = 20 * np.log10(np.abs(h_sym))
            phase_deg_sym = np.angle(h_sym) * 180 / np.pi
            
            ax1.semilogx(frequencies, magnitude_db_sym, 'b-', linewidth=2, label='Symbolique')
            ax2.semilogx(frequencies, phase_deg_sym, 'b-', linewidth=2, label='Symbolique')
            
            print(f"Gain DC symbolique: {magnitude_db_sym[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé symbolique: {e}")
    
    # Tracer la fonction numérique si disponible
    if tf_numeric is not None:
        try:
            _, h_num = signal.freqresp(tf_numeric, omega)
            magnitude_db_num = 20 * np.log10(np.abs(h_num))
            phase_deg_num = np.angle(h_num) * 180 / np.pi
            
            ax1.semilogx(frequencies, magnitude_db_num, 'r--', linewidth=2, label='Numérique')
            ax2.semilogx(frequencies, phase_deg_num, 'r--', linewidth=2, label='Numérique')
            
            print(f"Gain DC numérique: {magnitude_db_num[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé numérique: {e}")
    
    # Configuration des graphiques
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - H1(s) = V7/V1 - Comparaison Symbolique vs Numérique')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(1, 1e12)
    ax1.legend()
    
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(1, 1e12)
    ax2.legend()
    
    plt.tight_layout()
    
    # Sauvegarder et afficher
    output_filename = 'bode_vue_comparison.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\nDiagramme de comparaison sauvegardé: {output_filename}")

def main():
    """Fonction principale"""
    
    print("=== Analyse des fonctions de transfert du fichier vue.txt ===")
    
    # Extraire les paramètres du circuit
    print("\n=== Extraction des paramètres ===")
    parameters = extract_parameters_from_vue()
    
    if not parameters:
        print("Attention: Aucun paramètre trouvé")
        return
    
    # Extraire les fonctions de transfert
    print("\n=== Extraction des fonctions de transfert ===")
    h1_symbolic, h1_numeric = extract_transfer_functions()
    
    if h1_symbolic is None and h1_numeric is None:
        print("Erreur: Aucune fonction de transfert trouvée")
        return
    
    # Créer les fonctions de transfert scipy
    tf_symbolic = None
    tf_numeric = None
    
    if h1_symbolic:
        tf_symbolic = create_scipy_transfer_function(h1_symbolic, parameters, is_numeric=False)
    
    if h1_numeric:
        tf_numeric = create_scipy_transfer_function(h1_numeric, {}, is_numeric=True)
    
    # Tracer les diagrammes de Bode
    if tf_symbolic is not None or tf_numeric is not None:
        print("\n=== Génération des diagrammes de Bode ===")
        plot_bode_comparison(tf_symbolic, tf_numeric)
    else:
        print("Erreur: Aucune fonction de transfert n'a pu être créée")

if __name__ == "__main__":
    main()
