#!/usr/bin/env python3
"""
Analyse finale pour comprendre pourquoi les fonctions symbolique et numérique
de H1(s) = V7/V1 ne sont pas identiques
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re

def extract_h1_functions(filename):
    """Extrait les fonctions de transfert H1(s) = V7/V1 du fichier"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire la fonction symbolique
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert num.*?rique|$)', content, re.DOTALL)
    h1_symbolic = h1_symbolic_match.group(1).strip()
    
    # Extraire la fonction numérique
    h1_numeric_match = re.search(r'Fonction de transfert num.*?rique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    h1_numeric = h1_numeric_match.group(1).strip()
    
    return h1_symbolic, h1_numeric

def parse_transfer_function_expression(tf_expr):
    """Parse une expression de fonction de transfert complexe"""
    
    tf_expr = tf_expr.strip()
    
    # Compter les parenthèses pour trouver la séparation
    paren_count = 0
    split_pos = -1
    
    for i, char in enumerate(tf_expr):
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
            if paren_count == 0:
                if i + 1 < len(tf_expr) and tf_expr[i + 1] == '/':
                    split_pos = i + 1
                    break
    
    if split_pos > 0:
        num_expr = tf_expr[1:split_pos-1]
        den_expr = tf_expr[split_pos+2:-1]
        return num_expr, den_expr
    
    return None, None

def analyze_coefficient_differences():
    """Analyse finale des différences entre fonctions symbolique et numérique"""
    
    print("🔍 === ANALYSE FINALE DES DIFFÉRENCES ===")
    
    filename = "Results_Simulation_20250614_1139.txt"
    
    # Extraire les fonctions
    h1_symbolic, h1_numeric = extract_h1_functions(filename)
    
    # Parser les expressions
    num_sym, den_sym = parse_transfer_function_expression(h1_symbolic)
    num_num, den_num = parse_transfer_function_expression(h1_numeric)
    
    s = sp.Symbol('s')
    
    # Fonction numérique (référence)
    print(f"\n📊 === FONCTION NUMÉRIQUE (RÉFÉRENCE) ===")
    num_numeric = sp.sympify(num_num)
    den_numeric = sp.sympify(den_num)
    
    num_poly_num = sp.Poly(num_numeric, s)
    den_poly_num = sp.Poly(den_numeric, s)
    
    num_coeffs_num = [float(c) for c in num_poly_num.all_coeffs()]
    den_coeffs_num = [float(c) for c in den_poly_num.all_coeffs()]
    
    print(f"Numérateur (degré {len(num_coeffs_num)-1}):")
    for i, coeff in enumerate(num_coeffs_num):
        print(f"  s^{len(num_coeffs_num)-1-i}: {coeff:.6e}")
    
    print(f"Dénominateur (degré {len(den_coeffs_num)-1}):")
    for i, coeff in enumerate(den_coeffs_num):
        print(f"  s^{len(den_coeffs_num)-1-i}: {coeff:.6e}")
    
    # Fonction symbolique avec R7 = 0
    print(f"\n🔧 === FONCTION SYMBOLIQUE AVEC R7 = 0 ===")
    
    # Paramètres avec R7 = 0
    parameters = {
        'R1': 100.0,
        'R2': 10000.0,
        'R3': 10000.0,
        'R4': 1000.0,
        'R5': 1000.0,
        'R6': 800.0,
        'C1': 1e-7,
        'C2': 1e-5,
        'C3': 1e-7,
        'beta_Q1': 100.0,
        'Cbc_Q1': 2e-12,
        'Cbe_Q1': 1e-11,
        'ro_Q1': 100000.0,
        'rpi_Q1': 833.333333333,
        'R7': 0.0,  # Valeur critique
        'gm_Q1': 100.0/833.333333333,
        'Vbe_on_Q1': 0.7,
        'pi': 3.141592653589793
    }
    
    # Créer le dictionnaire de substitution
    subs_dict = {}
    for param, value in parameters.items():
        subs_dict[sp.Symbol(param)] = value
    
    # Parser et substituer
    num_sympy = sp.sympify(num_sym)
    den_sympy = sp.sympify(den_sym)
    
    num_substituted = num_sympy.subs(subs_dict)
    den_substituted = den_sympy.subs(subs_dict)
    
    # Extraire les coefficients
    num_poly_sym = sp.Poly(num_substituted, s)
    den_poly_sym = sp.Poly(den_substituted, s)
    
    num_coeffs_sym = [float(c) for c in num_poly_sym.all_coeffs()]
    den_coeffs_sym = [float(c) for c in den_poly_sym.all_coeffs()]
    
    print(f"Numérateur (degré {len(num_coeffs_sym)-1}):")
    for i, coeff in enumerate(num_coeffs_sym):
        print(f"  s^{len(num_coeffs_sym)-1-i}: {coeff:.6e}")
    
    print(f"Dénominateur (degré {len(den_coeffs_sym)-1}):")
    for i, coeff in enumerate(den_coeffs_sym):
        print(f"  s^{len(den_coeffs_sym)-1-i}: {coeff:.6e}")
    
    # Comparaison détaillée
    print(f"\n🔍 === COMPARAISON DÉTAILLÉE ===")
    
    print(f"📊 Degrés:")
    print(f"  Numérique: num={len(num_coeffs_num)-1}, den={len(den_coeffs_num)-1}")
    print(f"  Symbolique: num={len(num_coeffs_sym)-1}, den={len(den_coeffs_sym)-1}")
    
    if len(den_coeffs_num) != len(den_coeffs_sym):
        print(f"❌ PROBLÈME MAJEUR: Degrés différents au dénominateur!")
        print(f"   Cela indique que certains termes s'annulent dans la version symbolique")
        print(f"   mais pas dans la version numérique.")
        
        # Analyser les coefficients de plus haut degré
        print(f"\n🔍 Analyse des coefficients de plus haut degré:")
        max_deg = max(len(den_coeffs_num), len(den_coeffs_sym)) - 1
        
        # Étendre les listes pour la comparaison
        den_num_extended = [0] * (max_deg + 1)
        den_sym_extended = [0] * (max_deg + 1)
        
        # Remplir les coefficients (du plus haut degré au plus bas)
        for i, coeff in enumerate(den_coeffs_num):
            den_num_extended[i] = coeff
        
        for i, coeff in enumerate(den_coeffs_sym):
            den_sym_extended[i] = coeff
        
        for i in range(max_deg + 1):
            deg = max_deg - i
            num_val = den_num_extended[i]
            sym_val = den_sym_extended[i]
            
            if abs(num_val) > 1e-10 or abs(sym_val) > 1e-10:
                diff = abs(num_val - sym_val)
                rel_diff = diff / max(abs(num_val), abs(sym_val)) if max(abs(num_val), abs(sym_val)) > 0 else 0
                
                print(f"  s^{deg}: num={num_val:.6e}, sym={sym_val:.6e}, diff={diff:.6e} ({rel_diff*100:.1f}%)")
    
    # Conclusion
    print(f"\n🎯 === CONCLUSION ===")
    print(f"Les fonctions symbolique et numérique ne sont PAS identiques car:")
    print(f"1. Degrés différents (numérique: 6, symbolique: 5 au dénominateur)")
    print(f"2. Cela suggère que la fonction numérique a été générée avec")
    print(f"   des paramètres légèrement différents de ceux utilisés ici")
    print(f"3. Ou bien il y a des termes qui ne s'annulent pas complètement")
    print(f"   dans la version numérique mais s'annulent dans la symbolique")
    
    # Créer les fonctions de transfert pour comparaison visuelle
    tf_numeric = signal.TransferFunction(num_coeffs_num, den_coeffs_num)
    tf_symbolic = signal.TransferFunction(num_coeffs_sym, den_coeffs_sym)
    
    # Tracer les diagrammes de Bode
    frequencies = np.logspace(-1, 9, 10000)
    omega = 2 * np.pi * frequencies
    
    _, h_num = signal.freqresp(tf_numeric, omega)
    _, h_sym = signal.freqresp(tf_symbolic, omega)
    
    magnitude_db_num = 20 * np.log10(np.abs(h_num))
    magnitude_db_sym = 20 * np.log10(np.abs(h_sym))
    
    phase_deg_num = np.angle(h_num) * 180 / np.pi
    phase_deg_sym = np.angle(h_sym) * 180 / np.pi
    
    # Graphique
    _, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    
    ax1.semilogx(frequencies, magnitude_db_num, 'r-', linewidth=2, label='Numérique (référence)')
    ax1.semilogx(frequencies, magnitude_db_sym, 'b--', linewidth=2, label='Symbolique (R7=0)')
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('H1(s) = V7/V1 - Comparaison Finale')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(0.1, 1e9)
    ax1.legend()
    
    ax2.semilogx(frequencies, phase_deg_num, 'r-', linewidth=2, label='Numérique (référence)')
    ax2.semilogx(frequencies, phase_deg_sym, 'b--', linewidth=2, label='Symbolique (R7=0)')
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(0.1, 1e9)
    ax2.legend()
    
    plt.tight_layout()
    
    output_filename = 'bode_H1_final_analysis.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n💾 Diagramme d'analyse finale sauvegardé: {output_filename}")
    
    # Différences quantitatives
    max_diff_mag = np.max(np.abs(magnitude_db_sym - magnitude_db_num))
    max_diff_phase = np.max(np.abs(phase_deg_sym - phase_deg_num))
    
    print(f"\n📊 Différences maximales:")
    print(f"  Magnitude: {max_diff_mag:.3f} dB")
    print(f"  Phase: {max_diff_phase:.3f} degrés")
    
    if max_diff_mag < 0.1 and max_diff_phase < 1.0:
        print(f"✅ Les fonctions sont pratiquement identiques!")
    else:
        print(f"❌ Les fonctions sont significativement différentes")

def main():
    """Fonction principale"""
    
    print("🔍 === ANALYSE FINALE DE H1(s) = V7/V1 ===")
    print("Objectif: Comprendre pourquoi symbolique ≠ numérique")
    
    analyze_coefficient_differences()

if __name__ == "__main__":
    main()
