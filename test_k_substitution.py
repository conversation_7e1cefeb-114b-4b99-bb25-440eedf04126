#!/usr/bin/env python3
"""
Test de la substitution de k par 1e3
"""
import subprocess
import sys
import os

def test_k_substitution():
    """Teste que k est bien remplacé par 1e3"""
    
    print("=== TEST DE LA SUBSTITUTION DE k ===")
    
    # Circuit de l'utilisateur
    user_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit utilisateur:")
    print(user_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{user_circuit}"""
    
    print("=== Test substitution k ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "7", 1.0, 1e12, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    with open('run_k_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_k_test.py")
    print("Exécution du test (timeout: 60s)...")
    print()
    
    try:
        result = subprocess.run(['python', 'run_k_test.py'], 
                              capture_output=True, text=True, timeout=60)
        
        output = result.stdout
        error = result.stderr
        
        print("=== ANALYSE DES RÉSULTATS ===")
        
        # Chercher les occurrences de k dans la sortie
        k_count = output.count("*k*")
        k_alone_count = output.count(" k ")
        
        print(f"Occurrences de '*k*' : {k_count}")
        print(f"Occurrences de ' k ' : {k_alone_count}")
        
        if k_count == 0 and k_alone_count == 0:
            print("✅ Aucun 'k' trouvé - substitution réussie")
        else:
            print("❌ Des 'k' sont encore présents - substitution échouée")
            
            # Montrer quelques lignes avec k
            lines = output.split('\n')
            k_lines = [line for line in lines if 'k' in line and ('*k*' in line or ' k ' in line)]
            if k_lines:
                print("\nLignes contenant 'k':")
                for i, line in enumerate(k_lines[:3]):  # Montrer max 3 lignes
                    print(f"  {i+1}: {line[:100]}...")
        
        # Chercher les erreurs
        if "Erreur: Coefficients nuls" in output:
            print("❌ Erreur 'Coefficients nuls' encore présente")
        else:
            print("✅ Plus d'erreur 'Coefficients nuls'")
        
        if "Fonction de transfert finale:" in output:
            print("✅ Fonction de transfert finale affichée")
        else:
            print("❌ Fonction de transfert finale non affichée")
        
        if "Diagramme de Bode affiche" in output:
            print("✅ Diagramme de Bode généré")
        else:
            print("❌ Diagramme de Bode non généré")
        
        return k_count == 0 and k_alone_count == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_k_test.py'):
            os.remove('run_k_test.py')

def test_simple_k_substitution():
    """Teste la substitution de k avec un circuit simple"""
    
    print("\n=== TEST SIMPLE DE k ===")
    
    # Circuit très simple
    simple_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n"""
    
    print("Circuit simple:")
    print(simple_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{simple_circuit}"""
    
    print("=== Test simple k ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "2", 1.0, 1e6, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    with open('run_simple_k_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        result = subprocess.run(['python', 'run_simple_k_test.py'], 
                              capture_output=True, text=True, timeout=30)
        
        output = result.stdout
        
        print("Résultat circuit simple:")
        if "*k*" in output or " k " in output:
            print("❌ k encore présent dans circuit simple")
        else:
            print("✅ k correctement substitué dans circuit simple")
        
        if "Fonction de transfert finale:" in output:
            # Extraire la fonction de transfert
            lines = output.split('\n')
            for line in lines:
                if "Fonction de transfert finale:" in line:
                    print(f"Fonction: {line}")
                    break
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test simple: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_simple_k_test.py'):
            os.remove('run_simple_k_test.py')

def main():
    """Fonction principale"""
    
    print("TEST DE LA CORRECTION DE k")
    print("=" * 40)
    
    # Test simple d'abord
    simple_ok = test_simple_k_substitution()
    
    if simple_ok:
        # Test complexe
        complex_ok = test_k_substitution()
        
        if complex_ok:
            print("\n🎉 CORRECTION RÉUSSIE!")
            print("✅ k est maintenant correctement remplacé par 1e3")
            print("✅ Plus de variables symboliques dans la fonction finale")
        else:
            print("\n⚠️  Correction partielle - k encore présent dans circuit complexe")
    else:
        print("\n❌ Problème avec la substitution de k")

if __name__ == "__main__":
    main()
