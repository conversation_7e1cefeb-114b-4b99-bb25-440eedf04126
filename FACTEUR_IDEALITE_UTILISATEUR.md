# Facteur d'idéalité personnalisable pour les diodes

## 🎯 Nouvelle fonctionnalité

L'utilisateur peut maintenant spécifier le **facteur d'idéalité** de chaque diode individuellement.

## 📝 Syntaxe étendue

### Format
```
Dnom anode cathode tension_seuil [facteur_idealite]
```

### Exemples
```
D1 1 2 0.7      # Facteur par défaut (n = 10)
D2 2 3 0.7 1    # Diode idéale (n = 1)
D3 3 4 0.7 2    # Diode silicium (n = 2)
D4 4 0 2.2 5    # Diode Zener (n = 5)
D5 5 0 1.8 15   # LED (n = 15)
```

## ⚙️ Implémentation

### Parsing étendu
```python
values[f"Vth_{comp_name}"] = parse_value(comp[3]) if len(comp) > 3 else "0.7"
values[f"n_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "10"  # Nouveau !
```

### Calcul de rd
```python
rd = (n_utilisateur * VT) / Id_dc
```

## 📊 Impact sur la résistance dynamique

### Formule
```
rd = n * VT / I
```

### Exemples avec Id_dc = 0.019 A
| Facteur n | rd (Ω) | Type de diode |
|-----------|--------|---------------|
| **1** | 1.37 | Idéale |
| **2** | 2.74 | Silicium standard |
| **5** | 6.84 | Diode réelle |
| **10** | 13.68 | **Défaut** |
| **15** | 20.53 | LED |
| **20** | 27.37 | LED haute puissance |

## 🎛️ Contrôle de l'atténuation AC

### Circuit test
V1(3V AC) → R1(100Ω) → D1(2.2V, n) → R2(100Ω) → GND

| Facteur n | V3_ac (V) | Atténuation | Réduction vs n=1 |
|-----------|-----------|-------------|-------------------|
| **1** | 1.490 | 50.3% | 0% |
| **2** | 1.481 | 50.6% | 0.6% |
| **5** | 1.458 | 51.4% | 2.1% |
| **10** | 1.429 | 52.4% | 4.1% |
| **15** | 1.405 | 53.2% | 5.7% |
| **20** | 1.385 | 53.8% | 7.0% |

## 💡 Guide d'utilisation

### Choix du facteur n

#### n = 1 (Diode idéale)
- **Usage** : Simulations théoriques
- **Avantage** : rd minimale, moins d'atténuation
- **Inconvénient** : Irréaliste

#### n = 2 (Diodes silicium)
- **Usage** : Diodes de redressement standard
- **Avantage** : Bon compromis réalisme/performance
- **Exemple** : 1N4007, 1N4148

#### n = 5-8 (Diodes spécialisées)
- **Usage** : Diodes Zener, diodes de puissance
- **Avantage** : Modélisation plus précise
- **Exemple** : Zener 5.1V, diodes Schottky

#### n = 10 (Défaut)
- **Usage** : Valeur conservative générale
- **Avantage** : Bon comportement par défaut
- **Recommandation** : Quand on ne connaît pas le type exact

#### n = 15-20 (LEDs)
- **Usage** : Diodes électroluminescentes
- **Avantage** : Modélise la forte non-linéarité des LEDs
- **Exemple** : LED rouge, verte, bleue

## 🔧 Exemples de circuits

### Redresseur optimisé
```
V1 1 0 DC 0 AC 10
D1 1 2 0.7 1    # Diode idéale pour efficacité maximale
R1 2 0 1k
```

### Régulateur Zener précis
```
V1 1 0 DC 15
R1 1 2 1k
D1 0 2 5.1 8    # Zener avec facteur réaliste
R2 2 0 10k
```

### Indicateur LED
```
V1 1 0 DC 5
R1 1 2 330
D1 2 0 1.8 15   # LED avec forte non-linéarité
```

### Circuit mixte
```
V1 1 0 DC 12 AC 2
R1 1 2 1k
D1 2 3 0.7 2    # Diode silicium standard
R2 3 4 2k
D2 4 0 5.1 8    # Zener de régulation
```

## ✅ Avantages

### 🎯 Flexibilité
- **Personnalisation** par diode
- **Adaptation** au type de composant
- **Contrôle précis** de l'atténuation

### 🔬 Réalisme
- **Modélisation physique** correcte
- **Différents types** de diodes
- **Comportement adaptatif**

### 🛠️ Simplicité
- **Syntaxe intuitive** : paramètre optionnel
- **Valeur par défaut** raisonnable (n = 10)
- **Rétrocompatibilité** : anciens circuits fonctionnent

## 📋 Résumé

**FONCTIONNALITÉ IMPLÉMENTÉE**

- ✅ **Syntaxe étendue** : `Dnom anode cathode Vth [n]`
- ✅ **Facteur personnalisable** par diode
- ✅ **Valeur par défaut** : n = 10
- ✅ **Calcul physique** : rd = n*VT/I
- ✅ **Rétrocompatibilité** totale
- ✅ **Contrôle d'atténuation** précis

L'utilisateur peut maintenant ajuster finement le comportement AC de chaque diode selon son type réel !
