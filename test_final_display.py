#!/usr/bin/env python3
"""
Test final pour vérifier l'affichage simplifié et clair
"""
import subprocess
import os

def test_display_simplification():
    """Teste que l'affichage est maintenant simplifié"""
    
    print("=== TEST DE L'AFFICHAGE SIMPLIFIÉ ===")
    
    # Circuit de test simple
    test_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n"""
    
    print("Circuit de test:")
    print(test_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{test_circuit}"""
    
    print("=== Test de l'affichage final ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "2", 1.0, 1e6, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # <PERSON>uvegard<PERSON> et exécuter le script
    with open('run_display_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_display_test.py")
    print("Exécution du test...")
    print()
    
    try:
        result = subprocess.run(['python', 'run_display_test.py'], 
                              capture_output=True, text=True, timeout=60)
        
        output = result.stdout
        error = result.stderr
        
        print("=== ANALYSE DE L'AFFICHAGE ===")
        
        # Compter les occurrences des différents affichages
        h1_count = output.count("H1(s) = V2/V1 =")
        fonction_symbolique_pure_count = output.count("Fonction symbolique pure")
        fonction_finale_count = output.count("Fonction de transfert finale:")
        substitutions_coherentes_count = output.count("Substitutions coherentes appliquees")
        
        print(f"Occurrences trouvées :")
        print(f"  'H1(s) = V2/V1 =' : {h1_count}")
        print(f"  'Fonction symbolique pure' : {fonction_symbolique_pure_count}")
        print(f"  'Fonction de transfert finale:' : {fonction_finale_count}")
        print(f"  'Substitutions coherentes appliquees' : {substitutions_coherentes_count}")
        print()
        
        # Vérifier l'affichage optimal
        success = True
        
        if h1_count == 1:
            print("✅ Une seule fonction H1(s) affichée (avec Fc substitués)")
        else:
            print(f"❌ Problème: {h1_count} fonctions H1(s) trouvées (attendu: 1)")
            success = False
        
        if fonction_symbolique_pure_count == 0:
            print("✅ Plus d'affichage redondant 'Fonction symbolique pure'")
        else:
            print(f"❌ Problème: {fonction_symbolique_pure_count} affichages redondants trouvés")
            success = False
        
        if fonction_finale_count == 1:
            print("✅ Une seule 'Fonction de transfert finale' affichée")
        else:
            print(f"❌ Problème: {fonction_finale_count} fonctions finales trouvées (attendu: 1)")
            success = False
        
        if substitutions_coherentes_count == 1:
            print("✅ Message de cohérence affiché")
        else:
            print(f"❌ Problème: {substitutions_coherentes_count} messages de cohérence (attendu: 1)")
            success = False
        
        # Vérifier qu'il n'y a plus de messages d'erreur
        if "ATTENTION Difference detectee" in output:
            print("❌ PROBLÈME: Message d'erreur encore présent")
            success = False
        else:
            print("✅ Plus de message 'ATTENTION Difference detectee'")
        
        print()
        if success:
            print("🎉 AFFICHAGE OPTIMAL ATTEINT!")
            print("\n📝 MAINTENANT VOUS VERREZ SEULEMENT :")
            print("1. H1(s) = V2/V1 = [expression avec Fc substitués]")
            print("2. Fonction de transfert finale: [expression avec toutes les valeurs]")
            print("3. Substitutions coherentes appliquees")
            print("4. Diagramme de Bode affiche")
        else:
            print("⚠️  Affichage partiellement amélioré")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_display_test.py'):
            os.remove('run_display_test.py')

def verify_code_changes():
    """Vérifie que les changements de code sont corrects"""
    
    print("\n=== VÉRIFICATION DES CHANGEMENTS ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier que la ligne redondante a été supprimée
    checks = [
        ("Plus de 'Fonction symbolique pure'", "Fonction symbolique pure (sans substitutions)" not in content),
        ("H1(s) présent", "H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function_with_fc_values}" in content),
        ("Fonction finale présente", "Fonction de transfert finale:" in content),
        ("Message cohérence présent", "Substitutions coherentes appliquees" in content)
    ]
    
    print("Vérifications du code :")
    all_good = True
    for check_name, check_result in checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}")
        if not check_result:
            all_good = False
    
    return all_good

def main():
    """Fonction principale"""
    
    print("TEST DE L'AFFICHAGE FINAL SIMPLIFIÉ")
    print("=" * 50)
    
    # Vérifier les changements de code
    code_ok = verify_code_changes()
    
    if code_ok:
        print("\n✅ Changements de code corrects")
        
        # Tester l'affichage
        display_ok = test_display_simplification()
        
        if display_ok:
            print("\n🎉 MISSION ACCOMPLIE!")
            print("\n✨ RÉSUMÉ DES AMÉLIORATIONS FINALES :")
            print("• Suppression de l'affichage redondant 'Fonction symbolique pure'")
            print("• Conservation de H1(s) avec substitutions Fc des amplis op")
            print("• Une seule 'Fonction de transfert finale' avec toutes les valeurs")
            print("• Messages clairs et non redondants")
            print("• Plus de confusion entre les différentes versions")
            print("\n🎯 L'affichage est maintenant optimal et professionnel !")
        else:
            print("\n⚠️  Améliorations appliquées mais test partiel")
    else:
        print("\n❌ Problème avec les changements de code")

if __name__ == "__main__":
    main()
