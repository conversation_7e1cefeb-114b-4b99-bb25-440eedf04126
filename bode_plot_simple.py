#!/usr/bin/env python3
"""
Script pour extraire et tracer le diagramme de Bode de H1(s) = V7/V1
à partir du fichier Results_Simulation_20250612_2352.txt
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re

def extract_symbolic_transfer_function(filename):
    """Extrait la fonction de transfert symbolique H1(s) = V7/V1 du fichier"""

    print(f"=== Extraction de la fonction symbolique H1(s) = V7/V1 ===")

    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Chercher H1(s) = V7/V1 = (numérateur)/(dénominateur)
    h1_match = re.search(r'H1\(s\) = V7/V1 = \((.*?)\)/\((.*?)\)', content, re.DOTALL)

    if not h1_match:
        print("Erreur: Fonction H1(s) = V7/V1 non trouvée")
        return None, None

    numerator = h1_match.group(1).strip()
    denominator = h1_match.group(2).strip()

    print(f"Fonction symbolique H1(s) = V7/V1 trouvée:")
    print(f"Numérateur: {len(numerator)} caractères")
    print(f"Dénominateur: {len(denominator)} caractères")

    return numerator, denominator

def extract_numeric_transfer_function(filename):
    """Extrait la fonction de transfert numérique du fichier"""

    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Chercher la fonction de transfert numérique (avec caractères spéciaux possibles)
    numeric_match = re.search(r'Fonction de transfert num.*?rique: \((.*?)\)/\((.*?)\)', content, re.DOTALL)

    if not numeric_match:
        print("Erreur: Fonction de transfert numérique non trouvée")
        return None, None

    numerator_str = numeric_match.group(1).strip()
    denominator_str = numeric_match.group(2).strip()

    print(f"Fonction de transfert numérique trouvée:")
    print(f"Numérateur: {len(numerator_str)} caractères")
    print(f"Dénominateur: {len(denominator_str)} caractères")

    return numerator_str, denominator_str

def parse_numeric_transfer_function(num_str, den_str):
    """Parse la fonction de transfert numérique et crée un objet scipy"""

    print("\n=== Parsing de la fonction de transfert numérique ===")

    try:
        # Nettoyer les expressions
        def clean_expression(expr):
            # Remplacer les notations scientifiques
            expr = re.sub(r'(\d+(?:\.\d+)?)e\+(\d+)', r'\1*10**\2', expr)
            expr = re.sub(r'(\d+(?:\.\d+)?)e-(\d+)', r'\1*10**(-\2)', expr)
            return expr

        num_clean = clean_expression(num_str)
        den_clean = clean_expression(den_str)

        # Créer le symbole s
        s = sp.Symbol('s')

        print("Parsing du numérateur...")
        num_expr = sp.sympify(num_clean)
        print("Parsing du dénominateur...")
        den_expr = sp.sympify(den_clean)

        print("Extraction des coefficients...")
        num_coeffs = extract_coefficients(num_expr, s)
        den_coeffs = extract_coefficients(den_expr, s)

        # Créer la fonction de transfert scipy
        tf = signal.TransferFunction(num_coeffs, den_coeffs)

        print(f"Fonction de transfert numérique créée avec succès!")
        print(f"Ordre du numérateur: {len(num_coeffs)-1}")
        print(f"Ordre du dénominateur: {len(den_coeffs)-1}")

        return tf

    except Exception as e:
        print(f"Erreur lors du parsing: {e}")
        return None

def extract_circuit_values(filename):
    """Extrait les valeurs des composants du circuit"""

    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    values = {}

    # Extraire la netlist
    netlist_match = re.search(r'Netlist originale:(.*?)(?=Temps de calcul|$)', content, re.DOTALL)

    if netlist_match:
        netlist = netlist_match.group(1)

        # Extraire les résistances
        r_matches = re.findall(r'R(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[kmMG]?)', netlist)
        for r_num, r_val in r_matches:
            values[f'R{r_num}'] = parse_value(r_val)

        # Extraire les capacités
        c_matches = re.findall(r'C(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[pnumkMG]?[Ff]?)', netlist)
        for c_num, c_val in c_matches:
            values[f'C{c_num}'] = parse_value(c_val)

    # Extraire les paramètres BJT
    bjt_section_match = re.search(r'===== Transistor BJT parameters =====(.*?)===== DC Analysis', content, re.DOTALL)

    if bjt_section_match:
        bjt_section = bjt_section_match.group(1)

        # Beta
        beta_match = re.search(r'beta_Q1.*?= (\d+(?:\.\d+)?)', bjt_section)
        if beta_match:
            values['beta_Q1'] = float(beta_match.group(1))

        # Capacités BJT
        cbe_match = re.search(r'Cbe_Q1.*?= ([0-9.e-]+) F', bjt_section)
        if cbe_match:
            values['Cbe_Q1'] = float(cbe_match.group(1))

        cbc_match = re.search(r'Cbc_Q1.*?= ([0-9.e-]+) F', bjt_section)
        if cbc_match:
            values['Cbc_Q1'] = float(cbc_match.group(1))

        # Transconductance et résistances (valeurs numériques)
        gm_match = re.search(r'gm_Q1.*?Valeur num.*?rique: ([0-9.]+) S', bjt_section)
        if gm_match:
            values['gm_Q1'] = float(gm_match.group(1))

        rpi_match = re.search(r'rpi_Q1.*?Valeur num.*?rique: ([0-9.]+) Ohm', bjt_section)
        if rpi_match:
            values['rpi_Q1'] = float(rpi_match.group(1))

        ro_match = re.search(r'ro_Q1.*?Valeur num.*?rique: ([0-9.]+[kM]?) Ohm', bjt_section)
        if ro_match:
            values['ro_Q1'] = parse_value(ro_match.group(1))

    # Ajouter R7 (valeur fictive car elle apparaît dans l'expression mais pas dans la netlist)
    # R7 semble être une résistance interne ou calculée
    if 'R7' not in values:
        values['R7'] = 0.001  # Essayer avec une très petite valeur (quasi court-circuit)

    print(f"\n=== Valeurs extraites ===")
    for key, value in values.items():
        if 'C' in key:
            print(f"{key} = {value:.2e} F")
        elif 'gm' in key:
            print(f"{key} = {value:.3f} S")
        else:
            print(f"{key} = {value}")

    return values

def parse_value(value_str):
    """Parse une valeur avec unité"""
    value_str = value_str.strip().upper()
    value_str = re.sub(r'[FHΩ]$', '', value_str)
    
    multipliers = {
        'P': 1e-12, 'N': 1e-9, 'U': 1e-6, 'M': 1e-3,
        'K': 1e3, 'MEG': 1e6, 'G': 1e9
    }
    
    for mult, factor in multipliers.items():
        if value_str.endswith(mult):
            base_val = float(value_str[:-len(mult)])
            return base_val * factor
    
    return float(value_str)

def create_transfer_function(numerator_str, denominator_str, values):
    """Crée la fonction de transfert à partir des expressions symboliques"""
    
    print("\n=== Création de la fonction de transfert H1(s) = V7/V1 ===")
    
    # Remplacer les notations scientifiques
    def clean_expression(expr):
        expr = re.sub(r'(\d+(?:\.\d+)?)e\+(\d+)', r'\1*10**\2', expr)
        expr = re.sub(r'(\d+(?:\.\d+)?)e-(\d+)', r'\1*10**(-\2)', expr)
        return expr
    
    numerator_clean = clean_expression(numerator_str)
    denominator_clean = clean_expression(denominator_str)
    
    try:
        # Créer les symboles
        s = sp.Symbol('s')
        
        # Créer les symboles pour les paramètres
        symbols_dict = {sp.Symbol('pi'): np.pi}
        for param, value in values.items():
            symbols_dict[sp.Symbol(param)] = value
        
        print("Parsing du numérateur...")
        num_expr = sp.sympify(numerator_clean)
        print("Parsing du dénominateur...")
        den_expr = sp.sympify(denominator_clean)
        
        print("Substitution des valeurs numériques...")
        num_numeric = num_expr.subs(symbols_dict)
        den_numeric = den_expr.subs(symbols_dict)
        
        print("Extraction des coefficients...")
        num_coeffs = extract_coefficients(num_numeric, s)
        den_coeffs = extract_coefficients(den_numeric, s)
        
        # Créer la fonction de transfert scipy
        tf = signal.TransferFunction(num_coeffs, den_coeffs)
        
        print(f"Fonction de transfert créée avec succès!")
        print(f"Ordre du numérateur: {len(num_coeffs)-1}")
        print(f"Ordre du dénominateur: {len(den_coeffs)-1}")
        
        return tf
        
    except Exception as e:
        print(f"Erreur lors de la création de la fonction de transfert: {e}")
        return None

def extract_coefficients(poly_expr, s_symbol):
    """Extrait les coefficients d'un polynôme"""
    
    expanded = sp.expand(poly_expr)
    degree = sp.degree(expanded, s_symbol)
    
    coeffs = []
    for power in range(degree, -1, -1):
        coeff = expanded.coeff(s_symbol, power)
        if coeff is None:
            coeff = 0
        coeffs.append(float(coeff))
    
    return coeffs

def plot_bode_comparison_exact(tf_symbolic, tf_numeric):
    """Trace les deux diagrammes de Bode exacts sur le même graphique"""

    print("\n=== Tracé des diagrammes de Bode comparatifs (symbolique vs numérique) ===")

    # Gamme de fréquences
    frequencies = np.logspace(0, 10, 10000)  # 1 Hz à 10 GHz
    omega = 2 * np.pi * frequencies

    # Création du graphique
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))

    # Calcul et tracé de la fonction symbolique (si disponible)
    if tf_symbolic is not None:
        try:
            _, h_sym = signal.freqresp(tf_symbolic, omega)
            magnitude_db_sym = 20 * np.log10(np.abs(h_sym))
            phase_deg_sym = np.angle(h_sym) * 180 / np.pi

            ax1.semilogx(frequencies, magnitude_db_sym, 'r-', linewidth=2, label='Fonction symbolique (avec substitution)')
            ax2.semilogx(frequencies, phase_deg_sym, 'r-', linewidth=2, label='Fonction symbolique (avec substitution)')

            print(f"Gain DC (symbolique): {magnitude_db_sym[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé de la fonction symbolique: {e}")
            tf_symbolic = None

    # Calcul et tracé de la fonction numérique (si disponible)
    if tf_numeric is not None:
        try:
            _, h_num = signal.freqresp(tf_numeric, omega)
            magnitude_db_num = 20 * np.log10(np.abs(h_num))
            phase_deg_num = np.angle(h_num) * 180 / np.pi

            line_style = '--' if tf_symbolic is not None else '-'
            ax1.semilogx(frequencies, magnitude_db_num, 'b' + line_style, linewidth=2, label='Fonction numérique exacte')
            ax2.semilogx(frequencies, phase_deg_num, 'b' + line_style, linewidth=2, label='Fonction numérique exacte')

            print(f"Gain DC (numérique): {magnitude_db_num[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé de la fonction numérique: {e}")
            tf_numeric = None

    # Vérification de la cohérence
    if tf_symbolic is not None and tf_numeric is not None:
        try:
            diff_magnitude = np.abs(magnitude_db_sym - magnitude_db_num)
            max_diff = np.max(diff_magnitude)
            print(f"Différence maximale en magnitude: {max_diff:.2e} dB")

            if max_diff < 1e-6:
                print("✓ Les fonctions symbolique et numérique sont identiques (différence < 1µdB)")
            else:
                print(f"⚠ Les fonctions diffèrent significativement (max diff: {max_diff:.2e} dB)")
        except:
            print("⚠ Impossible de comparer les fonctions")

    # Configuration des axes
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - H1(s) = V7/V1 - Symbolique vs Numérique')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(1, 1e10)
    ax1.legend()

    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(1, 1e10)
    ax2.legend()

    plt.tight_layout()
    plt.savefig('bode_H1_V7_V1_symbolic_vs_numeric.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("Diagramme comparatif sauvegardé: bode_H1_V7_V1_symbolic_vs_numeric.png")

def create_bjt_transfer_function(values):
    """Crée une fonction de transfert approximative pour le circuit BJT"""

    print("\n=== Création d'une fonction de transfert approximative pour le circuit BJT ===")

    # Valeurs par défaut si non trouvées
    R1 = values.get('R1', 100)
    R2 = values.get('R2', 10000)
    R3 = values.get('R3', 10000)
    R4 = values.get('R4', 1000)
    R5 = values.get('R5', 1000)
    R6 = values.get('R6', 800)
    C1 = values.get('C1', 100e-9)
    C2 = values.get('C2', 10e-6)
    C3 = values.get('C3', 100e-9)
    beta = values.get('beta_Q1', 100)
    gm = values.get('gm_Q1', 0.12)  # Valeur approximative

    print(f"Circuit BJT avec:")
    print(f"R1={R1}Ω, R2={R2}Ω, R3={R3}Ω, R4={R4}Ω, R5={R5}Ω, R6={R6}Ω")
    print(f"C1={C1*1e9:.0f}nF, C2={C2*1e6:.0f}µF, C3={C3*1e9:.0f}nF")
    print(f"β={beta}, gm≈{gm:.3f}S")

    # Fonction de transfert approximative pour V7/V1
    # V7 est la sortie du circuit (nœud 7)
    # Le circuit a plusieurs pôles dus aux capacités

    # Fréquences de coupure approximatives
    f1 = 1 / (2 * np.pi * R1 * C1)  # Pôle d'entrée
    f2 = 1 / (2 * np.pi * R5 * C2)  # Pôle de l'émetteur
    f3 = 1 / (2 * np.pi * R6 * C3)  # Pôle de sortie

    # Gain DC approximatif (diviseur de tension de sortie)
    # V7 est connecté via R6, donc c'est un diviseur
    gain_dc = R6 / (R6 + R4)  # Approximation simple

    print(f"\nFréquences de coupure approximatives:")
    print(f"f1 (entrée): {f1/1e3:.1f} kHz")
    print(f"f2 (émetteur): {f2:.1f} Hz")
    print(f"f3 (sortie): {f3/1e3:.1f} kHz")
    print(f"Gain DC approximatif: {gain_dc:.3f} ({20*np.log10(abs(gain_dc)):.1f} dB)")

    # Créer une fonction de transfert avec 3 pôles
    # H(s) = K / [(1 + s/ω1)(1 + s/ω2)(1 + s/ω3)]

    omega1 = 2 * np.pi * f1
    omega2 = 2 * np.pi * f2
    omega3 = 2 * np.pi * f3

    # Coefficients du dénominateur: s³ + a₂s² + a₁s + a₀
    a2 = omega1 + omega2 + omega3
    a1 = omega1*omega2 + omega1*omega3 + omega2*omega3
    a0 = omega1*omega2*omega3

    # Numérateur et dénominateur
    num = [gain_dc * a0]  # Gain DC × produit des ωᵢ
    den = [1, a2, a1, a0]

    tf = signal.TransferFunction(num, den)

    return tf

def main():
    """Fonction principale"""

    filename = "Results_Simulation_20250612_2352.txt"

    print("=== Analyse du circuit BJT - H1(s) = V7/V1 ===")
    print("Extraction de la fonction symbolique ET numérique")

    # 1. Extraire la fonction de transfert symbolique H1(s) = V7/V1
    print("\n=== 1. Fonction de transfert symbolique H1(s) = V7/V1 ===")
    sym_num_str, sym_den_str = extract_symbolic_transfer_function(filename)

    # 2. Extraire la fonction de transfert numérique
    print("\n=== 2. Fonction de transfert numérique ===")
    num_str, den_str = extract_numeric_transfer_function(filename)

    # 3. Extraire les valeurs des composants
    print("\n=== 3. Valeurs des composants ===")
    values = extract_circuit_values(filename)

    # 4. Créer les fonctions de transfert
    tf_symbolic = None
    tf_numeric = None

    if sym_num_str and sym_den_str and values:
        print("\n=== 4. Création de la fonction symbolique avec substitution ===")
        tf_symbolic = create_transfer_function(sym_num_str, sym_den_str, values)

    if num_str and den_str:
        print("\n=== 5. Création de la fonction numérique ===")
        tf_numeric = parse_numeric_transfer_function(num_str, den_str)

    # 5. Tracer les deux diagrammes de Bode sur le même graphique
    print("\n=== 6. Comparaison des diagrammes de Bode ===")
    if tf_symbolic is not None or tf_numeric is not None:
        plot_bode_comparison_exact(tf_symbolic, tf_numeric)
    else:
        print("Erreur: Aucune fonction de transfert n'a pu être créée")

    # 6. Résumé
    print("\n=== Résumé ===")
    if tf_symbolic is not None:
        print("✓ Fonction de transfert symbolique (avec substitution) créée avec succès")
    else:
        print("✗ Échec de la création de la fonction symbolique")

    if tf_numeric is not None:
        print("✓ Fonction de transfert numérique extraite avec succès")
    else:
        print("✗ Échec de l'extraction de la fonction numérique")

    if tf_symbolic is not None and tf_numeric is not None:
        print("✓ Les deux fonctions devraient être identiques (symbolique = numérique)")
    else:
        print("⚠ Impossible de comparer les deux fonctions")

if __name__ == "__main__":
    main()
