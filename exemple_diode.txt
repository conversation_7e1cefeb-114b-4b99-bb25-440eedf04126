Exemple de circuit avec diode pour tester le simulateur modifié
================================================================

Netlist d'exemple:
V1 1 0 DC 5 AC 1
R1 1 2 1k
D1 2 0 0.7
R2 2 3 2k

Description du circuit:
- V1: Source de tension de 5V DC et 1V AC entre nœuds 1 et 0
- R1: Résistance de 1kΩ entre nœuds 1 et 2  
- D1: Diode avec tension de seuil 0.7V entre nœuds 2 (anode) et 0 (cathode)
- R2: Résistance de 2kΩ entre nœuds 2 et 3

Paramètres de la diode:
- Vth_D1 = 0.7 V (tension de seuil, paramètre symbolique)
- rd_D1 = 1e-3 Ω (résistance dynamique par défaut)

Modélisation:
- En DC: La diode est modélisée comme une source de tension de seuil (Vth_D1) en série avec une résistance (rd_D1)
- En AC: La diode se comporte comme une résistance dynamique (rd_D1)

Équations du modèle:
- DC: V_anode - V_cathode = Vth_D1 + I_D1 * rd_D1
- AC: I_D1 = (V_anode - V_cathode) / rd_D1

Avantages de cette implémentation:
1. Tension de seuil paramétrique (symbolique dans les expressions)
2. Modèle simple mais efficace pour l'analyse de circuits
3. Compatible avec l'architecture existante du simulateur
4. Modifications minimales du code existant
