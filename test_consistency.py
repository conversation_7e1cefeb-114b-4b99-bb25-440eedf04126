#!/usr/bin/env python3
"""
Script de test pour vérifier la cohérence entre expressions symboliques et numériques
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

def test_bjt_circuit():
    """Test avec un circuit simple contenant un BJT"""
    print("=== Test Circuit BJT ===")
    
    netlist_bjt = """R1 1 2 1k
V1 1 0 DC 5 AC 1
Q1 3 2 0 100 NPN
R2 2 0 10k
R3 3 1 2k"""
    
    print("Netlist BJT:")
    print(netlist_bjt)
    print("\nAnalyse en cours...")
    
    try:
        solve_circuit(netlist_bjt, frequency_hz=1000, do_transfer_function=False, laplace_domain=True)
        print("✓ Test BJT terminé avec succès")
    except Exception as e:
        print(f"✗ Erreur dans le test BJT: {e}")
        import traceback
        traceback.print_exc()

def test_opamp_circuit():
    """Test avec un circuit simple contenant un amplificateur opérationnel"""
    print("\n=== Test Circuit Amplificateur Opérationnel ===")
    
    netlist_opamp = """R1 1 2 1k
V1 1 0 DC 0 AC 1
X1 0 2 3 100k 1G 100G
R2 3 2 10k"""
    
    print("Netlist OpAmp:")
    print(netlist_opamp)
    print("\nAnalyse en cours...")
    
    try:
        solve_circuit(netlist_opamp, frequency_hz=1000, do_transfer_function=False, laplace_domain=True)
        print("✓ Test OpAmp terminé avec succès")
    except Exception as e:
        print(f"✗ Erreur dans le test OpAmp: {e}")
        import traceback
        traceback.print_exc()

def test_simple_circuit():
    """Test avec un circuit simple sans BJT ni OpAmp pour référence"""
    print("\n=== Test Circuit Simple (Référence) ===")
    
    netlist_simple = """R1 1 2 1k
V1 1 0 DC 5 AC 1
R2 2 0 2k
C1 2 0 1u"""
    
    print("Netlist Simple:")
    print(netlist_simple)
    print("\nAnalyse en cours...")
    
    try:
        solve_circuit(netlist_simple, frequency_hz=1000, do_transfer_function=False, laplace_domain=True)
        print("✓ Test circuit simple terminé avec succès")
    except Exception as e:
        print(f"✗ Erreur dans le test circuit simple: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Tests de cohérence symbolique/numérique")
    print("=" * 50)
    
    # Test avec circuit simple d'abord
    test_simple_circuit()
    
    # Test avec BJT
    test_bjt_circuit()
    
    # Test avec OpAmp
    test_opamp_circuit()
    
    print("\n" + "=" * 50)
    print("Tests terminés. Vérifiez les fichiers de résultats générés.")
