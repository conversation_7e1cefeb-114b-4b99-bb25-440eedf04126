#!/usr/bin/env python3
"""
Test pour vérifier l'analyse avancée des diodes avec point de polarisation DC
"""

def test_analyse_avancee():
    print("=== Test de l'analyse avancée des diodes ===")
    print("Circuit: V1(5V DC + 3V AC) → R1(100Ω) → D1(2.2V) → GND")
    print()
    
    # Paramètres du circuit
    V1_dc = 5.0  # V
    V1_ac = 3.0  # V
    Vth_D1 = 2.2  # V
    R1 = 100.0  # Ω
    Vt = 0.026  # V (tension thermique)
    
    # Point de fonctionnement DC
    V2_dc = Vth_D1  # Imposé par la diode
    Id_dc = (V1_dc - V2_dc) / R1
    rd_calculated = Vt / Id_dc
    
    print("=== Analyse du point de fonctionnement DC ===")
    print(f"V1_dc = {V1_dc} V")
    print(f"V2_dc = {V2_dc} V (imposé par Vth_D1)")
    print(f"Id_dc = {Id_dc:.6f} A")
    print(f"rd = Vt/Id_dc = {Vt}/{Id_dc:.6f} = {rd_calculated:.3f} Ω")
    print()
    
    # Analyse de l'état de la diode
    seuil_conduction = 1e-9  # 1 nA
    V_diode_dc = V2_dc  # Tension aux bornes de la diode
    
    if Id_dc > seuil_conduction and V_diode_dc > 0.5 * Vth_D1:
        etat_diode = "CONDUCTRICE"
        print(f"État de la diode: {etat_diode}")
        print(f"  V_dc aux bornes = {V_diode_dc:.3f} V")
        print(f"  Id_dc = {Id_dc:.6f} A")
        print(f"  Critère conduction: Id > {seuil_conduction} A ✓")
        print(f"  Critère tension: V > {0.5 * Vth_D1} V ✓")
    else:
        etat_diode = "BLOQUEE"
        print(f"État de la diode: {etat_diode}")
    print()
    
    # Analyse du régime AC (petit signal vs grand signal)
    if etat_diode == "CONDUCTRICE":
        # Estimation de l'amplitude AC aux bornes de la diode
        # Dans ce circuit simple: V2_ac ≈ V1_ac * rd/(R1 + rd)
        amplitude_ac_estimee = V1_ac * rd_calculated / (R1 + rd_calculated)
        
        limite_petit_signal = 0.1 * Vt  # 10% de Vt ≈ 2.6 mV
        
        print("=== Analyse du régime AC ===")
        print(f"Amplitude AC source: {V1_ac} V")
        print(f"Amplitude AC estimée aux bornes diode: {amplitude_ac_estimee:.6f} V")
        print(f"Limite petit signal: {limite_petit_signal:.6f} V")
        print()
        
        if amplitude_ac_estimee < limite_petit_signal:
            regime = "PETIT SIGNAL"
            fiabilite = "FIABLE"
            print(f"Régime: {regime}")
            print(f"Analyse AC: {fiabilite}")
            print(f"  ✓ {amplitude_ac_estimee:.6f} V < {limite_petit_signal:.6f} V")
            print(f"  ✓ La résistance dynamique rd = {rd_calculated:.3f} Ω est valide")
        else:
            regime = "GRAND SIGNAL"
            fiabilite = "APPROXIMATIVE"
            print(f"Régime: {regime}")
            print(f"Analyse AC: {fiabilite}")
            print(f"  ⚠️  {amplitude_ac_estimee:.6f} V > {limite_petit_signal:.6f} V")
            print(f"  ⚠️  La diode peut changer d'état pendant le cycle AC")
            print(f"  ⚠️  Résultats AC approximatifs - écrêtage possible")
        print()
    
    # Comparaison avec l'ancienne méthode
    print("=== Comparaison avec l'ancienne méthode ===")
    rd_old = 10.0  # Ancienne valeur fixe
    V2_ac_old = V1_ac * rd_old / (R1 + rd_old)
    V2_ac_new = V1_ac * rd_calculated / (R1 + rd_calculated)
    
    print(f"Ancienne méthode (rd fixe = 10Ω):")
    print(f"  V2_ac = {V2_ac_old:.6f} V")
    print(f"Nouvelle méthode (rd calculée = {rd_calculated:.3f}Ω):")
    print(f"  V2_ac = {V2_ac_new:.6f} V")
    print(f"Amélioration: {abs(V2_ac_new - V2_ac_old):.6f} V de différence")
    print()
    
    print("✓ L'analyse prend maintenant en compte:")
    print("  - Le point de polarisation DC réel")
    print("  - L'état de la diode (conductrice/bloquée)")
    print("  - Le régime AC (petit/grand signal)")
    print("  - La validité de l'analyse linéaire")

def test_cas_limites():
    print("\n=== Test des cas limites ===")
    
    # Cas 1: Diode bloquée
    print("Cas 1: Diode bloquée (V_dc < Vth)")
    V_dc_bloquee = 0.5  # V (< 2.2V)
    Id_dc_bloquee = 1e-12  # A (courant de fuite)
    print(f"  V_dc = {V_dc_bloquee} V < Vth")
    print(f"  Id_dc = {Id_dc_bloquee} A")
    print(f"  État: BLOQUEE")
    print(f"  rd_AC = 1TΩ (circuit ouvert)")
    print()
    
    # Cas 2: Diode en régime grand signal
    print("Cas 2: Diode en régime grand signal")
    V1_ac_grand = 10.0  # V (amplitude très élevée)
    amplitude_estimee_grand = 1.0  # V (estimation)
    limite_ps = 0.0026  # V
    print(f"  Amplitude AC estimée: {amplitude_estimee_grand} V")
    print(f"  Limite petit signal: {limite_ps} V")
    print(f"  Régime: GRAND SIGNAL ({amplitude_estimee_grand} >> {limite_ps})")
    print(f"  Analyse AC: APPROXIMATIVE")
    print(f"  ⚠️  Écrêtage et distortion attendus")

if __name__ == "__main__":
    test_analyse_avancee()
    test_cas_limites()
