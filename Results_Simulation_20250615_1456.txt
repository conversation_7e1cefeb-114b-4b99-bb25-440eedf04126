--- Simulation Results - 2025-06-15 14:56:24 ---

Netlist originale:
V1 1 0 DC 5 AC 3
R1 1 2 100
D1 2 0 2.2

Diode D1: CONDUCTRICE, regime GRAND SIGNAL

  V_dc = 2.200V, Id_dc = 0.028000A

  rd = 0.929 Ohm (approximatif)

  ATTENTION: Analyse AC non lineaire - resultats approximatifs

===== Resume analyse diodes pour AC =====

Diodes conductrices: D1

ATTENTION: Diodes en regime grand signal: D1

L'analyse AC lineaire peut donner des resultats approximatifs



===== Diode D1 parameters =====

  Vth_D1 (Tension de seuil) = 2.2 V

  Vt_D1 (Tension thermique) = 0.026 V

  rd_D1 (Resistance dynamique) = 0.9285714285714285 Ohm

  Id_dc_D1 (Courant DC) = -0.028 A

  Note: rd = Vt / |Id_dc| = 0.026 / |-0.028| = 0.9285714285714285 Ohm



===== DC Analysis (Symbolic) =====

V1 = 5

V2 = Vth_D1

I_V1 = (Vth_D1-5)/R1

I_R1 = (-Vth_D1+5)/R1

I_D1 = (Vth_D1-5)/R1

===== DC Analysis (Numerical) =====

V1 = 5.0

V2 = 2.2

I_V1 = -0.028

I_R1 = 0.028

I_D1 = -0.028

===== AC Analysis (Symbolic) =====

V1 = 3

V2 = 3*rd_D1/(R1+rd_D1)

I_V1 = -3/(R1+rd_D1)

I_R1 = 3/(R1+rd_D1)

I_D1 = 3/(R1+rd_D1)

===== AC Analysis (Numerical) =====

V1 = 3.0

V2 = 0.0276008492569

I_V1 = -0.0297239915074

I_R1 = 0.0297239915074

I_D1 = 0.0297239915074

===== AC Analysis (Temporal) - f = 1.0 Hz =====

v1(t) = 3.0*cos(6.283185307179586*t + 0.0)

v2(t) = 0.0276008492569*cos(6.283185307179586*t + 0.0)

i_V1(t) = 0.0297239915074*cos(6.283185307179586*t + 3.14159265359)

i_R1(t) = 0.0297239915074*cos(6.283185307179586*t + 0.0)

i_D1(t) = 0.0297239915074*cos(6.283185307179586*t + 0.0)

===== Fonction de Transfert 1 =====

H1(s) = V2/V1 = rd_D1/(R1+rd_D1)

Fonction de transfert finale: 0.00920028308563

Substitutions numeriques appliquees:

  R1 = 100.0

  rd_D1 = 0.928571428571

