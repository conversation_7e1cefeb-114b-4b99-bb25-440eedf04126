#!/usr/bin/env python3

# Test script pour vérifier les modifications BJT compensation

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

# Exemple de netlist avec un BJT
netlist_example = """V1 1 0 DC 1 AC 1
R1 1 2 10k
C1 2 3 1n
R2 3 4 100k
R3 3 0 100k
Q1 4 3 6 100 NPN 0.1p 0.1p
R4 6 0 10k
C3 6 7 10n
R5 7 0 1k
V2 4 0 DC 10 AC 0"""

print("Test de la netlist avec BJT:")
print(netlist_example)
print("\n" + "="*50 + "\n")

# Simuler l'entrée utilisateur pour les questions interactives
class MockInput:
    def __init__(self, responses):
        self.responses = iter(responses)
    
    def __call__(self, prompt=""):
        try:
            response = next(self.responses)
            print(f"{prompt}{response}")
            return response
        except StopIteration:
            return ""

# Remplacer temporairement input() par notre mock
import builtins
original_input = builtins.input
mock_responses = [
    "1",      # fréquence pour analyse temporelle
    "1",      # nœud d'entrée
    "7",      # nœud de sortie
    "1",      # fréquence min
    "1e10"    # fréquence max (10 GHz pour tester la limitation)
]
builtins.input = MockInput(mock_responses)

try:
    # Exécuter la simulation
    solve_circuit(netlist_example, laplace_domain=True)
finally:
    # Restaurer input() original
    builtins.input = original_input

print("\nTest terminé!")
