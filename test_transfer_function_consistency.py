#!/usr/bin/env python3
"""
Test de cohérence pour les fonctions de transfert des circuits spécifiés.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def test_transfer_function_consistency(circuit_netlist, input_node, output_node, circuit_name):
    """
    Teste la cohérence entre les expressions symboliques et numériques
    pour une fonction de transfert spécifique.
    """
    print(f"\n=== Test de cohérence pour la fonction de transfert {circuit_name} ===")
    print(f"Nœud d'entrée: {input_node}, Nœud de sortie: {output_node}")
    print(f"Netlist:\n{circuit_netlist}")
    
    # Analyser le circuit
    lines = [line.strip() for line in circuit_netlist.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'Q':
            values[f"Vbe_on_{comp_name}"] = "0.7"
            values[f"Vt_{comp_name}"] = "0.025"
            values[f"VA_{comp_name}"] = "100"
            values[f"beta_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100"
            values[f"Cbe_{comp_name}"] = parse_value(comp[6]) if len(comp) > 6 else "0"
            values[f"Cbc_{comp_name}"] = parse_value(comp[7]) if len(comp) > 7 else "0"
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse AC
    print("Analyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    
    # Calcul de la fonction de transfert symbolique
    print("Calcul de la fonction de transfert symbolique...")
    if input_node in voltages_ac and output_node in voltages_ac:
        # Fonction de transfert symbolique
        tf_symbolic = f"({voltages_ac[output_node]})/({voltages_ac[input_node]})"
        tf_symbolic = xcas(f"simplify({tf_symbolic})")
        
        # Fonction de transfert numérique avec l'ancienne méthode
        ac_voltages_num_old = substitute_values(voltages_ac, values, use_improved_method=False)
        tf_numeric_old = f"({ac_voltages_num_old[output_node]})/({ac_voltages_num_old[input_node]})"
        tf_numeric_old = xcas(f"simplify({tf_numeric_old})")
        
        # Fonction de transfert numérique avec la nouvelle méthode
        ac_voltages_num_new = substitute_values(voltages_ac, values, use_improved_method=True)
        tf_numeric_new = f"({ac_voltages_num_new[output_node]})/({ac_voltages_num_new[input_node]})"
        tf_numeric_new = xcas(f"simplify({tf_numeric_new})")
        
        # Vérification de cohérence
        print("\n=== Vérification de cohérence de la fonction de transfert ===")
        
        # Substitution manuelle des valeurs dans l'expression symbolique
        manual_substitution = tf_symbolic
        
        # Remplacer d'abord 'k' par 1000
        manual_substitution = xcas(f"subst({manual_substitution},k,1000)")
        
        # Puis substituer toutes les autres valeurs une par une
        for var, val in values.items():
            if var != 'k' and not var.startswith('comp_BJT_'):
                try:
                    # Vérifier si la variable apparaît dans l'expression
                    if var in str(manual_substitution):
                        manual_substitution = xcas(f"subst({manual_substitution},{var},{val})")
                except:
                    continue
        
        # Simplifier et évaluer
        manual_result = xcas(f"evalf(simplify({manual_substitution}))")
        
        # Comparer avec le résultat numérique
        print(f"\nFonction de transfert symbolique: {tf_symbolic}")
        print(f"\nFonction de transfert numérique (ancienne méthode): {tf_numeric_old}")
        print(f"\nFonction de transfert numérique (nouvelle méthode): {tf_numeric_new}")
        print(f"\nSubstitution manuelle de la fonction symbolique: {manual_result}")
        
        # Vérifier la cohérence avec la nouvelle méthode
        try:
            # Essayer de convertir en nombres complexes pour comparaison
            # Remplacer 's' par une valeur spécifique (par exemple s=j*2*pi*1000)
            s_value = "I*2*pi*1000"  # 1 kHz
            
            numeric_with_s = xcas(f"evalf(subst({tf_numeric_new},s,{s_value}))")
            manual_with_s = xcas(f"evalf(subst({manual_result},s,{s_value}))")
            
            print(f"\nÉvaluation à s={s_value}:")
            print(f"Numérique: {numeric_with_s}")
            print(f"Manuelle: {manual_with_s}")
            
            # Comparer les valeurs numériques
            try:
                numeric_complex = complex(numeric_with_s.replace('I', 'j'))
                manual_complex = complex(manual_with_s.replace('I', 'j'))
                
                rel_diff = abs(numeric_complex - manual_complex) / max(abs(numeric_complex), abs(manual_complex))
                
                if rel_diff < 1e-6:
                    print(f"\n✅ COHÉRENCE CONFIRMÉE: Différence relative = {rel_diff:.2e}")
                    return True
                else:
                    print(f"\n❌ INCOHÉRENCE DÉTECTÉE: Différence relative = {rel_diff:.2e}")
                    return False
            except:
                # Si la conversion en complexe échoue, comparer les chaînes
                if str(numeric_with_s) == str(manual_with_s):
                    print("\n✅ COHÉRENCE CONFIRMÉE (comparaison de chaînes)")
                    return True
                else:
                    print("\n❌ INCOHÉRENCE DÉTECTÉE (comparaison de chaînes)")
                    return False
        except Exception as e:
            print(f"\nErreur lors de la comparaison numérique: {e}")
            
            # Comparer les expressions symboliques directement
            if str(tf_numeric_new) == str(manual_result):
                print("\n✅ COHÉRENCE CONFIRMÉE (comparaison d'expressions)")
                return True
            else:
                print("\n❌ INCOHÉRENCE DÉTECTÉE (comparaison d'expressions)")
                return False
    else:
        print(f"Erreur: Nœuds {input_node} ou {output_node} non trouvés dans les résultats AC")
        return False

if __name__ == "__main__":
    print("Test de cohérence pour les fonctions de transfert")
    print("=" * 60)
    
    # Circuit 1: OpAmp
    circuit1 = """
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 1M 1G
R2 2 3 2k
"""
    
    # Circuit 2: BJT
    circuit2 = """
V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
"""
    
    # Test des fonctions de transfert
    success1 = test_transfer_function_consistency(circuit1, "1", "3", "Circuit OpAmp (1→3)")
    success2 = test_transfer_function_consistency(circuit2, "1", "7", "Circuit BJT (1→7)")
    
    print("\n" + "=" * 60)
    print("RÉSUMÉ DES TESTS:")
    print(f"Circuit OpAmp (1→3): {'✅ SUCCÈS' if success1 else '❌ ÉCHEC'}")
    print(f"Circuit BJT (1→7): {'✅ SUCCÈS' if success2 else '❌ ÉCHEC'}")
    
    if success1 and success2:
        print("\n🎉 Tous les tests sont passés avec succès!")
        print("Les fonctions de transfert symboliques et numériques sont cohérentes.")
    else:
        print("\n⚠️  Des incohérences ont été détectées.")
        print("Les fonctions de transfert symboliques et numériques ne sont pas parfaitement cohérentes.")
