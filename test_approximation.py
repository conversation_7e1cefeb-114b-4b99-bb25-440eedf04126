#!/usr/bin/env python3
"""
Test script pour vérifier la fonction d'approximation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bode_H1_final import (
    extract_variables_from_expression, 
    count_terms_in_expression,
    create_approximation,
    read_transfer_function
)

def test_approximation():
    print("=== Test de la fonction d'approximation ===")
    
    # Lire la fonction de transfert
    try:
        transfer_function = read_transfer_function("FTC.txt")
        print(f"Fonction de transfert lue: {len(transfer_function)} caractères")
        print(f"Début: {transfer_function[:100]}...")
    except Exception as e:
        print(f"Erreur lors de la lecture: {e}")
        return
    
    # Extraire les variables
    variables = extract_variables_from_expression(transfer_function)
    print(f"Variables extraites: {variables}")
    
    # Compter les termes originaux
    original_terms = count_terms_in_expression(transfer_function)
    print(f"Nombre de termes originaux: {original_terms}")
    
    # Définir des plages de test
    slider_ranges = {}
    for var in variables:
        if var.startswith('C'):
            slider_ranges[var] = (1e-12, 1e-3)
        elif var.startswith('R'):
            slider_ranges[var] = (0.1, 10e6)
        elif var.startswith('L'):
            slider_ranges[var] = (1e-9, 100)
        else:
            slider_ranges[var] = (0.1, 1000)
    
    print(f"Plages définies pour {len(slider_ranges)} variables")
    
    # Créer l'approximation
    try:
        print("\n=== Génération de l'approximation ===")
        approximated_function = create_approximation(
            transfer_function, 
            variables, 
            slider_ranges, 
            None  # frequencies not used in current implementation
        )
        
        print(f"Approximation générée: {len(approximated_function)} caractères")
        print(f"Début: {approximated_function[:100]}...")
        
        # Compter les termes approximés
        approx_terms = count_terms_in_expression(approximated_function)
        print(f"Nombre de termes approximés: {approx_terms}")
        
        # Calculer la réduction
        reduction = (original_terms - approx_terms) / original_terms * 100
        print(f"Réduction: {reduction:.1f}%")
        
        # Vérifier les variables
        approx_vars = extract_variables_from_expression(approximated_function)
        print(f"Variables dans l'approximation: {approx_vars}")
        
        missing_vars = set(variables) - set(approx_vars)
        if missing_vars:
            print(f"Variables manquantes: {missing_vars}")
        else:
            print("Toutes les variables sont présentes ✓")
            
    except Exception as e:
        print(f"Erreur lors de l'approximation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_approximation()
