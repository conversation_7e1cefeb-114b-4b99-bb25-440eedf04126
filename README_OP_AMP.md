# Amplificateur Opérationnel (OP) - Documentation

## Syntaxe

```
OPnom V- V+ Vout [gain] [fc1] [fc2]
```

### Paramètres :
- **OPnom** : Nom de l'amplificateur opérationnel (doit commencer par "OP")
- **V-** : Nœud d'entrée inverseuse
- **V+** : Nœud d'entrée non-inverseuse
- **Vout** : Nœud de sortie
- **gain** : Gain en boucle ouverte (optionnel, défaut = 1000000)
- **fc1** : Première fréquence de coupure en Hz (optionnel, défaut = 1000000)
- **fc2** : Seconde fréquence de coupure en Hz (optionnel, défaut = 10000000)

### Exemples :
```
OP1 2 3 4 1000000          # Amplificateur avec valeurs par défaut
OP1 2 3 4 ********** 10000  # Amplificateur avec fc1=100Hz, fc2=10kHz
```

## Modèle

L'amplificateur opérationnel est modélisé avec :

- **Impédance d'entrée infinie** : Aucun courant ne circule dans les entrées V+ et V-
- **Impédance de sortie nulle** : La sortie peut fournir tout courant nécessaire
- **Gain fréquentiel** : H(s) = gain / ((1 + s/(2π×fc1)) × (1 + s/(2π×fc2)))
- **Réponse fréquentielle réaliste** :
  - Gain constant jusqu'à fc1
  - -20dB/décade entre fc1 et fc2
  - -40dB/décade au-delà de fc2

## Configurations typiques

### 1. Amplificateur non-inverseur
```
V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 0 2 3 1000000
R2 3 0 10k
```
**Gain** = 1 + R2/R1 = 1 + 10k/1k = 11

### 2. Amplificateur inverseur
```
V1 1 0 DC 1 AC 1
R1 1 2 1k
R2 2 3 10k
OP1 0 0 3 1000000
```
**Gain** = -R2/R1 = -10k/1k = -10

### 3. Suiveur de tension (buffer)
```
V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 3 2 3 1000000
```
**Gain** = 1 (sortie = entrée)

## Utilisation dans le simulateur

1. Ajoutez l'amplificateur opérationnel dans votre netlist avec la syntaxe appropriée
2. Le simulateur calculera automatiquement :
   - Les tensions DC et AC à tous les nœuds
   - Les courants dans tous les composants
   - Le courant de sortie de l'amplificateur opérationnel

## Exemples de test

Voir les fichiers :
- `test_op_amp.py` : Test basique
- `test_op_amp_buffer.py` : Test en configuration suiveur
- `exemple_op_amp.py` : Exemples des configurations principales

## Comportement fréquentiel

### Paramètres par défaut :
- **gain** = 1000000 (120 dB)
- **fc1** = 1 MHz (pôle dominant)
- **fc2** = 10 MHz (pôle secondaire)

### Réponse typique :
- **0 Hz à 1 MHz** : Gain constant ≈ 120 dB
- **1 MHz à 10 MHz** : Décroissance -20 dB/décade
- **Au-delà de 10 MHz** : Décroissance -40 dB/décade

### Exemples de configurations :
```
# Amplificateur audio (fc1=20Hz, fc2=20kHz)
OP1 2 3 4 100000 20 20000

# Amplificateur haute fréquence (fc1=1kHz, fc2=100MHz)
OP1 2 3 4 1000000 1000 *********

# Amplificateur quasi-idéal (fréquences très hautes)
OP1 2 3 4 1000000 1000000 *********
```

## Notes importantes

- L'amplificateur opérationnel a maintenant une réponse fréquentielle réaliste
- En boucle ouverte, la sortie peut atteindre des valeurs très élevées aux basses fréquences
- Utilisez toujours une rétroaction négative pour des applications pratiques
- Les fréquences de coupure permettent de modéliser des amplificateurs réels
- Pour un comportement quasi-idéal, utilisez des fréquences de coupure très élevées
