#!/usr/bin/env python3
"""
Script de test pour vérifier que les légendes s'affichent correctement
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from bode_H1_final import *
    print("✓ Import du module bode_H1_final réussi")
except ImportError as e:
    print(f"✗ Erreur d'import: {e}")
    sys.exit(1)

def test_legends():
    """Test que les légendes s'affichent correctement"""
    print("\n=== Test des légendes ===")
    
    # Créer une application Qt
    app = QApplication(sys.argv)
    
    # Créer une expression de test simple
    transfer_function = "1/(1+s*R1*C1)"
    variables = ['R1', 'C1']
    values = {'R1': 1000.0, 'C1': 1e-6}
    frequency_params = {'f_min': 1.0, 'f_max': 1e6, 'num_points': 100}
    slider_ranges = {'R1': (100.0, 10000.0), 'C1': (1e-9, 1e-3)}
    
    try:
        # Créer l'analyseur de Bode
        analyzer = BodeAnalyzer(transfer_function, variables, values, frequency_params, slider_ranges)
        print("✓ Création de BodeAnalyzer réussie")
        
        # Vérifier que les légendes existent dès le début
        magnitude_legend = analyzer.magnitude_plot.legend
        phase_legend = analyzer.phase_plot.legend
        
        if magnitude_legend is not None:
            print("✓ Légende de magnitude créée")
        else:
            print("✗ Légende de magnitude manquante")
            
        if phase_legend is not None:
            print("✓ Légende de phase créée")
        else:
            print("✗ Légende de phase manquante")
        
        # Ajouter une expression simplifiée
        simplified_expr = "1/(1+s*1000*1e-6)"
        analyzer.add_simplified_curves(simplified_expr, values)
        print("✓ Expression simplifiée ajoutée")
        
        # Ajouter une expression manuelle
        manual_expr = "0.8/(1+s*1200*1e-6)"
        analyzer.add_manual_curves(manual_expr, values)
        print("✓ Expression manuelle ajoutée")
        
        # Vérifier que toutes les courbes ont des noms
        curves_magnitude = []
        curves_phase = []
        
        # Parcourir tous les items des graphiques
        for item in analyzer.magnitude_plot.listDataItems():
            if hasattr(item, 'name') and item.name():
                curves_magnitude.append(item.name())
                
        for item in analyzer.phase_plot.listDataItems():
            if hasattr(item, 'name') and item.name():
                curves_phase.append(item.name())
        
        print(f"Courbes de magnitude trouvées: {curves_magnitude}")
        print(f"Courbes de phase trouvées: {curves_phase}")
        
        # Vérifier que nous avons les trois expressions
        expected_names = ['Expression originale', 'Expression simplifiée', 'Expression manuelle']
        
        for name in expected_names:
            if name in curves_magnitude:
                print(f"✓ {name} trouvée dans magnitude")
            else:
                print(f"✗ {name} manquante dans magnitude")
                
            if name in curves_phase:
                print(f"✓ {name} trouvée dans phase")
            else:
                print(f"✗ {name} manquante dans phase")
        
        print("✓ Test des légendes terminé")
        
        # Fermer l'application
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        app.quit()
        return False

if __name__ == "__main__":
    print("=== Test des légendes pour toutes les expressions ===")
    
    if test_legends():
        print("\n🎉 Test des légendes réussi!")
    else:
        print("\n❌ Test des légendes échoué.")
        sys.exit(1)
