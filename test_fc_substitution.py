#!/usr/bin/env python3
"""
Test pour vérifier que les paramètres Fc sont remplacés par leurs valeurs numériques
dans les fonctions de transfert des amplificateurs opérationnels.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def test_fc_substitution_opamp():
    """Test du remplacement des paramètres Fc pour le circuit OpAmp"""
    print("=== Test de substitution des paramètres Fc pour OpAmp ===")
    
    netlist_str = """
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 1M 1G
R2 2 3 2k
"""
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse AC
    print("Analyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    
    # Calculer la fonction de transfert symbolique
    input_node, output_node = "1", "3"
    if input_node in voltages_ac and output_node in voltages_ac:
        v_in, v_out = voltages_ac[input_node], voltages_ac[output_node]
        
        # Fonction de transfert originale (avec Fc symboliques)
        transfer_function_original = xcas(f"simplify(({v_out})/({v_in}))")
        
        # Fonction de transfert avec Fc remplacés par leurs valeurs
        transfer_function_with_fc = force_fc_substitution(transfer_function_original, values)
        
        print(f"\nFonction de transfert originale (avec Fc symboliques):")
        print(f"H(s) = {transfer_function_original}")
        
        print(f"\nFonction de transfert avec valeurs Fc numériques:")
        print(f"H(s) = {transfer_function_with_fc}")
        
        # Vérifier que les paramètres Fc ont été remplacés
        fc_params_found = []
        for var in values.keys():
            if var.startswith('Fc') and '_' in var:
                if var in str(transfer_function_original):
                    fc_params_found.append(var)
        
        fc_params_remaining = []
        for var in fc_params_found:
            if var in str(transfer_function_with_fc):
                fc_params_remaining.append(var)
        
        print(f"\nParamètres Fc trouvés dans l'expression originale: {fc_params_found}")
        print(f"Paramètres Fc restants après substitution: {fc_params_remaining}")
        
        # Afficher les valeurs des paramètres Fc
        print(f"\nValeurs des paramètres Fc:")
        for var in fc_params_found:
            print(f"  {var} = {values[var]}")
        
        if not fc_params_remaining:
            print("\n✅ SUCCÈS: Tous les paramètres Fc ont été remplacés par leurs valeurs numériques")
            return True
        else:
            print(f"\n❌ ÉCHEC: {len(fc_params_remaining)} paramètres Fc n'ont pas été remplacés")
            return False
    else:
        print("Erreur: Nœuds non trouvés dans les résultats AC")
        return False

def test_fc_values_display():
    """Test pour afficher les valeurs des paramètres Fc calculés"""
    print("\n=== Test des valeurs calculées des paramètres Fc ===")
    
    # Test avec différentes configurations
    test_configs = [
        {"Av": "100000", "fc2": "1000000", "fc4": "1000000000", "name": "1MHz/1GHz"},
        {"Av": "100000", "fc2": "1000000000", "fc4": "100000000000", "name": "1GHz/100GHz"},
        {"Av": "100000", "fc2": "100000", "fc4": "10000000", "name": "100kHz/10MHz"},
    ]
    
    for config in test_configs:
        print(f"\nConfiguration {config['name']}:")
        print(f"  Av = {config['Av']}")
        print(f"  fc2_user = {config['fc2']}")
        print(f"  fc4_user = {config['fc4']}")
        
        fc2_val = float(config['fc2'])
        fc4_val = float(config['fc4'])
        av_val = float(config['Av'])
        
        if fc2_val < 1e9:
            fc1_calc = fc2_val * 1e-5
            fc4_calc = fc2_val
        else:
            fc1_calc = av_val
            fc4_calc = fc2_val * 2
        
        fc2_calc = fc4_val
        fc3_calc = fc4_val
        
        print(f"  Fc1 calculé = {fc1_calc}")
        print(f"  Fc2 calculé = {fc2_calc}")
        print(f"  Fc3 calculé = {fc3_calc}")
        print(f"  Fc4 calculé = {fc4_calc}")

if __name__ == "__main__":
    print("Test de substitution des paramètres Fc dans les fonctions de transfert")
    print("=" * 70)
    
    success = test_fc_substitution_opamp()
    test_fc_values_display()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Test réussi! Les paramètres Fc sont correctement remplacés par leurs valeurs numériques.")
    else:
        print("❌ Test échoué. Les paramètres Fc ne sont pas correctement remplacés.")
