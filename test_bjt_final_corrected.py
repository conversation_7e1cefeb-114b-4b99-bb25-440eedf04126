#!/usr/bin/env python3
"""
Test final avec le circuit BJT après correction complète de k
"""
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    # Circuit BJT de l'utilisateur
    netlist_str = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("=== Test final circuit BJT avec correction complète ===")
    print("Circuit BJT complet")
    print("Fonction de transfert V1 → V7")
    print("Correction k appliquée partout")
    print()
    
    solve_circuit(netlist_str, 1000.0, True, "1", "7", 1.0, 1e12, laplace_domain=True)
    
    print("\n=== Test terminé ===")
    
except Exception as e:
    print(f"Erreur: {e}")
    import traceback
    traceback.print_exc()
