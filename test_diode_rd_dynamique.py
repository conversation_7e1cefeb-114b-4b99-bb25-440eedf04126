#!/usr/bin/env python3
"""
Test pour vérifier le calcul de la résistance dynamique de la diode
basé sur le point de fonctionnement DC
"""

def test_resistance_dynamique():
    print("=== Test de la résistance dynamique de la diode ===")
    print("Circuit: V1(5V DC, 3V AC) → R1(100Ω) → D1(2.2V) → GND")
    print()
    
    # Calcul du point de fonctionnement DC
    V1_dc = 5.0  # V
    Vth_D1 = 2.2  # V
    R1 = 100.0  # Ω
    
    V2_dc = Vth_D1  # Imposé par la diode
    Id_dc = (V1_dc - V2_dc) / R1  # Courant DC de la diode
    
    print("Point de fonctionnement DC:")
    print(f"V1 = {V1_dc} V")
    print(f"V2 = {V2_dc} V")
    print(f"Id_dc = {Id_dc:.6f} A ({Id_dc*1000:.3f} mA)")
    print()
    
    # Calcul de la résistance dynamique
    Vt = 0.026  # V (tension thermique à 25°C)
    rd_calculated = Vt / Id_dc
    
    print("Calcul de la résistance dynamique:")
    print(f"Vt = {Vt} V (tension thermique)")
    print(f"rd = Vt / Id_dc = {Vt} / {Id_dc:.6f} = {rd_calculated:.3f} Ω")
    print()
    
    # Analyse AC avec la vraie résistance dynamique
    V1_ac = 3.0  # V
    V2_ac_calculated = V1_ac * rd_calculated / (R1 + rd_calculated)
    I_ac_calculated = V1_ac / (R1 + rd_calculated)
    
    print("Analyse AC avec rd calculée:")
    print(f"V1_ac = {V1_ac} V")
    print(f"V2_ac = {V1_ac} × {rd_calculated:.3f} / ({R1} + {rd_calculated:.3f}) = {V2_ac_calculated:.6f} V")
    print(f"I_ac = {V1_ac} / ({R1} + {rd_calculated:.3f}) = {I_ac_calculated:.6f} A")
    print()
    
    # Comparaison avec l'ancienne méthode
    rd_old = 10.0  # Ω (valeur fixe précédente)
    V2_ac_old = V1_ac * rd_old / (R1 + rd_old)
    I_ac_old = V1_ac / (R1 + rd_old)
    
    print("Comparaison avec l'ancienne méthode (rd = 10Ω):")
    print(f"V2_ac_old = {V2_ac_old:.6f} V")
    print(f"I_ac_old = {I_ac_old:.6f} A")
    print()
    
    print("Amélioration:")
    print(f"- Résistance dynamique: {rd_old}Ω → {rd_calculated:.3f}Ω")
    print(f"- Tension AC: {V2_ac_old:.6f}V → {V2_ac_calculated:.6f}V")
    print(f"- Courant AC: {I_ac_old:.6f}A → {I_ac_calculated:.6f}A")
    print()
    
    print("✓ La résistance dynamique est maintenant calculée physiquement")
    print("✓ Basée sur le vrai point de fonctionnement DC de la diode")
    print("✓ Utilise la formule rd = Vt / Id_dc")

def test_cas_limites():
    print("\n=== Test des cas limites ===")
    
    # Cas 1: Diode bloquée (courant très faible)
    print("Cas 1: Diode bloquée (Id_dc ≈ 0)")
    Id_dc_blocked = 1e-15  # A (courant de fuite)
    Vt = 0.026  # V
    if Id_dc_blocked > 1e-12:
        rd_blocked = Vt / Id_dc_blocked
        print(f"rd = {Vt} / {Id_dc_blocked} = {rd_blocked:.0f} Ω")
    else:
        print("rd = 1MΩ (valeur par défaut pour diode bloquée)")
    
    # Cas 2: Diode fortement conductrice
    print("\nCas 2: Diode fortement conductrice (Id_dc = 100 mA)")
    Id_dc_high = 0.1  # A
    rd_high = Vt / Id_dc_high
    print(f"rd = {Vt} / {Id_dc_high} = {rd_high:.3f} Ω")
    
    # Cas 3: Diode faiblement conductrice
    print("\nCas 3: Diode faiblement conductrice (Id_dc = 1 mA)")
    Id_dc_low = 0.001  # A
    rd_low = Vt / Id_dc_low
    print(f"rd = {Vt} / {Id_dc_low} = {rd_low:.1f} Ω")
    
    print("\n✓ La méthode s'adapte automatiquement au point de fonctionnement")

if __name__ == "__main__":
    test_resistance_dynamique()
    test_cas_limites()
