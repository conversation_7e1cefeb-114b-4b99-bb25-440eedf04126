#!/usr/bin/env python3
"""
Test du circuit OpAmp spécifique pour vérifier l'affichage des valeurs Fc numériques
dans la fonction de transfert.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def simulate_opamp_circuit():
    """Simule le circuit OpAmp et affiche la fonction de transfert avec valeurs Fc numériques"""
    
    netlist_str = """
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 1M 1G
R2 2 3 2k
"""
    
    print("Circuit OpAmp à analyser:")
    print(netlist_str)
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    print("\nValeurs des composants:")
    for key, value in values.items():
        if not key.startswith('comp_BJT_'):
            print(f"  {key} = {value}")
    
    # Analyse AC
    print("\nAnalyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    
    # Calculer la fonction de transfert
    input_node, output_node = "1", "3"
    if input_node in voltages_ac and output_node in voltages_ac:
        v_in, v_out = voltages_ac[input_node], voltages_ac[output_node]
        
        print(f"\nTensions AC:")
        print(f"  V{input_node} = {v_in}")
        print(f"  V{output_node} = {v_out}")
        
        # Fonction de transfert originale (avec Fc symboliques)
        transfer_function_original = xcas(f"simplify(({v_out})/({v_in}))")
        
        # Fonction de transfert avec Fc remplacés par leurs valeurs
        transfer_function_with_fc = force_fc_substitution(transfer_function_original, values)
        
        print(f"\n" + "="*80)
        print("FONCTION DE TRANSFERT")
        print("="*80)
        
        print(f"\nAvec paramètres Fc symboliques:")
        print(f"H1(s) = V{output_node}/V{input_node} = {transfer_function_original}")
        
        print(f"\nAvec valeurs Fc numériques:")
        print(f"H1(s) = V{output_node}/V{input_node} = {transfer_function_with_fc}")
        
        # Simplifier encore plus l'expression avec les valeurs numériques
        transfer_function_simplified = xcas(f"simplify({transfer_function_with_fc})")
        print(f"\nSimplifiée:")
        print(f"H1(s) = V{output_node}/V{input_node} = {transfer_function_simplified}")
        
        # Remplacer aussi les autres paramètres pour avoir une expression complètement numérique
        transfer_function_fully_numeric = transfer_function_with_fc
        for var, val in values.items():
            if var in ['R1', 'R2', 'Av_X1'] and var in str(transfer_function_fully_numeric):
                transfer_function_fully_numeric = xcas(f"subst({transfer_function_fully_numeric},{var},{val})")
        
        transfer_function_fully_numeric = xcas(f"simplify({transfer_function_fully_numeric})")
        print(f"\nComplètement numérique:")
        print(f"H1(s) = V{output_node}/V{input_node} = {transfer_function_fully_numeric}")
        
        print("\n" + "="*80)
        
        return True
    else:
        print("Erreur: Nœuds non trouvés dans les résultats AC")
        return False

if __name__ == "__main__":
    print("Test du circuit OpAmp avec affichage des valeurs Fc numériques")
    print("=" * 70)
    
    success = simulate_opamp_circuit()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ Simulation réussie! Les paramètres Fc sont affichés avec leurs valeurs numériques.")
    else:
        print("❌ Simulation échouée.")
