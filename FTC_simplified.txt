# Expression de transfert symbolique simplifiée (méthode intelligente)
# Simplification par conservation des termes dominants
# État des variables après simplification :
# R1 (valeur: 1.159e+00, impact: 0.535 dB) -> Partiellement conservée
# C1 (valeur: 1.910e-05, impact: 0.206 dB) -> Partiellement conservée
# R2 (valeur: 2.291e+00, impact: 0.012 dB) -> Supprimée
# R3 (valeur: 1.770e+03, impact: 0.007 dB) -> Supprimée
# R5 (valeur: 4.786e+04, impact: 0.001 dB) -> Supprimée
# L1 (valeur: 1.104e+01, impact: 0.000 dB) -> Supprimée
# C3 (valeur: 2.455e-06, impact: 0.000 dB) -> Partiellement conservée
# R4 (valeur: 1.000e+03, impact: 0.000 dB) -> Partiellement conservée
# C2 (valeur: 2.477e-10, impact: 0.000 dB) -> Supprimée

(C3*R4*s + 1)/(C1*C3*R1*R4*s**2 + C3*R4*s + 1)