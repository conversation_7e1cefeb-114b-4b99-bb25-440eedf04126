#!/usr/bin/env python3
"""
Test pour vérifier l'impact du facteur d'idéalité n = 2.0
"""

def test_n_factor_impact():
    print("=== Impact du facteur d'idéalité n = 2.0 ===")
    print()
    
    # Paramètres physiques
    VT = 0.026  # V
    
    # Circuit test: V1(5V DC + 3V AC) → R1(100Ω) → D1(2.2V) → R2(100Ω) → GND
    print("Circuit: V1(5V DC + 3V AC) → R1(100Ω) → D1(2.2V) → R2(100Ω) → GND")
    print()
    
    # Estimation du courant DC (approximation)
    # Pour ce circuit, Id_dc ≈ 0.019 A (calculé précédemment)
    Id_dc = 0.019  # A
    
    print(f"Courant DC estimé: Id_dc = {Id_dc} A = {Id_dc*1000:.1f} mA")
    print()
    
    # Comparaison n = 1.0 vs n = 2.0
    n_values = [1.0, 2.0]
    
    print("Facteur n | rd (Ω)  | V3_ac (V) | Atténuation")
    print("----------|---------|-----------|------------")
    
    for n in n_values:
        # Calcul de rd
        rd = (n * VT) / Id_dc
        
        # Calcul AC: V3_ac = V1_ac * R2 / (R1 + rd + R2)
        V1_ac = 3.0  # V
        R1 = R2 = 100.0  # Ω
        V3_ac = V1_ac * R2 / (R1 + rd + R2)
        attenuation = V3_ac / V1_ac
        
        print(f"{n:8.1f} | {rd:6.3f} | {V3_ac:8.6f} | {attenuation:.3f}")
    
    print()
    
    # Calcul détaillé pour n = 2.0
    n = 2.0
    rd = (n * VT) / Id_dc
    R_total = 100 + rd + 100
    V3_ac = 3.0 * 100 / R_total
    
    print("Calcul détaillé avec n = 2.0:")
    print(f"  rd = n * VT / Id_dc = {n} * {VT} / {Id_dc} = {rd:.3f} Ω")
    print(f"  R_total = R1 + rd + R2 = 100 + {rd:.3f} + 100 = {R_total:.3f} Ω")
    print(f"  V3_ac = V1_ac * R2 / R_total = 3.0 * 100 / {R_total:.3f} = {V3_ac:.6f} V")
    print()
    
    # Comparaison avec l'ancienne méthode (rd = 10Ω fixe)
    rd_old = 10.0
    R_total_old = 100 + rd_old + 100
    V3_ac_old = 3.0 * 100 / R_total_old
    
    print("Comparaison avec l'ancienne méthode:")
    print(f"  Ancienne (rd = 10Ω): V3_ac = {V3_ac_old:.6f} V")
    print(f"  Nouvelle (n = 2.0):  V3_ac = {V3_ac:.6f} V")
    print(f"  Différence: {abs(V3_ac - V3_ac_old):.6f} V")
    print(f"  Plus d'atténuation: {V3_ac < V3_ac_old}")
    print()
    
    print("✓ Facteur d'idéalité n = 2.0 implémenté")
    print("✓ rd doublée par rapport à n = 1.0")
    print("✓ Plus d'atténuation du signal AC")
    print("✓ Comportement plus réaliste pour diodes réelles")

def test_different_n_values():
    print("\n=== Test avec différentes valeurs de n ===")
    
    VT = 0.026  # V
    Id_dc = 0.019  # A
    V1_ac = 3.0  # V
    R1 = R2 = 100.0  # Ω
    
    n_values = [1.0, 1.2, 1.5, 2.0]
    
    print("n     | rd (Ω)  | V3_ac (V) | Atténuation | Type de diode")
    print("------|---------|-----------|-------------|---------------")
    
    for n in n_values:
        rd = (n * VT) / Id_dc
        V3_ac = V1_ac * R2 / (R1 + rd + R2)
        attenuation = V3_ac / V1_ac
        
        if n == 1.0:
            diode_type = "Idéale"
        elif n <= 1.2:
            diode_type = "Schottky"
        elif n <= 1.5:
            diode_type = "Silicium"
        else:
            diode_type = "Réelle/LED"
        
        print(f"{n:4.1f} | {rd:6.3f} | {V3_ac:8.6f} | {attenuation:10.3f} | {diode_type}")
    
    print()
    print("Observation: Plus n est élevé, plus rd est élevée,")
    print("donc plus d'atténuation du signal AC.")

if __name__ == "__main__":
    test_n_factor_impact()
    test_different_n_values()
