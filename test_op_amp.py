#!/usr/bin/env python3

# Test script pour vérifier l'amplificateur opérationnel

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

# Exemple de netlist avec un amplificateur opérationnel en configuration non-inverseuse
netlist_example = """V1 1 0 DC 0 AC 1
R1 1 2 1k
R2 2 0 1k
OP1 0 2 3 1000000
R3 3 4 10k
R4 4 0 1k"""

print("Test de la netlist avec amplificateur opérationnel:")
print(netlist_example)
print("\n" + "="*50 + "\n")

# Test de la fonction solve_circuit
try:
    solve_circuit(netlist_example, frequency_hz=1.0, do_transfer_function=False, laplace_domain=True)
    print("Test réussi!")
except Exception as e:
    print(f"Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
