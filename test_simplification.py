#!/usr/bin/env python3
"""
Test de la fonctionnalité de simplification avancée
"""

import numpy as np
import sympy
from bode_H1_final import AdvancedSimplifier, evaluate_fast, extract_variables_from_expression

def test_simplification():
    """Test de la simplification sur l'expression FTC.txt"""
    
    # Lire l'expression de transfert
    with open("FTC.txt", 'r') as f:
        transfer_function = f.read().strip()
    
    print("=== TEST DE SIMPLIFICATION AVANCÉE ===")
    print(f"Expression originale: {len(transfer_function)} caractères")
    print(f"Expression: {transfer_function[:100]}...")
    
    # Extraire les variables
    variables = extract_variables_from_expression(transfer_function)
    print(f"Variables détectées: {variables}")
    
    # Valeurs de base et plages
    base_values = {
        'C1': 1e-07, 'C2': 1e-05, 'C3': 1e-07,
        'R1': 100.0, 'R2': 10000.0, 'R3': 10000.0, 'R4': 1000.0, 'R5': 1000.0,
        'L1': 1e-06
    }
    
    slider_ranges = {}
    for var in variables:
        if var.startswith('C'):
            slider_ranges[var] = (1e-12, 1e-3)
        elif var.startswith('R'):
            slider_ranges[var] = (0.1, 10e6)
        elif var.startswith('L'):
            slider_ranges[var] = (1e-9, 100)
        else:
            slider_ranges[var] = (0.1, 1)
    
    # Créer le simplificateur
    simplifier = AdvancedSimplifier(transfer_function, variables, base_values, slider_ranges)
    
    # Fréquences de test
    frequencies = np.logspace(1, 6, 500)
    
    print("\n=== LANCEMENT DE LA SIMPLIFICATION ===")
    
    # Tester la simplification
    simplified_expr, error, reduction = simplifier.simplify_transfer_function(
        frequencies, target_error_db=5.0
    )
    
    print(f"\n=== RÉSULTATS ===")
    print(f"Expression originale: {len(transfer_function)} caractères")
    print(f"Expression simplifiée: {len(str(simplified_expr))} caractères")
    print(f"Réduction: {reduction:.1f}%")
    print(f"Erreur maximale: {error:.2f} dB")
    
    if reduction > 0:
        print(f"\nExpression simplifiée:")
        print(f"{simplified_expr}")
        
        # Test de validation sur quelques points
        print(f"\n=== VALIDATION ===")
        test_points = [
            {var: base_values[var] for var in variables},
            {var: base_values[var] * 2 for var in variables},
            {var: base_values[var] / 2 for var in variables}
        ]
        
        test_freqs = np.array([100, 1000, 10000, 100000])
        
        for i, point in enumerate(test_points):
            print(f"\nPoint de test {i+1}:")
            
            # Évaluer l'original
            mag_orig, phase_orig = evaluate_fast(transfer_function, test_freqs, point, variables)
            
            # Évaluer la simplifiée
            mag_simp, phase_simp = evaluate_fast(str(simplified_expr), test_freqs, point, variables)
            
            # Calculer les erreurs
            db_orig = 20 * np.log10(np.maximum(mag_orig, 1e-12))
            db_simp = 20 * np.log10(np.maximum(mag_simp, 1e-12))
            error_db = np.abs(db_orig - db_simp)
            
            print(f"  Erreurs (dB): {error_db}")
            print(f"  Erreur max: {np.max(error_db):.2f} dB")
    else:
        print("Aucune simplification trouvée")

def test_individual_methods():
    """Test des méthodes individuelles de simplification"""
    
    # Expression de test simple
    test_expr = "s^3 + 2*s^2 + 3*s + 1"
    variables = ['s']
    base_values = {'s': 1}
    slider_ranges = {'s': (0.1, 10)}
    
    simplifier = AdvancedSimplifier(test_expr, variables, base_values, slider_ranges)
    
    print("\n=== TEST DES MÉTHODES INDIVIDUELLES ===")
    
    # Test de comptage de termes
    expr = sympy.sympify(test_expr)
    terms = simplifier._count_terms(expr)
    print(f"Expression: {test_expr}")
    print(f"Nombre de termes: {terms}")
    
    # Test de factorisation
    factored = simplifier._advanced_factorization(expr)
    print(f"Factorisée: {factored}")
    
    # Test de simplification par magnitude
    frequencies = np.logspace(1, 3, 100)
    simplified = simplifier._simplify_by_magnitude(expr, frequencies)
    print(f"Simplifiée par magnitude: {simplified}")

if __name__ == "__main__":
    try:
        test_simplification()
        test_individual_methods()
    except Exception as e:
        print(f"Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
