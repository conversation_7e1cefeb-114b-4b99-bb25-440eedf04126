# Suppression de la section "--- Valeurs numeriques retenues pour Q1 ---"

## Objectif
Supprimer l'affichage de la section "--- Valeurs numeriques retenues pour Q1 ---" qui n'est plus nécessaire.

## Modification apportée

### Section supprimée (lignes 1116-1123)
**Code supprimé :**
```python
file_print(f"--- Valeurs numeriques retenues pour {name_q} ---")
vbe_on_val = values.get(f'Vbe_on_{name_q}', '0.7')
file_print(f"  Vbe_on_{name_q} = {vbe_on_val}")
file_print(f"  beta_{name_q} = {beta_val}")
file_print(f"  Cbc_{name_q} = {values[f'Cbc_{name_q}']}")
file_print(f"  ro_{name_q} = {ro_ac_val}")
file_print(f"  rpi_{name_q} = {rpi_ac_val}")
file_print(f"  Cbe_{name_q} = {values[f'Cbe_{name_q}']}", end='\n\n')
```

### Contexte
La section était située dans la boucle d'affichage des paramètres BJT, après l'affichage des paramètres AC détaillés et avant le début de l'analyse DC symbolique.

### Avant la modification
```
--- Parametres pour Q1 AC ---
  ro_Q1 (Resistance de sortie AC) = ... (Valeur numerique: ... Ohm)
  gm_Q1 (Transconductance AC) = ... (Valeur numerique: ... S)
  rpi_Q1 (Resistance d'entree Base-Émetteur AC) = ... (Valeur numerique: ... Ohm)
  Note: rpi_Q1 est utilise dans le modele AC via l'admittance Ybe = 1/rpi_Q1 + s*Cbe_Q1...

--- Valeurs numeriques retenues pour Q1 ---
  Vbe_on_Q1 = 0.7
  beta_Q1 = 100
  Cbc_Q1 = 0
  ro_Q1 = 1000
  rpi_Q1 = 833.333
  Cbe_Q1 = 0

===== DC Analysis (Symbolic) =====
```

### Après la modification
```
--- Parametres pour Q1 AC ---
  ro_Q1 (Resistance de sortie AC) = ... (Valeur numerique: ... Ohm)
  gm_Q1 (Transconductance AC) = ... (Valeur numerique: ... S)
  rpi_Q1 (Resistance d'entree Base-Émetteur AC) = ... (Valeur numerique: ... Ohm)
  Note: rpi_Q1 est utilise dans le modele AC via l'admittance Ybe = 1/rpi_Q1 + s*Cbe_Q1...

===== DC Analysis (Symbolic) =====
```

## Résultat
- La section redondante "--- Valeurs numeriques retenues pour Q1 ---" n'est plus affichée
- L'affichage est plus concis et évite la duplication d'informations
- Les valeurs numériques sont toujours disponibles dans les sections précédentes et dans les nouvelles sections de substitutions ajoutées précédemment
- Le flux d'affichage passe directement des paramètres BJT AC à l'analyse DC symbolique

## Impact
- **Aucun impact fonctionnel** : toutes les valeurs sont toujours calculées et utilisées
- **Amélioration de la lisibilité** : suppression d'informations redondantes
- **Cohérence** : les substitutions numériques sont maintenant affichées de manière plus détaillée dans les nouvelles sections ajoutées précédemment

## Compatibilité
- Modification rétrocompatible
- Aucune fonctionnalité supprimée, seulement l'affichage redondant
- Toutes les valeurs numériques restent accessibles et utilisées dans les calculs
