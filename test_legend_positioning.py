#!/usr/bin/env python3
"""
Test script pour vérifier le positionnement des légendes
"""

import sys
import os
import numpy as np
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

# Ajouter le répertoire courant au path pour importer bode_H1_final
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from bode_H1_final import BodeAnalyzer, extract_variables_from_expression
    print("✓ Import de BodeAnalyzer réussi")
except ImportError as e:
    print(f"✗ Erreur d'import: {e}")
    sys.exit(1)

def test_legend_positioning():
    """Test que les légendes sont bien positionnées à l'extérieur du graphique"""
    print("\n=== Test du positionnement des légendes ===")
    
    # Créer une application Qt
    app = QApplication(sys.argv)
    
    # Créer une expression de test simple
    transfer_function = "1/(1+s*R1*C1)"
    variables = ['R1', 'C1']
    values = {'R1': 1000.0, 'C1': 1e-6}
    frequency_params = {'f_min': 1.0, 'f_max': 1e6, 'num_points': 100}
    slider_ranges = {'R1': (100.0, 10000.0), 'C1': (1e-9, 1e-3)}
    
    try:
        # Créer l'analyseur de Bode
        analyzer = BodeAnalyzer(transfer_function, variables, values, frequency_params, slider_ranges)
        print("✓ Création de BodeAnalyzer réussie")
        
        # Vérifier que les légendes existent
        if hasattr(analyzer, 'magnitude_legend') and analyzer.magnitude_legend is not None:
            print("✓ Légende de magnitude créée")
            
            # Vérifier la configuration de la légende
            try:
                offset = analyzer.magnitude_legend.offset()
                print(f"✓ Offset de la légende de magnitude: {offset}")
            except:
                print("✗ Impossible de récupérer l'offset de la légende de magnitude")
                
        else:
            print("✗ Légende de magnitude manquante")
            
        if hasattr(analyzer, 'phase_legend') and analyzer.phase_legend is not None:
            print("✓ Légende de phase créée")
            
            # Vérifier la configuration de la légende
            try:
                offset = analyzer.phase_legend.offset()
                print(f"✓ Offset de la légende de phase: {offset}")
            except:
                print("✗ Impossible de récupérer l'offset de la légende de phase")
        else:
            print("✗ Légende de phase manquante")
        
        # Vérifier que les courbes originales ont des noms
        if hasattr(analyzer.magnitude_curve, 'name') and analyzer.magnitude_curve.name():
            print(f"✓ Courbe de magnitude nommée: '{analyzer.magnitude_curve.name()}'")
        else:
            print("✗ Courbe de magnitude sans nom")
            
        if hasattr(analyzer.phase_curve, 'name') and analyzer.phase_curve.name():
            print(f"✓ Courbe de phase nommée: '{analyzer.phase_curve.name()}'")
        else:
            print("✗ Courbe de phase sans nom")
        
        # Ajouter une expression simplifiée pour tester les légendes multiples
        simplified_expr = "1/(1+s*1000*1e-6)"
        analyzer.add_simplified_curves(simplified_expr, values)
        print("✓ Expression simplifiée ajoutée")
        
        # Ajouter une expression manuelle pour tester les légendes multiples
        manual_expr = "0.8/(1+s*1200*1e-6)"
        analyzer.add_manual_curves(manual_expr, values)
        print("✓ Expression manuelle ajoutée")
        
        # Vérifier que toutes les courbes ont des noms dans les légendes
        print("\n=== Vérification des éléments de légende ===")
        
        # Compter les éléments dans les légendes
        try:
            mag_legend_items = len(analyzer.magnitude_legend.items)
            phase_legend_items = len(analyzer.phase_legend.items)
            print(f"✓ Légende magnitude: {mag_legend_items} éléments")
            print(f"✓ Légende phase: {phase_legend_items} éléments")
            
            # Lister les noms des éléments
            for i, item in enumerate(analyzer.magnitude_legend.items):
                if hasattr(item, 'text') and hasattr(item.text, 'toPlainText'):
                    name = item.text.toPlainText()
                    print(f"  - Élément {i+1}: '{name}'")
                    
        except Exception as e:
            print(f"✗ Erreur lors de la vérification des éléments de légende: {e}")
        
        # Afficher la fenêtre brièvement pour test visuel
        analyzer.show()
        
        # Fermer automatiquement après 3 secondes
        def close_app():
            analyzer.close()
            app.quit()
            
        QTimer.singleShot(3000, close_app)
        
        print("\n✓ Test terminé avec succès")
        print("La fenêtre va s'afficher pendant 3 secondes pour vérification visuelle...")
        
        # Lancer l'application
        app.exec_()
        
    except Exception as e:
        print(f"✗ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True

if __name__ == "__main__":
    success = test_legend_positioning()
    if success:
        print("\n🎉 Tous les tests sont passés!")
    else:
        print("\n❌ Certains tests ont échoué.")
        sys.exit(1)
