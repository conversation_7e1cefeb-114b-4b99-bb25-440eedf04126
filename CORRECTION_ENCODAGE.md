# Correction du problème d'encodage Unicode

## Problème rencontré

```
UnicodeEncodeError: 'charmap' codec can't encode character '\u03a9' in position 41: character maps to <undefined>
```

Le caractère Unicode Ω (oméga, U+03A9) ne peut pas être encodé en CP1252 (encodage Windows par défaut).

## Cause

Les messages d'affichage des diodes utilisaient le symbole Ω :
```python
file_print(f"Diode {name_d}: Id_dc = {Id_dc_numeric:.6f} A, rd = {rd_calculated:.3f} Ω (calculée)")
```

## Solution appliquée

Remplacement de tous les caractères Unicode problématiques :

### Avant (avec erreur)
```python
file_print(f"Diode {name_d}: Id_dc = {Id_dc_numeric:.6f} A, rd = {rd_calculated:.3f} Ω (calculée)")
file_print(f"Diode {name_d}: Diode bloquée, rd = 1MΩ (par défaut)")
file_print(f"Diode {name_d}: Erreur calcul rd, utilisation valeur par défaut 10Ω")
```

### Après (corrigé)
```python
file_print(f"Diode {name_d}: Id_dc = {Id_dc_numeric:.6f} A, rd = {rd_calculated:.3f} Ohm (calculee)")
file_print(f"Diode {name_d}: Diode bloquee, rd = 1MOhm (par defaut)")
file_print(f"Diode {name_d}: Erreur calcul rd, utilisation valeur par defaut 10Ohm")
```

## Changements effectués

1. **Ω → Ohm** : Remplacement du symbole oméga par le texte "Ohm"
2. **é → e** : Suppression des accents (calculée → calculee, bloquée → bloquee)
3. **Cohérence** : Application à tous les messages liés aux diodes

## Résultat

✅ Plus d'erreur d'encodage Unicode  
✅ Affichage correct sur tous les systèmes Windows  
✅ Fonctionnalité préservée  
✅ Lisibilité maintenue  

## Messages corrigés

- Calcul de résistance dynamique
- Diode bloquée
- Erreur de calcul
- Affichage des paramètres
- Notes explicatives

Le simulateur fonctionne maintenant sans problème d'encodage sur Windows.
