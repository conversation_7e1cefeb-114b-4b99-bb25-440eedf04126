
import sys
import os
sys.path.append('.')

# Rediriger la sortie vers un fichier
with open('Test3_NoBJT_output.txt', 'w', encoding='utf-8') as f:
    original_stdout = sys.stdout
    sys.stdout = f
    
    try:
        from test import solve_circuit
        
        netlist_str = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
        
        print("=== Test3_NoBJT ===")
        print("Circuit:")
        print(netlist_str)
        print()
        
        # Paramètres de test
        frequency_hz = 1000.0
        do_transfer_function = True
        input_node = "1"
        output_node = "7"
        freq_min = 1.0
        freq_max = 1e6
        
        solve_circuit(netlist_str, frequency_hz, do_transfer_function, 
                     input_node, output_node, freq_min, freq_max, laplace_domain=True)
        
    except Exception as e:
        print(f"ERREUR: {e}")
        import traceback
        traceback.print_exc()
    finally:
        sys.stdout = original_stdout

print("Test3_NoBJT terminé")
