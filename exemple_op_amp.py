#!/usr/bin/env python3

# Exemple d'utilisation de l'amplificateur opérationnel

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

print("=== Exemples d'utilisation de l'amplificateur opérationnel ===\n")

# Exemple 1: Amplificateur non-inverseur
print("1. Amplificateur non-inverseur (gain = 1 + R2/R1)")
netlist_non_inv = """V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 0 2 3 1000000
R2 3 0 10k
R3 3 4 1k"""

print("Netlist:")
print(netlist_non_inv)
print("\nRésultats:")
solve_circuit(netlist_non_inv, frequency_hz=1.0, do_transfer_function=False, laplace_domain=True)

print("\n" + "="*60 + "\n")

# Exemple 2: Amplificateur inverseur
print("2. Amplificateur inverseur (gain = -R2/R1)")
netlist_inv = """V1 1 0 DC 1 AC 1
R1 1 2 1k
R2 2 3 10k
OP1 0 0 3 1000000
R3 3 4 1k"""

print("Netlist:")
print(netlist_inv)
print("\nRésultats:")
solve_circuit(netlist_inv, frequency_hz=1.0, do_transfer_function=False, laplace_domain=True)

print("\n" + "="*60 + "\n")

# Exemple 3: Suiveur de tension (buffer)
print("3. Suiveur de tension (buffer, gain = 1)")
netlist_buffer = """V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 3 2 3 1000000
R2 3 4 1k"""

print("Netlist:")
print(netlist_buffer)
print("\nRésultats:")
solve_circuit(netlist_buffer, frequency_hz=1.0, do_transfer_function=False, laplace_domain=True)
