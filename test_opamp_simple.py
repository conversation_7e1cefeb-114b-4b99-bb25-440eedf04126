#!/usr/bin/env python3
"""
Test simple pour vérifier que les fréquences de coupure sont bien substituées
"""

# Simuler un test simple sans exécuter le programme complet
def test_substitution():
    """Test de substitution des valeurs d'amplificateur opérationnel"""
    
    # Valeurs typiques d'un amplificateur opérationnel
    values = {
        'Av_X1': '100000',      # 100k gain
        'Fc1_X1': '10',         # 10 Hz
        'Fc2_X1': '1000000',    # 1 MHz
        'Fc3_X1': '10000000',   # 10 MHz
        'Fc4_X1': '100000000',  # 100 MHz
        'R1': '1000',           # 1k ohm
        'R2': '1000',           # 1k ohm
        'V1': '1'               # 1V
    }
    
    # Expression typique d'une fonction de transfert avec amplificateur opérationnel
    # Ceci simule ce qui serait généré par l'analyse AC
    transfer_function = "Av_X1 / ((1 + s/(2*3.14159265359*Fc1_X1)) * (1 + s/(2*3.14159265359*Fc2_X1)) * (1 + s/(2*3.14159265359*Fc3_X1)) * (1 + s/(2*3.14159265359*Fc4_X1)))"
    
    print("=== Test de substitution des valeurs d'amplificateur opérationnel ===")
    print(f"Fonction de transfert originale:")
    print(f"H(s) = {transfer_function}")
    print()
    
    print("Valeurs à substituer:")
    for key, value in values.items():
        if key.startswith(('Av_', 'Fc1_', 'Fc2_', 'Fc3_', 'Fc4_')):
            print(f"  {key} = {value}")
    print()
    
    # Simulation de la substitution (sans utiliser xcas)
    tf_substituted = transfer_function
    for var_name, var_value in values.items():
        tf_substituted = tf_substituted.replace(var_name, var_value)
    
    print("Fonction de transfert après substitution:")
    print(f"H(s) = {tf_substituted}")
    print()
    
    # Vérification que toutes les variables Fc ont été remplacées
    remaining_fc_vars = []
    for var_name in values.keys():
        if var_name.startswith(('Fc1_', 'Fc2_', 'Fc3_', 'Fc4_')) and var_name in tf_substituted:
            remaining_fc_vars.append(var_name)
    
    if remaining_fc_vars:
        print(f"❌ ERREUR: Variables non substituées: {remaining_fc_vars}")
        return False
    else:
        print("✅ SUCCÈS: Toutes les variables Fc ont été substituées")
        return True

def test_priority_vars():
    """Test que les variables d'amplificateur opérationnel sont bien dans les variables prioritaires"""
    
    # Simulation de la logique de plot_bode
    values = {
        'ro_Q1': '1000',
        'beta_Q1': '100', 
        'Ic_ac_Q1': '0.001',
        'Av_X1': '100000',
        'Fc1_X1': '10',
        'Fc2_X1': '1000000',
        'Fc3_X1': '10000000',
        'Fc4_X1': '100000000',
        'R1': '1000',
        'comp_BJT_cap_C1': 'true'
    }
    
    # Logique de plot_bode modifiée
    priority_vars = [k for k in values.keys() if k.startswith('ro_') or k.startswith('beta_') or k.startswith('Ic_ac_') or k.startswith('Av_') or k.startswith('Fc1_') or k.startswith('Fc2_') or k.startswith('Fc3_') or k.startswith('Fc4_')]
    
    print("=== Test des variables prioritaires ===")
    print("Variables prioritaires détectées:")
    for var in priority_vars:
        print(f"  {var} = {values[var]}")
    print()
    
    # Vérification que toutes les variables d'amplificateur opérationnel sont prioritaires
    expected_opamp_vars = ['Av_X1', 'Fc1_X1', 'Fc2_X1', 'Fc3_X1', 'Fc4_X1']
    missing_vars = [var for var in expected_opamp_vars if var not in priority_vars]
    
    if missing_vars:
        print(f"❌ ERREUR: Variables d'amplificateur opérationnel manquantes: {missing_vars}")
        return False
    else:
        print("✅ SUCCÈS: Toutes les variables d'amplificateur opérationnel sont prioritaires")
        return True

if __name__ == "__main__":
    print("Tests de l'implémentation de l'amplificateur opérationnel")
    print("=" * 70)
    
    success = True
    success &= test_substitution()
    print()
    success &= test_priority_vars()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ Tous les tests ont réussi!")
        print("Les fréquences de coupure devraient maintenant être correctement substituées.")
    else:
        print("❌ Certains tests ont échoué.")
    
    print("\nNote: Ces tests simulent la logique de substitution.")
    print("Pour un test complet, utilisez le programme principal avec un circuit contenant un amplificateur opérationnel.")
