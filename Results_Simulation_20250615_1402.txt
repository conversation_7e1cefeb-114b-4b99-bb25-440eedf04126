--- Simulation Results - 2025-06-15 14:02:21 ---

Netlist originale:
V1 1 0 DC 5 AC 3
R1 1 2 100
D1 2 0 2.2

===== Diode D1 parameters =====

  Vth_D1 (Tension de seuil) = 2.2 V

  rd_D1 (Resistance dynamique) = 1e-3 Ohm



===== DC Analysis (Symbolic) =====

V1 = 5

V2 = Vth_D1

I_V1 = (Vth_D1-5)/R1

I_R1 = (-Vth_D1+5)/R1

I_D1 = (Vth_D1-5)/R1

===== DC Analysis (Numerical) =====

V1 = 5.0

V2 = 2.2

I_V1 = -0.028

I_R1 = 0.028

I_D1 = -0.028

===== AC Analysis (Symbolic) =====

V1 = 3

V2 = 3*rd_D1/(R1+rd_D1)

I_V1 = -3/(R1+rd_D1)

I_R1 = 3/(R1+rd_D1)

I_D1 = 3/(R1+rd_D1)

===== AC Analysis (Numerical) =====

V1 = 3.0

V2 = 2.9999700003e-05

I_V1 = -0.029999700003

I_R1 = 0.029999700003

I_D1 = 0.029999700003

===== AC Analysis (Temporal) - f = 1.0 Hz =====

v1(t) = 3.0*cos(6.283185307179586*t + 0.0)

v2(t) = 2.9999700003e-05*cos(6.283185307179586*t + 0.0)

i_V1(t) = 0.029999700003*cos(6.283185307179586*t + 3.14159265359)

i_R1(t) = 0.029999700003*cos(6.283185307179586*t + 0.0)

i_D1(t) = 0.029999700003*cos(6.283185307179586*t + 0.0)

===== Fonction de Transfert 1 =====

H1(s) = V2/V1 = rd_D1/(R1+rd_D1)

Fonction de transfert finale: 9.999900001e-06

Substitutions numeriques appliquees:

  R1 = 100.0

  rd_D1 = 0.001

