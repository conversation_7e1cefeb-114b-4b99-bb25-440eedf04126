#!/usr/bin/env python3
"""
Démonstration de la fonctionnalité d'ajout de termes dominants
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bode_H1_final import *

def demo_add_functionality():
    """Démonstration de la fonctionnalité d'ajout"""
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    
    try:
        # Lire la fonction de transfert de test
        if os.path.exists("FTC_test.txt"):
            transfer_function = read_transfer_function("FTC_test.txt")
            print(f"Fonction de transfert chargée: {transfer_function}")
        else:
            transfer_function = "1/(R1*C1*s + 1) + R2*gm_Q1/(R3*C2*s + R4*C3*s + 1)"
            print(f"Fonction de transfert par défaut: {transfer_function}")
        
        # Extraire les variables
        variables = extract_variables_from_expression(transfer_function)
        print(f"Variables détectées: {variables}")
        
        # Valeurs par défaut pour les composants
        default_values = {
            'R1': 1000.0, 'C1': 1e-6, 'R2': 2000.0, 'gm_Q1': 0.1,
            'R3': 1500.0, 'C2': 2e-6, 'R4': 500.0, 'C3': 5e-7
        }
        
        # Utiliser les valeurs par défaut pour les variables présentes
        values = {}
        slider_ranges = {}
        for var in variables:
            if var in default_values:
                values[var] = default_values[var]
            else:
                values[var] = 1.0
            
            # Définir les plages des sliders
            if var.startswith('R'):
                slider_ranges[var] = (100.0, 10000.0)
            elif var.startswith('C'):
                slider_ranges[var] = (1e-9, 1e-3)
            elif var.startswith('gm'):
                slider_ranges[var] = (0.01, 1.0)
            else:
                slider_ranges[var] = (0.1, 10.0)
        
        # Paramètres de fréquence
        freq_params = {
            'f_min': 1.0,
            'f_max': 1e6,
            'num_points': 500
        }
        
        print(f"Valeurs des composants: {values}")
        print(f"Plages des sliders: {slider_ranges}")
        
        # Créer l'analyseur de Bode
        analyzer = BodeAnalyzer(transfer_function, variables, values, freq_params, slider_ranges)
        analyzer.show()
        
        # Message d'information
        QMessageBox.information(None, "Démonstration", 
                              "Analyseur de Bode lancé avec la fonctionnalité d'ajout de termes.\n\n"
                              "Pour tester:\n"
                              "1. Cliquez sur 'Analyse de sensibilité'\n"
                              "2. Simplifiez la fonction de transfert\n"
                              "3. Affichez le Bode simplifié\n"
                              "4. Utilisez les boutons 'Add' pour ajouter des termes dominants\n\n"
                              "Les boutons 'Add' seront activés après la simplification.")
        
        # Lancer l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        QMessageBox.critical(None, "Erreur", f"Erreur lors du lancement: {e}")
        print(f"Erreur détaillée: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_add_functionality()
