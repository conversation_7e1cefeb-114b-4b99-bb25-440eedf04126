--- Simulation Results - 2025-06-14 21:49:37 ---

Netlist originale:
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 100k 100G 100G 10G
R2 2 3 2k

===== Amplificateur operationnel X1 parameters =====

  Av_X1 (Gain en boucle ouverte) = 100000.0

  Mode IDEAL active - Frequences de coupure ignorees

  Gain constant sur toute la bande de frequence



===== DC Analysis (Symbolic) =====

V1 = 1

V2 = R2/(Av_X1*R1+R1+R2)

V3 = -Av_X1*R2/(Av_X1*R1+R1+R2)

I_V1 = (-Av_X1-1)/(Av_X1*R1+R1+R2)

I_R1 = (Av_X1+1)/(Av_X1*R1+R1+R2)

I_R2 = (Av_X1+1)/(Av_X1*R1+R1+R2)

===== DC Analysis (Numerical) =====

V1 = 1.0

V2 = 1.9999400018e-05

V3 = -1.9999400018

I_V1 = -0.0009999800006

I_R1 = 0.0009999800006

I_R2 = 0.0009999800006

===== AC Analysis (Symbolic) =====

V1 = 1

V2 = R2/(Av_X1*R1+R1+R2)

V3 = -Av_X1*R2/(Av_X1*R1+R1+R2)

I_V1 = (-Av_X1-1)/(Av_X1*R1+R1+R2)

I_R1 = (Av_X1+1)/(Av_X1*R1+R1+R2)

I_R2 = (Av_X1+1)/(Av_X1*R1+R1+R2)

===== AC Analysis (Numerical) =====

V1 = 1.0

V2 = 1.9999400018e-05

V3 = -1.9999400018

I_V1 = -0.0009999800006

I_R1 = 0.0009999800006

I_R2 = 0.0009999800006

===== AC Analysis (Temporal) - f = 1.0 Hz =====

v1(t) = 1.0*cos(6.283185307179586*t + 0.0)

v2(t) = 1.9999400018e-05*cos(6.283185307179586*t + 0.0)

v3(t) = 1.9999400018*cos(6.283185307179586*t + 3.14159265359)

i_V1(t) = 0.0009999800006*cos(6.283185307179586*t + 3.14159265359)

i_R1(t) = 0.0009999800006*cos(6.283185307179586*t + 0.0)

i_R2(t) = 0.0009999800006*cos(6.283185307179586*t + 0.0)

===== Fonction de Transfert 1 =====

H1(s) = V3/V1 = -Av_X1*R2/(Av_X1*R1+R1+R2)

Fonction de transfert finale: -1.9999400018

Substitutions numeriques appliquees:

  Av_X1 = 100000.0

  R1 = 1000.0

  R2 = 2000.0

