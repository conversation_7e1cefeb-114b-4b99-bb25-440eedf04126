# Fonctionnalité d'Ajout Intelligent de Termes Dominants

## Vue d'ensemble

Cette fonctionnalité avancée permet d'ajouter intelligemment des termes dominants à une fonction de transfert simplifiée, en optimisant la précision tout en préservant la cohérence mathématique de l'expression.

## Améliorations intelligentes

### Sélection optimale des termes
- **Évaluation de précision** : Chaque terme candidat est testé pour mesurer son amélioration réelle
- **Validation de cohérence** : Vérification mathématique avant acceptation du terme
- **Critères stricts** : Seuls les termes apportant une amélioration significative (>0.1 dB) sont acceptés
- **Prévention de dégradation** : Rejet des termes qui détériorent la précision globale

## Fonctionnement

### 1. Interface utilisateur

- **Boutons "Add"** : Un bouton "Add (0)" apparaît à droite de chaque slider de variable
- **État initial** : Les boutons sont désactivés (gris) jusqu'à ce qu'une simplification soit effectuée
- **Compteur** : Le nombre entre parenthèses indique combien de fois le bouton a été utilisé

### 2. Processus d'utilisation

1. **Analyse de sensibilité** : Lancez l'analyse pour identifier l'importance des variables
2. **Simplification** : Simplifiez la fonction de transfert avec un seuil d'erreur
3. **Affichage Bode simplifié** : Affichez les courbes de l'expression simplifiée
4. **Activation des boutons** : Les boutons "Add" s'activent automatiquement (orange)
5. **Ajout de termes** : Cliquez sur "Add" pour ajouter le terme le plus dominant de cette variable

### 3. Mécanisme d'ajout

#### Stockage des termes supprimés
- Lors de la simplification, tous les termes supprimés sont stockés par variable
- Les termes sont triés par ordre d'impact décroissant (les plus importants en premier)
- Chaque terme conserve ses informations : expression, impact (dB), partie (numérateur/dénominateur)

#### Ajout automatique
- Sélection automatique du terme le plus dominant disponible pour la variable
- Reconstruction de l'expression avec le terme ajouté
- Mise à jour immédiate des courbes de Bode
- Sauvegarde automatique avec nom incrémental

### 4. Sauvegarde automatique

Chaque ajout génère automatiquement un fichier :
```
FTC_simplified_add_{variable}_{count}.txt
```

Exemple :
- `FTC_simplified_add_R1_1.txt` : Premier ajout pour R1
- `FTC_simplified_add_C2_3.txt` : Troisième ajout pour C2

### 5. Gestion des erreurs

- **Acceptation d'erreurs élevées** : L'ajout se fait même si cela dépasse le seuil d'erreur initial
- **Épuisement des termes** : Le bouton se désactive quand plus aucun terme n'est disponible
- **Réutilisation** : Possibilité d'ajouter plusieurs termes pour la même variable

## Implémentation technique

### Classes modifiées

#### `BodeAnalyzer`
- **Nouveaux attributs** :
  - `removed_terms_by_variable` : Dictionnaire des termes supprimés par variable
  - `add_buttons` : Dictionnaire des boutons Add
  - `add_counters` : Compteurs d'utilisation

- **Nouvelles méthodes** :
  - `add_dominant_term(variable)` : Ajoute le terme le plus dominant
  - `update_simplified_curves()` : Met à jour les courbes simplifiées
  - `save_modified_expression(variable, count)` : Sauvegarde automatique
  - `enable_add_buttons(removed_terms_by_variable)` : Active les boutons

#### `SensitivityResultsDialog`
- **Nouvel attribut** :
  - `removed_terms_by_variable` : Stockage des termes supprimés

- **Nouvelle méthode** :
  - `store_removed_term_by_variable(term, impact, part_name)` : Stocke les termes par variable

### Algorithme d'ajout

1. **Récupération** : Prendre le premier terme de la liste triée pour la variable
2. **Reconstruction** : Ajouter le terme au numérateur ou dénominateur selon son origine
3. **Simplification** : Simplifier l'expression résultante avec SymPy
4. **Mise à jour** : Actualiser l'affichage et les compteurs
5. **Sauvegarde** : Écrire automatiquement le fichier

## Avantages

- **Contrôle progressif** : Ajout terme par terme selon les besoins
- **Automatisation** : Sélection automatique des termes les plus importants
- **Traçabilité** : Sauvegarde automatique de chaque étape
- **Flexibilité** : Possibilité d'ajouter plusieurs termes par variable
- **Feedback visuel** : Compteurs et mise à jour immédiate des courbes

## Utilisation recommandée

1. Commencez par une simplification agressive (seuil d'erreur faible)
2. Observez les courbes simplifiées vs originales
3. Ajoutez progressivement des termes pour les variables critiques
4. Surveillez l'amélioration de la précision sur les courbes de Bode
5. Utilisez les fichiers sauvegardés pour comparer différentes versions

Cette fonctionnalité offre un équilibre optimal entre simplicité et précision, permettant un ajustement fin de la complexité de l'expression selon les besoins spécifiques de l'analyse.
