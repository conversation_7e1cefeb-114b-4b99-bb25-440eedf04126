import sys
import os
import numpy as np
import pyqtgraph as pg
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
                            QSlider, QLabel, QGridLayout, QDialog, QLineEdit, QPushButton,
                            QFormLayout, QDialogButtonBox, QMessageBox, QSpinBox, QDoubleSpinBox,
                            QTextEdit, QTabWidget, QProgressBar, QCheckBox, QInputDialog, QRadioButton,
                            QButtonGroup)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap
import sympy as sp
import re
from functools import lru_cache
import multiprocessing as mp
import time
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

def create_application_icon():
    """Crée une icône pour l'application"""
    # Créer une icône simple avec du code (si pas de fichier icône disponible)
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.white)

    # Dessiner une icône simple représentant un graphique de Bode
    from PyQt5.QtGui import QPainter, QPen, QBrush, QFont
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)

    # Fond bleu avec dégradé
    painter.setBrush(QBrush(Qt.blue))
    painter.setPen(QPen(Qt.darkBlue, 2))
    painter.drawRoundedRect(4, 4, 56, 56, 8, 8)

    # Dessiner des courbes représentant un diagramme de Bode
    painter.setPen(QPen(Qt.white, 3))
    # Courbe de magnitude (décroissante)
    painter.drawLine(12, 20, 25, 25)
    painter.drawLine(25, 25, 40, 35)
    painter.drawLine(40, 35, 52, 40)

    # Courbe de phase (en S)
    painter.setPen(QPen(Qt.yellow, 2))
    painter.drawLine(12, 45, 25, 42)
    painter.drawLine(25, 42, 40, 48)
    painter.drawLine(40, 48, 52, 52)

    # Axes
    painter.setPen(QPen(Qt.lightGray, 1))
    painter.drawLine(10, 15, 10, 55)  # Axe Y
    painter.drawLine(10, 55, 55, 55)  # Axe X

    # Ajouter "B" pour Bode
    painter.setPen(QPen(Qt.white, 1))
    font = QFont("Arial", 12, QFont.Bold)
    painter.setFont(font)
    painter.drawText(48, 18, "B")

    painter.end()

    # Sauvegarder l'icône pour utilisation future
    try:
        pixmap.save("bode_icon.png", "PNG")
        print("Icône sauvegardée: bode_icon.png")
    except:
        pass

    return QIcon(pixmap)

def set_application_icon(app, window=None):
    """Définit l'icône de l'application"""
    try:
        # Méthode 1: Essayer de charger un fichier icône s'il existe
        icon_files = ['icon.ico', 'icon.png', 'bode_icon.ico', 'bode_icon.png', 'app_icon.ico']
        icon_loaded = False

        for icon_file in icon_files:
            try:
                if os.path.exists(icon_file):
                    icon = QIcon(icon_file)
                    if not icon.isNull():
                        app.setWindowIcon(icon)
                        if window:
                            window.setWindowIcon(icon)
                        print(f"Icône chargée depuis: {icon_file}")
                        icon_loaded = True
                        break
            except:
                continue

        # Méthode 2: Si aucun fichier icône trouvé, utiliser l'icône générée
        if not icon_loaded:
            icon = create_application_icon()
            app.setWindowIcon(icon)
            if window:
                window.setWindowIcon(icon)
            print("Icône générée automatiquement (diagramme de Bode stylisé)")

    except Exception as e:
        print(f"Erreur lors du chargement de l'icône: {e}")

def extract_variables_from_expression(expression_str):
    variables = set()
    pattern = r'[a-zA-Z_][a-zA-Z0-9_]*'
    matches = re.findall(pattern, expression_str)
    for match in matches:
        if match not in ['s', 'j', 'I', 'E', 'pi', 'exp', 'sin', 'cos', 'tan', 'log', 'ln', 'sqrt']:
            variables.add(match)
    return sorted(list(variables))

def read_transfer_function(filename):
    with open(filename, 'r') as file:
        content = file.read().strip()
    return content

@lru_cache(maxsize=1000)
def create_fast_evaluator(expr_str, variables_tuple):
    variables = list(variables_tuple)
    s = sp.Symbol('s')
    symbols_dict = {var: sp.Symbol(var) for var in variables}
    symbols_dict['s'] = s

    expr = sp.sympify(expr_str, locals=symbols_dict)
    func_args = [s] + [symbols_dict[var] for var in variables]
    fast_func = sp.lambdify(func_args, expr, 'numpy')
    return fast_func

def evaluate_fast(expr_str, frequencies, values, variables):
    s_values = 2j * np.pi * frequencies
    var_values = [values[var] for var in variables]
    
    try:
        fast_func = create_fast_evaluator(expr_str, tuple(variables))
        result = fast_func(s_values, *var_values)
        magnitude = np.abs(result)
        phase = np.angle(result, deg=True)
        return magnitude, phase
    except:
        magnitude = np.full_like(frequencies, 1e-12)
        phase = np.zeros_like(frequencies)
        return magnitude, phase

class InputDialog(QDialog):
    def __init__(self, variables):
        super().__init__()
        self.setWindowTitle("Configuration des paramètres")
        self.setModal(True)

        self.variables = variables
        self.values = {}
        self.frequency_params = {}
        self.slider_ranges = {}

        layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # Titre centré pour les valeurs et plages des composants
        title_label_1 = QLabel("=== VALEURS ET PLAGES DES COMPOSANTS ===")
        title_label_1.setAlignment(Qt.AlignCenter)
        title_label_1.setStyleSheet("font-weight: bold; margin: 10px 0px;")
        form_layout.addRow(title_label_1)

        self.value_inputs = {}
        self.range_inputs = {}
        default_values = {
            'ro_Q1': 33333.3333333, 'R1': 100.0, 'C1': 1e-07, 'R2': 10000.0, 'R3': 10000.0,
            'Cbe_Q1': 1e-11, 'Cbc_Q1': 2e-12, 'R4': 1000.0, 'R5': 1000.0, 'C2': 1e-05,
            'C3': 1e-07, 'R6': 800.0, 'gm_Q1': 0.12, 'rpi_Q1': 833.333333333
        }

        for var in variables:
            row_layout = QHBoxLayout()

            input_field = QLineEdit()
            input_field.setFixedWidth(120)
            default_val = default_values.get(var, 1.0)
            input_field.setText(str(default_val))

            min_val, max_val = self.get_default_range(var)
            min_input = QLineEdit()
            min_input.setFixedWidth(80)
            min_input.setText(str(min_val))
            max_input = QLineEdit()
            max_input.setFixedWidth(80)
            max_input.setText(str(max_val))

            row_layout.addWidget(input_field)
            row_layout.addWidget(QLabel("Min:"))
            row_layout.addWidget(min_input)
            row_layout.addWidget(QLabel("Max:"))
            row_layout.addWidget(max_input)
            row_layout.addStretch()

            form_layout.addRow(f"{var}:", row_layout)
            self.value_inputs[var] = input_field
            self.range_inputs[var] = (min_input, max_input)
        
        # Titre centré pour la plage de fréquence
        title_label_2 = QLabel("=== PLAGE DE FRÉQUENCE ===")
        title_label_2.setAlignment(Qt.AlignCenter)
        title_label_2.setStyleSheet("font-weight: bold; margin: 10px 0px;")
        form_layout.addRow(title_label_2)
        
        self.freq_min = QDoubleSpinBox()
        self.freq_min.setRange(0.001, 1e12)
        self.freq_min.setValue(1.0)
        self.freq_min.setSuffix(" Hz")
        form_layout.addRow("Fréquence min:", self.freq_min)
        
        self.freq_max = QDoubleSpinBox()
        self.freq_max.setRange(0.001, 1e12)
        self.freq_max.setValue(1e6)
        self.freq_max.setSuffix(" Hz")
        form_layout.addRow("Fréquence max:", self.freq_max)
        
        self.num_points = QSpinBox()
        self.num_points.setRange(50, 5000)
        self.num_points.setValue(500)
        form_layout.addRow("Nombre de points:", self.num_points)

        layout.addLayout(form_layout)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        self.adjustSize()
        self.setMinimumWidth(500)

    def get_default_range(self, var):
        if var.startswith('C') and var[1:].isdigit():
            return 1e-12, 1e-3
        elif var.startswith('R') and var[1:].isdigit():
            return 0.1, 10e6
        elif var.startswith('L') and var[1:].isdigit():
            return 1e-9, 100
        else:
            return 0.1, 1

    def accept(self):
        try:
            for var, input_field in self.value_inputs.items():
                self.values[var] = float(input_field.text())

            for var, (min_input, max_input) in self.range_inputs.items():
                min_val = float(min_input.text())
                max_val = float(max_input.text())
                if min_val >= max_val:
                    QMessageBox.warning(self, "Erreur", f"Pour {var}: la valeur minimale doit être inférieure à la valeur maximale")
                    return
                self.slider_ranges[var] = (min_val, max_val)

            self.frequency_params = {
                'f_min': self.freq_min.value(),
                'f_max': self.freq_max.value(),
                'num_points': self.num_points.value()
            }

            if self.frequency_params['f_min'] >= self.frequency_params['f_max']:
                QMessageBox.warning(self, "Erreur", "La fréquence minimale doit être inférieure à la fréquence maximale")
                return

            super().accept()
        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez entrer des valeurs numériques valides")

def get_user_input(variables):
    dialog = InputDialog(variables)
    if dialog.exec_() == QDialog.Accepted:
        return dialog.values, dialog.frequency_params, dialog.slider_ranges
    else:
        return None, None, None

# ============================================================================
# FONCTIONS D'ANALYSE DE SENSIBILITÉ
# ============================================================================

def is_voltage_source(var_name):
    """Vérifie si une variable est une source de tension (commence par 'V')"""
    return var_name.startswith('V')

def filter_non_voltage_variables(component_values):
    """Filtre les variables pour exclure les sources de tension"""
    return {var: value for var, value in component_values.items() if not is_voltage_source(var)}

def calculate_bode_at_frequencies(expression_str, component_values, test_frequencies):
    """Calcule la réponse de Bode à des fréquences spécifiques"""
    try:
        s = sp.Symbol('s')
        expr = sp.sympify(expression_str)
        for var in sorted(component_values.keys(), key=len, reverse=True):
            if sp.symbols(var) in expr.free_symbols:
                expr = expr.subs(sp.symbols(var), component_values[var])
        H_func = sp.lambdify(s, expr, 'numpy')
        s_values = 1j * 2 * np.pi * test_frequencies
        H_jw = H_func(s_values)
        return 20 * np.log10(np.abs(H_jw)), np.angle(H_jw) * 180 / np.pi
    except:
        return None, None

def calculate_geometric_mean_magnitude_impact(mag_rel_change):
    """Calcule la moyenne géométrique des variations de magnitude"""
    non_zero_changes = mag_rel_change[mag_rel_change > 1e-12]

    if len(non_zero_changes) == 0:
        return 0.0

    try:
        geometric_mean = np.exp(np.mean(np.log(non_zero_changes)))
        return geometric_mean
    except:
        return np.mean(mag_rel_change)

def analyze_single_variable_worker(args):
    """
    Fonction worker pour analyser une seule variable en parallèle
    Args: tuple contenant (var_name, expression_str, component_values, test_frequencies, mag_ref, phase_ref)
    """
    var_name, expression_str, component_values, test_frequencies, mag_ref, phase_ref = args

    try:
        # Créer une copie des valeurs avec la variable doublée
        modified_values = component_values.copy()
        modified_values[var_name] = component_values[var_name] * 2.0  # +100%

        # Calculer les nouvelles valeurs
        mag_mod, phase_mod = calculate_bode_at_frequencies(expression_str, modified_values, test_frequencies)

        if mag_mod is None:
            return None

        # Calculer les variations
        mag_diff = mag_mod - mag_ref
        phase_diff = phase_mod - phase_ref

        # Calculer les variations relatives
        mag_rel_change = np.abs(mag_diff)
        phase_rel_change = np.abs(phase_diff)

        # Métriques d'impact
        max_mag_change = np.max(mag_rel_change)
        max_phase_change = np.max(phase_rel_change)
        avg_mag_change = np.mean(mag_rel_change)
        avg_phase_change = np.mean(phase_rel_change)

        # Score d'impact = moyenne géométrique des variations de magnitude
        geometric_mean_mag = calculate_geometric_mean_magnitude_impact(mag_rel_change)
        impact_score = geometric_mean_mag

        result = {
            'variable': var_name,
            'max_mag_change': max_mag_change,
            'avg_mag_change': avg_mag_change,
            'geometric_mean_mag': geometric_mean_mag,
            'max_phase_change': max_phase_change,
            'avg_phase_change': avg_phase_change,
            'impact_score': impact_score,
            'original_value': component_values[var_name],
            'mag_diff': mag_diff,
            'phase_diff': phase_diff,
            'mag_mod': mag_mod,
            'phase_mod': phase_mod
        }

        return result

    except Exception as e:
        print(f"Erreur lors de l'analyse de {var_name}: {e}")
        return None

def analyze_variable_sensitivity_parallel(expression_str, component_values, num_processes=None):
    """
    Version parallélisée de l'analyse de sensibilité
    """
    # Déterminer le nombre de processus à utiliser
    if num_processes is None:
        num_processes = mp.cpu_count()  # Utiliser tous les cœurs disponibles

    print(f"Utilisation de {num_processes} processus pour l'analyse parallèle")

    # Fréquences de test
    test_frequencies = np.array([10, 100, 1e3, 1e4, 1e5, 1e6, 1e7])

    # Séparer les variables selon leur type
    non_voltage_vars = filter_non_voltage_variables(component_values)
    voltage_vars = {var: value for var, value in component_values.items() if is_voltage_source(var)}

    print(f"Variables à analyser (hors sources V) : {list(non_voltage_vars.keys())}")
    print(f"Sources de tension ignorées : {list(voltage_vars.keys())}")

    # Calcul des valeurs de référence
    print("\nCalcul des valeurs de référence...")
    start_time = time.time()
    mag_ref, phase_ref = calculate_bode_at_frequencies(expression_str, component_values, test_frequencies)
    ref_time = time.time() - start_time
    print(f"Temps de calcul de référence : {ref_time:.2f}s")

    if mag_ref is None:
        print("Erreur dans le calcul de référence")
        return None

    print(f"\nValeurs de référence :")
    print(f"{'Fréquence (Hz)':<12} {'Magnitude (dB)':<15} {'Phase (°)':<12}")
    print("-" * 40)
    for i, freq in enumerate(test_frequencies):
        print(f"{freq:<12.0e} {mag_ref[i]:<15.3f} {phase_ref[i]:<12.3f}")

    # Préparer les arguments pour le multiprocessing
    var_names = list(non_voltage_vars.keys())
    args_list = [(var_name, expression_str, component_values, test_frequencies, mag_ref, phase_ref)
                 for var_name in var_names]

    print(f"\n{'='*90}")
    print("ANALYSE DE SENSIBILITÉ PARALLÈLE - SCORE = MOYENNE GÉOMÉTRIQUE (HORS SOURCES V)")
    print(f"{'='*90}")
    print(f"Analyse de {len(var_names)} variables en parallèle...")

    # Lancer l'analyse en parallèle
    start_time = time.time()

    with mp.Pool(processes=num_processes) as pool:
        # Afficher la progression
        results = []
        for i, result in enumerate(pool.imap(analyze_single_variable_worker, args_list)):
            if result is not None:
                results.append(result)
                print(f"Analysé {i+1}/{len(var_names)}: {result['variable']} "
                      f"(Score: {result['impact_score']:.3f} dB)")
            else:
                print(f"Erreur pour la variable {i+1}/{len(var_names)}")

    parallel_time = time.time() - start_time
    print(f"\nTemps d'analyse parallèle : {parallel_time:.2f}s")
    print(f"Temps estimé en séquentiel : {ref_time * len(var_names):.2f}s")
    print(f"Accélération : {(ref_time * len(var_names)) / parallel_time:.1f}x")

    # Tri par ordre d'impact décroissant
    results.sort(key=lambda x: x['impact_score'], reverse=True)

    # Affichage des résultats détaillés
    print(f"\n{'='*100}")
    print("CLASSEMENT DES VARIABLES PAR MOYENNE GÉOMÉTRIQUE (HORS SOURCES V)")
    print(f"{'='*100}")

    print(f"{'Rang':<4} {'Variable':<12} {'Moy.Géom(dB)':<13} {'Max Mag':<9} {'Moy Arith':<11} {'Max Phase':<11} {'Valeur Orig.':<15}")
    print("-" * 100)

    for i, result in enumerate(results, 1):
        print(f"{i:<4} {result['variable']:<12} {result['impact_score']:<13.3f} "
              f"{result['max_mag_change']:<9.3f} {result['avg_mag_change']:<11.3f} "
              f"{result['max_phase_change']:<11.3f} {result['original_value']:<15.3e}")

    # Afficher les sources de tension pour information
    if voltage_vars:
        print(f"\n{'='*60}")
        print("SOURCES DE TENSION (NON ANALYSÉES)")
        print(f"{'='*60}")
        for var_name, value in voltage_vars.items():
            print(f"{var_name:<12} {value:<15.3e} (ignorée dans le calcul de score)")

    return results

class SensitivityResultsDialog(QDialog):
    def __init__(self, sensitivity_results, expression_str, component_values, bode_analyzer=None):
        super().__init__()
        self.sensitivity_results = sensitivity_results
        self.expression_str = expression_str
        self.component_values = component_values
        self.simplified_expression = None  # Stockera l'expression simplifiée
        self.manual_expression = None  # Stockera l'expression modifiée manuellement
        self.bode_analyzer = bode_analyzer  # Référence à la fenêtre BodeAnalyzer



        self.setWindowTitle("Résultats de l'analyse de sensibilité")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # Titre
        title_label = QLabel("Analyse de sensibilité des variables")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)

        # Zone de texte pour les résultats
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setFont(pg.QtGui.QFont("Courier", 10))
        layout.addWidget(self.results_text)

        # Boutons
        button_layout = QHBoxLayout()

        self.simplify_btn = QPushButton("Simplifier la fonction de transfert")
        self.simplify_btn.clicked.connect(self.simplify_transfer_function)
        button_layout.addWidget(self.simplify_btn)

        self.plot_btn = QPushButton("Afficher graphiques de sensibilité")
        self.plot_btn.clicked.connect(self.plot_sensitivity)
        button_layout.addWidget(self.plot_btn)

        close_btn = QPushButton("Fermer")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        self.populate_results()

    def populate_results(self):
        """Remplit la zone de texte avec les résultats"""
        text = "CLASSEMENT DES VARIABLES PAR MOYENNE GÉOMÉTRIQUE (HORS SOURCES V)\n"
        text += "=" * 80 + "\n\n"

        text += f"{'Rang':<4} {'Variable':<12} {'Moy.Géom(dB)':<13} {'Max Mag':<9} {'Moy Arith':<11} {'Max Phase':<11} {'Valeur Orig.':<15}\n"
        text += "-" * 80 + "\n"

        for i, result in enumerate(self.sensitivity_results, 1):
            text += f"{i:<4} {result['variable']:<12} {result['impact_score']:<13.3f} "
            text += f"{result['max_mag_change']:<9.3f} {result['avg_mag_change']:<11.3f} "
            text += f"{result['max_phase_change']:<11.3f} {result['original_value']:<15.3e}\n"

        # Afficher les sources de tension pour information
        voltage_vars = {var: value for var, value in self.component_values.items() if is_voltage_source(var)}
        if voltage_vars:
            text += "\n" + "=" * 60 + "\n"
            text += "SOURCES DE TENSION (NON ANALYSÉES)\n"
            text += "=" * 60 + "\n"
            for var_name, value in voltage_vars.items():
                text += f"{var_name:<12} {value:<15.3e} (ignorée dans le calcul de score)\n"

        self.results_text.setPlainText(text)

    def simplify_transfer_function(self):
        """Lance la simplification de la fonction de transfert et affiche automatiquement le Bode simplifié"""
        # Demander le seuil d'erreur
        error_input, ok = QInputDialog.getText(self, "Seuil d'erreur",
                                             "Seuil d'erreur maximum en dB:",
                                             text="5.0")
        if not ok:
            return

        try:
            max_error_db = float(error_input)
        except:
            QMessageBox.warning(self, "Erreur", "Veuillez entrer une valeur numérique valide")
            return

        # Lancer la simplification
        simplified_expr, removed_vars = self.simplify_symbolic(max_error_db)

        if simplified_expr and removed_vars:
            # Stocker l'expression simplifiée
            self.simplified_expression = simplified_expr
            # Afficher les résultats dans une nouvelle fenêtre
            self.show_simplification_results(simplified_expr, removed_vars)
            # Afficher automatiquement le Bode simplifié
            self.show_simplified_bode_automatically()
        elif simplified_expr:
            # Même si aucune variable n'a été supprimée, on peut quand même afficher l'expression
            self.simplified_expression = simplified_expr
            QMessageBox.information(self, "Simplification",
                                  "Aucune variable n'a pu être supprimée avec le seuil d'erreur spécifié.")
            # Afficher quand même le Bode (même si pas de simplification)
            self.show_simplified_bode_automatically()
        else:
            QMessageBox.warning(self, "Erreur", "Erreur lors de la simplification.")

    def simplify_symbolic(self, max_error_db=5.0):
        """Simplifie la fonction de transfert symboliquement avec validation robuste sur les plages slider"""
        print(f"Démarrage de la simplification intelligente améliorée avec seuil d'erreur: {max_error_db} dB")

        # Analyser l'expression pour identifier les termes
        original_expr = sp.sympify(self.expression_str)
        print(f"Expression originale analysée: {len(str(original_expr))} caractères")

        # Séparer numérateur et dénominateur
        if original_expr.is_Add:
            # Expression simple
            numerator = original_expr
            denominator = sp.Integer(1)
        else:
            # Fraction
            numerator = sp.numer(original_expr)
            denominator = sp.denom(original_expr)

        print(f"Numérateur: {len(str(numerator))} caractères")
        print(f"Dénominateur: {len(str(denominator))} caractères")

        # Générer les fréquences de test adaptatives basées sur la plage utilisateur
        if hasattr(self, 'bode_analyzer') and self.bode_analyzer:
            freq_min = self.bode_analyzer.frequencies[0]
            freq_max = self.bode_analyzer.frequencies[-1]
            test_frequencies = np.logspace(np.log10(freq_min), np.log10(freq_max), 100)
        else:
            test_frequencies = np.logspace(1, 7, 100)

        print(f"Test sur {len(test_frequencies)} fréquences de {test_frequencies[0]:.1e} à {test_frequencies[-1]:.1e} Hz")

        # Générer les jeux de valeurs de test pour validation robuste
        test_value_sets = self.generate_test_value_sets()
        print(f"Validation sur {len(test_value_sets)} jeux de valeurs de composants")

        # Calculer la réponse de référence avec les valeurs actuelles (méthode originale)
        mag_ref, phase_ref = calculate_bode_at_frequencies(self.expression_str, self.component_values, test_frequencies)
        if mag_ref is None:
            return None, None

        # Simplification améliorée: garder la méthode actuelle mais avec validation multi-valeurs
        simplified_num = self.simplify_polynomial_terms_enhanced(
            numerator, test_frequencies, mag_ref, max_error_db, "numérateur", test_value_sets
        )
        simplified_den = self.simplify_polynomial_terms_enhanced(
            denominator, test_frequencies, mag_ref, max_error_db, "dénominateur", test_value_sets
        )

        # Reconstruire l'expression
        if simplified_den == 1:
            simplified_expr = simplified_num
        else:
            simplified_expr = simplified_num / simplified_den

        # Simplifier l'expression finale
        try:
            simplified_expr = sp.simplify(simplified_expr)
            simplified_str = str(simplified_expr)
        except:
            simplified_str = str(simplified_expr)

        # Calculer les statistiques de simplification
        original_terms = self.count_terms_in_expression(original_expr)
        simplified_terms = self.count_terms_in_expression(simplified_expr)

        variables_info = []
        for result in self.sensitivity_results:
            var_name = result['variable']
            var_symbol = sp.symbols(var_name)
            original_present = var_symbol in original_expr.free_symbols
            simplified_present = var_symbol in simplified_expr.free_symbols

            if original_present:
                if simplified_present:
                    status = "Partiellement conservée"
                else:
                    status = "Supprimée"
                variables_info.append((var_name, result['original_value'], result['impact_score'], status))

        print(f"Simplification terminée:")
        print(f"  Termes originaux: {original_terms}")
        print(f"  Termes simplifiés: {simplified_terms}")
        print(f"  Réduction: {((original_terms - simplified_terms) / original_terms * 100):.1f}%")

        return simplified_str, variables_info

    def generate_test_value_sets(self):
        """Génère des jeux de valeurs de test couvrant les plages des sliders"""
        test_sets = []

        # Valeurs actuelles (référence)
        test_sets.append(self.component_values.copy())

        # Vérifier si on a accès aux plages des sliders
        if not (hasattr(self, 'bode_analyzer') and self.bode_analyzer and hasattr(self.bode_analyzer, 'slider_ranges')):
            # Utiliser des plages par défaut basées sur les valeurs actuelles
            print("  Utilisation de plages par défaut (±50% et ±90%)")
            for var_name, current_val in self.component_values.items():
                if not is_voltage_source(var_name):  # Ignorer les sources de tension
                    # Générer des variations de ±50% et ±90%
                    for factor in [0.1, 0.5, 1.5, 10.0]:
                        test_values = self.component_values.copy()
                        test_values[var_name] = current_val * factor
                        test_sets.append(test_values)
            return test_sets

        # Générer des variations systématiques avec les vraies plages
        for var_name in self.component_values.keys():
            if var_name in self.bode_analyzer.slider_ranges:
                min_val, max_val = self.bode_analyzer.slider_ranges[var_name]

                # Points de test: min, 25%, 75%, max, et quelques points intermédiaires
                if max_val / min_val > 100:  # Échelle logarithmique
                    log_min = np.log10(abs(min_val)) if min_val > 0 else np.log10(1e-15)
                    log_max = np.log10(abs(max_val)) if max_val > 0 else np.log10(1e-15)
                    test_points = np.logspace(log_min, log_max, 5)
                else:  # Échelle linéaire
                    test_points = np.linspace(min_val, max_val, 5)

                for test_val in test_points:
                    test_values = self.component_values.copy()
                    test_values[var_name] = test_val
                    test_sets.append(test_values)

        # Ajouter quelques combinaisons aléatoires
        import random
        for _ in range(10):
            test_values = self.component_values.copy()
            for var_name in self.component_values.keys():
                if var_name in self.bode_analyzer.slider_ranges:
                    min_val, max_val = self.bode_analyzer.slider_ranges[var_name]
                    if max_val / min_val > 100:
                        log_min = np.log10(abs(min_val)) if min_val > 0 else np.log10(1e-15)
                        log_max = np.log10(abs(max_val)) if max_val > 0 else np.log10(1e-15)
                        random_val = 10 ** random.uniform(log_min, log_max)
                    else:
                        random_val = random.uniform(min_val, max_val)
                    test_values[var_name] = random_val
            test_sets.append(test_values)

        return test_sets

    def simplify_polynomial_terms_enhanced(self, poly_expr, test_frequencies, mag_ref, max_error_db, part_name, test_value_sets):
        """Simplifie les termes d'un polynôme avec approche agressive + validation multi-valeurs"""
        print(f"\nSimplification agressive du {part_name} avec seuil d'erreur: {max_error_db:.3f} dB...")

        if poly_expr.is_Add:
            terms = poly_expr.args
        else:
            terms = [poly_expr]

        print(f"  Nombre de termes initial: {len(terms)}")

        # Évaluer l'importance de chaque terme
        term_importance = []
        for i, term in enumerate(terms):
            test_expr_without_term = poly_expr - term
            original_full = sp.sympify(self.expression_str)
            if part_name == "numérateur":
                test_full_expr = test_expr_without_term / sp.denom(original_full)
            else:
                test_full_expr = sp.numer(original_full) / test_expr_without_term

            impact = self.evaluate_term_impact(str(test_full_expr), test_frequencies, mag_ref)
            term_importance.append((i, term, impact))
            print(f"    Terme {i}: impact = {impact:.3f} dB")

        # Trier par impact croissant
        term_importance.sort(key=lambda x: x[2])

        # APPROCHE AGRESSIVE: Essayer de supprimer plusieurs termes à la fois
        best_terms = list(terms)
        best_error = 0
        removed_terms = []

        # Stratégie 1: Supprimer tous les termes avec impact < seuil/2
        low_impact_threshold = max_error_db / 2
        candidates_for_removal = [term for i, term, impact in term_importance if impact < low_impact_threshold]

        if candidates_for_removal:
            print(f"  Tentative de suppression groupée de {len(candidates_for_removal)} termes à faible impact...")
            test_terms = [t for t in terms if t not in candidates_for_removal]

            if test_terms:  # S'assurer qu'il reste au moins un terme
                test_poly = sum(test_terms) if len(test_terms) > 1 else test_terms[0]

                # Tester avec les valeurs actuelles
                original_full = sp.sympify(self.expression_str)
                if part_name == "numérateur":
                    test_full_expr = test_poly / sp.denom(original_full)
                else:
                    test_full_expr = sp.numer(original_full) / test_poly

                total_impact = self.evaluate_term_impact(str(test_full_expr), test_frequencies, mag_ref)
                print(f"    Suppression groupée -> erreur: {total_impact:.3f} dB")

                if total_impact <= max_error_db:
                    # Validation multi-valeurs avec seuil plus permissif
                    validation_passed = self.validate_simplification_across_ranges(
                        str(test_full_expr), test_value_sets, test_frequencies, max_error_db * 1.5
                    )

                    if validation_passed:
                        best_terms = test_terms
                        best_error = total_impact
                        removed_terms = [(term, 0, part_name) for term in candidates_for_removal]
                        print(f"    ✓ Suppression groupée réussie!")

        # Stratégie 2: Si la suppression groupée n'a pas marché, essayer terme par terme mais plus agressivement
        if not removed_terms:
            print(f"  Suppression terme par terme avec seuils adaptatifs...")
            current_terms = list(terms)

            for i, term, impact in term_importance:
                if len(current_terms) <= 1:
                    break

                test_terms = [t for t in current_terms if t != term]
                if not test_terms:
                    continue

                test_poly = sum(test_terms) if len(test_terms) > 1 else test_terms[0]
                original_full = sp.sympify(self.expression_str)
                if part_name == "numérateur":
                    test_full_expr = test_poly / sp.denom(original_full)
                else:
                    test_full_expr = sp.numer(original_full) / test_poly

                total_impact = self.evaluate_term_impact(str(test_full_expr), test_frequencies, mag_ref)

                # Seuil adaptatif: plus permissif pour les termes à faible impact
                adaptive_threshold = max_error_db * (1.0 + 0.5 * (1 - impact / max(1e-6, max_error_db)))

                if total_impact <= adaptive_threshold:
                    # Validation multi-valeurs plus permissive
                    validation_passed = self.validate_simplification_across_ranges(
                        str(test_full_expr), test_value_sets, test_frequencies, max_error_db * 2.0
                    )

                    if validation_passed:
                        current_terms = test_terms
                        removed_terms.append((term, impact, part_name))
                        print(f"    ✓ Terme supprimé (seuil adaptatif {adaptive_threshold:.3f} dB): {str(term)[:50]}...")
                    else:
                        print(f"    ✗ Terme conservé (échec validation): {str(term)[:50]}...")
                else:
                    print(f"    ✗ Terme conservé (erreur {total_impact:.3f} > {adaptive_threshold:.3f} dB): {str(term)[:50]}...")

            if removed_terms:
                best_terms = current_terms

        # Stratégie 3: Approximation par dominance de fréquence
        if len(best_terms) == len(terms):  # Si aucune simplification n'a marché
            print(f"  Tentative d'approximation par dominance de fréquence...")
            best_terms = self.approximate_by_frequency_dominance(terms, test_frequencies, mag_ref, max_error_db, part_name)

        result_poly = sum(best_terms) if len(best_terms) > 1 else best_terms[0]
        print(f"  {part_name} simplifié: {len(best_terms)}/{len(terms)} termes conservés")
        print(f"  Termes supprimés: {len(removed_terms)}")

        return result_poly

    def approximate_by_frequency_dominance(self, terms, test_frequencies, mag_ref, max_error_db, part_name):
        """Approximation par analyse de dominance de fréquence"""
        print(f"    Analyse de dominance de fréquence...")

        if len(terms) <= 2:
            return list(terms)  # Pas assez de termes pour simplifier

        # Analyser la contribution de chaque terme sur différentes bandes de fréquence
        freq_bands = [
            (test_frequencies[0], test_frequencies[len(test_frequencies)//4]),  # Basses fréquences
            (test_frequencies[len(test_frequencies)//4], test_frequencies[3*len(test_frequencies)//4]),  # Moyennes fréquences
            (test_frequencies[3*len(test_frequencies)//4], test_frequencies[-1])  # Hautes fréquences
        ]

        # Pour chaque bande, identifier les termes dominants
        dominant_terms = set()

        for band_name, (f_min, f_max) in zip(['BF', 'MF', 'HF'], freq_bands):
            band_freqs = test_frequencies[(test_frequencies >= f_min) & (test_frequencies <= f_max)]
            if len(band_freqs) == 0:
                continue

            # Tester l'impact de chaque terme dans cette bande
            term_impacts = []
            for term in terms:
                test_expr_without_term = sum([t for t in terms if t != term])
                if test_expr_without_term == 0:
                    continue

                original_full = sp.sympify(self.expression_str)
                if part_name == "numérateur":
                    test_full_expr = test_expr_without_term / sp.denom(original_full)
                else:
                    test_full_expr = sp.numer(original_full) / test_expr_without_term

                # Calculer l'impact sur cette bande de fréquence
                try:
                    mag_test, _ = calculate_bode_at_frequencies(str(test_full_expr), self.component_values, band_freqs)
                    if mag_test is not None:
                        mag_ref_band, _ = calculate_bode_at_frequencies(self.expression_str, self.component_values, band_freqs)
                        if mag_ref_band is not None:
                            impact = np.max(np.abs(mag_test - mag_ref_band))
                            term_impacts.append((term, impact))
                except:
                    term_impacts.append((term, float('inf')))

            # Garder les termes les plus importants dans cette bande
            term_impacts.sort(key=lambda x: x[1], reverse=True)
            n_keep = max(1, len(term_impacts) // 2)  # Garder au moins la moitié des termes
            for term, impact in term_impacts[:n_keep]:
                dominant_terms.add(term)

            print(f"      Bande {band_name}: {n_keep}/{len(term_impacts)} termes dominants identifiés")

        # S'assurer qu'on garde au moins quelques termes
        if len(dominant_terms) < max(1, len(terms) // 3):
            # Si trop peu de termes, garder les plus importants globalement
            global_impacts = []
            for term in terms:
                test_expr_without_term = sum([t for t in terms if t != term])
                if test_expr_without_term == 0:
                    global_impacts.append((term, float('inf')))
                    continue

                original_full = sp.sympify(self.expression_str)
                if part_name == "numérateur":
                    test_full_expr = test_expr_without_term / sp.denom(original_full)
                else:
                    test_full_expr = sp.numer(original_full) / test_expr_without_term

                impact = self.evaluate_term_impact(str(test_full_expr), test_frequencies, mag_ref)
                global_impacts.append((term, impact))

            global_impacts.sort(key=lambda x: x[1], reverse=True)
            n_keep_global = max(len(terms) // 3, 1)
            dominant_terms = set([term for term, impact in global_impacts[:n_keep_global]])

        result_terms = list(dominant_terms)
        print(f"    Approximation par dominance: {len(result_terms)}/{len(terms)} termes conservés")

        return result_terms

    def validate_simplification_across_ranges(self, simplified_expr_str, test_value_sets, test_frequencies, max_error_db):
        """Valide qu'une expression simplifiée reste acceptable sur toutes les plages de valeurs - VERSION PERMISSIVE"""
        validation_errors = []
        failed_validations = 0

        # Tester sur un échantillon réduit pour être plus permissif
        sample_size = min(5, len(test_value_sets))  # Moins de tests = plus permissif
        import random
        sampled_sets = random.sample(test_value_sets, sample_size)

        for test_values in sampled_sets:
            try:
                # Calculer la réponse de référence avec ces valeurs
                mag_ref_test, _ = calculate_bode_at_frequencies(self.expression_str, test_values, test_frequencies)
                if mag_ref_test is None:
                    continue

                # Calculer la réponse avec l'expression simplifiée
                mag_simplified, _ = calculate_bode_at_frequencies(simplified_expr_str, test_values, test_frequencies)
                if mag_simplified is None:
                    failed_validations += 1
                    continue  # Ne pas échouer immédiatement, juste compter

                # Calculer l'erreur
                error = np.max(np.abs(mag_simplified - mag_ref_test))
                validation_errors.append(error)

                # Seuil très permissif pour la validation multi-valeurs
                if error > max_error_db * 3.0:  # Tolérance très élevée
                    failed_validations += 1

            except:
                failed_validations += 1
                continue

        # Accepter si moins de 50% des validations échouent
        total_tests = len(sampled_sets)
        success_rate = (total_tests - failed_validations) / max(1, total_tests)

        if validation_errors:
            avg_error = np.mean(validation_errors)
            max_error = np.max(validation_errors)
            print(f"      Validation permissive: erreur moy = {avg_error:.3f} dB, erreur max = {max_error:.3f} dB, succès = {success_rate:.1%}")

            # Critères permissifs: soit erreur acceptable, soit taux de succès > 50%
            return (max_error <= max_error_db * 2.0) or (success_rate >= 0.5)

        # Si pas d'erreurs calculées mais taux de succès acceptable
        return success_rate >= 0.3  # Très permissif

    def simplify_polynomial_terms_multivalue(self, poly_expr, test_frequencies, reference_responses, max_error_db, part_name):
        """Simplifie les termes d'un polynôme avec validation sur plusieurs jeux de valeurs"""
        print(f"\nSimplification multi-valeurs du {part_name} avec seuil d'erreur: {max_error_db:.3f} dB...")

        if poly_expr.is_Add:
            terms = poly_expr.args
        else:
            terms = [poly_expr]

        print(f"  Nombre de termes initial: {len(terms)}")

        # Évaluer l'importance de chaque terme sur tous les jeux de valeurs
        term_importance = []

        for i, term in enumerate(terms):
            # Calculer l'impact moyen de la suppression de ce terme
            total_impact = 0
            valid_tests = 0

            for test_values, mag_ref, phase_ref in reference_responses:
                # Calculer l'impact de la suppression de ce terme
                test_expr_without_term = poly_expr - term

                # Reconstruire l'expression complète pour le test
                original_full = sp.sympify(self.expression_str)
                if part_name == "numérateur":
                    test_full_expr = test_expr_without_term / sp.denom(original_full)
                else:
                    test_full_expr = sp.numer(original_full) / test_expr_without_term

                # Tester l'impact pour ce jeu de valeurs
                impact = self.evaluate_term_impact_with_values(str(test_full_expr), test_frequencies, mag_ref, test_values)
                if impact != float('inf'):
                    total_impact += impact
                    valid_tests += 1

            avg_impact = total_impact / valid_tests if valid_tests > 0 else float('inf')
            term_importance.append((i, term, avg_impact))
            print(f"    Terme {i}: impact moyen = {avg_impact:.3f} dB (sur {valid_tests} tests)")

        # Trier par impact croissant (les moins importants en premier)
        term_importance.sort(key=lambda x: x[2])

        # Simplification progressive en respectant strictement le seuil sur TOUS les jeux de valeurs
        current_terms = list(terms)
        removed_terms = []

        for i, term, avg_impact in term_importance:
            if len(current_terms) <= 1:  # Garder au moins 1 terme
                print(f"    Arrêt: ne peut pas supprimer le dernier terme")
                break

            # Tester la suppression de ce terme sur TOUS les jeux de valeurs
            test_terms = [t for t in current_terms if t != term]
            if not test_terms:
                continue

            test_poly = sum(test_terms) if len(test_terms) > 1 else test_terms[0]

            # Vérifier que l'erreur reste acceptable sur TOUS les jeux de valeurs
            max_impact_across_all = 0
            all_valid = True

            for test_values, mag_ref, phase_ref in reference_responses:
                # Reconstruire et tester l'expression complète
                original_full = sp.sympify(self.expression_str)
                if part_name == "numérateur":
                    test_full_expr = test_poly / sp.denom(original_full)
                else:
                    test_full_expr = sp.numer(original_full) / test_poly

                impact = self.evaluate_term_impact_with_values(str(test_full_expr), test_frequencies, mag_ref, test_values)

                if impact == float('inf'):
                    all_valid = False
                    break

                max_impact_across_all = max(max_impact_across_all, impact)

            print(f"    Test suppression terme (impact moyen: {avg_impact:.3f} dB) -> erreur max: {max_impact_across_all:.3f} dB")

            # RESPECTER STRICTEMENT LE SEUIL D'ERREUR SUR TOUS LES JEUX DE VALEURS
            if all_valid and max_impact_across_all <= max_error_db:
                current_terms = test_terms
                removed_terms.append((term, avg_impact, part_name))
                print(f"    ✓ Terme supprimé (erreur max {max_impact_across_all:.3f} ≤ {max_error_db:.3f} dB): {str(term)[:50]}...")
            else:
                if not all_valid:
                    print(f"    ✗ Terme conservé (expression invalide): {str(term)[:50]}...")
                else:
                    print(f"    ✗ Terme conservé (erreur max {max_impact_across_all:.3f} > {max_error_db:.3f} dB): {str(term)[:50]}...")

        result_poly = sum(current_terms) if len(current_terms) > 1 else current_terms[0]
        print(f"  {part_name} simplifié: {len(current_terms)}/{len(terms)} termes conservés")
        print(f"  Termes supprimés: {len(removed_terms)}")

        return result_poly

    def evaluate_term_impact_with_values(self, test_expr_str, test_frequencies, mag_ref, test_values):
        """Évalue l'impact de la modification d'une expression avec des valeurs spécifiques"""
        try:
            mag_test, _ = calculate_bode_at_frequencies(test_expr_str, test_values, test_frequencies)
            if mag_test is not None:
                return np.max(np.abs(mag_test - mag_ref))
            else:
                return float('inf')  # Impact très élevé si l'expression est invalide
        except:
            return float('inf')

    def simplify_polynomial_terms(self, poly_expr, test_frequencies, mag_ref, max_error_db, part_name):
        """Simplifie les termes d'un polynôme en respectant le seuil d'erreur"""
        print(f"\nSimplification du {part_name} avec seuil d'erreur: {max_error_db:.3f} dB...")

        if poly_expr.is_Add:
            terms = poly_expr.args
        else:
            terms = [poly_expr]

        print(f"  Nombre de termes initial: {len(terms)}")

        # Évaluer l'importance de chaque terme
        term_importance = []

        for i, term in enumerate(terms):
            # Calculer l'impact de la suppression de ce terme
            test_expr_without_term = poly_expr - term

            # Reconstruire l'expression complète pour le test
            original_full = sp.sympify(self.expression_str)
            if part_name == "numérateur":
                test_full_expr = test_expr_without_term / sp.denom(original_full)
            else:
                test_full_expr = sp.numer(original_full) / test_expr_without_term

            # Tester l'impact
            impact = self.evaluate_term_impact(str(test_full_expr), test_frequencies, mag_ref)
            term_importance.append((i, term, impact))
            print(f"    Terme {i}: impact = {impact:.3f} dB")

        # Trier par impact croissant (les moins importants en premier)
        term_importance.sort(key=lambda x: x[2])

        # Simplification progressive en respectant strictement le seuil
        current_terms = list(terms)
        removed_terms = []

        for i, term, impact in term_importance:
            if len(current_terms) <= 1:  # Garder au moins 1 terme
                print(f"    Arrêt: ne peut pas supprimer le dernier terme")
                break

            # Tester la suppression de ce terme
            test_terms = [t for t in current_terms if t != term]
            if not test_terms:
                continue

            test_poly = sum(test_terms) if len(test_terms) > 1 else test_terms[0]

            # Reconstruire et tester l'expression complète
            original_full = sp.sympify(self.expression_str)
            if part_name == "numérateur":
                test_full_expr = test_poly / sp.denom(original_full)
            else:
                test_full_expr = sp.numer(original_full) / test_poly

            total_impact = self.evaluate_term_impact(str(test_full_expr), test_frequencies, mag_ref)

            print(f"    Test suppression terme (impact individuel: {impact:.3f} dB) -> erreur totale: {total_impact:.3f} dB")

            # RESPECTER STRICTEMENT LE SEUIL D'ERREUR
            if total_impact <= max_error_db:
                current_terms = test_terms
                removed_terms.append((term, impact, part_name))
                print(f"    ✓ Terme supprimé (erreur {total_impact:.3f} ≤ {max_error_db:.3f} dB): {str(term)[:50]}...")
            else:
                print(f"    ✗ Terme conservé (erreur {total_impact:.3f} > {max_error_db:.3f} dB): {str(term)[:50]}...")

        result_poly = sum(current_terms) if len(current_terms) > 1 else current_terms[0]
        print(f"  {part_name} simplifié: {len(current_terms)}/{len(terms)} termes conservés")
        print(f"  Termes supprimés: {len(removed_terms)}")

        return result_poly

    def evaluate_term_impact(self, test_expr_str, test_frequencies, mag_ref):
        """Évalue l'impact de la modification d'une expression"""
        try:
            mag_test, _ = calculate_bode_at_frequencies(test_expr_str, self.component_values, test_frequencies)
            if mag_test is not None:
                return np.max(np.abs(mag_test - mag_ref))
            else:
                return float('inf')  # Impact très élevé si l'expression est invalide
        except:
            return float('inf')

    def count_terms_in_expression(self, expr):
        """Compte le nombre de termes dans une expression"""
        if expr.is_Add:
            return len(expr.args)
        elif expr.is_Mul:
            return 1
        else:
            return 1

    def show_simplification_results(self, simplified_expr, removed_vars):
        """Affiche les résultats de la simplification"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Résultats de la simplification")
        dialog.resize(700, 500)

        layout = QVBoxLayout(dialog)

        # Titre
        title = QLabel("Simplification de la fonction de transfert")
        title.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addWidget(title)

        # Zone de texte pour les résultats
        results_text = QTextEdit()
        results_text.setReadOnly(True)
        results_text.setFont(pg.QtGui.QFont("Courier", 9))

        text = "RÉSULTATS DE LA SIMPLIFICATION SYMBOLIQUE\n"
        text += "=" * 60 + "\n\n"

        variables_kept = len(self.sensitivity_results) - len(removed_vars)
        text += f"Variables supprimées : {len(removed_vars)}\n"
        text += f"Variables conservées : {variables_kept}\n\n"

        if removed_vars:
            text += "État des variables après simplification intelligente :\n"
            text += f"{'Variable':<12} {'Valeur orig.':<15} {'Impact (dB)':<12} {'État':<20}\n"
            text += "-" * 65 + "\n"
            for var_name, var_value, impact, status in removed_vars:
                text += f"{var_name:<12} {var_value:<15.3e} {impact:<12.3f} {status:<20}\n"

        # Calculer la réduction de complexité
        original_vars = len(self.sensitivity_results)
        suppressed_vars = len([info for info in removed_vars if info[3] == "Supprimée"])
        partial_vars = len([info for info in removed_vars if info[3] == "Partiellement conservée"])

        text += f"\nRéduction de complexité :\n"
        text += f"  Variables totalement supprimées : {suppressed_vars}/{original_vars} ({(suppressed_vars/original_vars)*100:.1f}%)\n"
        text += f"  Variables partiellement conservées : {partial_vars}/{original_vars} ({(partial_vars/original_vars)*100:.1f}%)\n"

        # Comparer les longueurs d'expression
        original_length = len(self.expression_str)
        simplified_length = len(simplified_expr)
        length_reduction = ((original_length - simplified_length) / original_length) * 100

        text += f"Longueur originale : {original_length} caractères\n"
        text += f"Longueur simplifiée : {simplified_length} caractères\n"
        text += f"Réduction de longueur : {length_reduction:.1f}%\n\n"

        text += "Expression symbolique simplifiée :\n"
        text += simplified_expr

        results_text.setPlainText(text)
        layout.addWidget(results_text)

        # Boutons
        button_layout = QHBoxLayout()

        save_btn = QPushButton("Sauvegarder")
        save_btn.clicked.connect(lambda: self.save_simplified_expression(simplified_expr, removed_vars))
        button_layout.addWidget(save_btn)

        close_btn = QPushButton("Fermer")
        close_btn.clicked.connect(dialog.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        dialog.exec_()

    def save_simplified_expression(self, simplified_expr, variables_info):
        """Sauvegarde l'expression simplifiée"""
        filename = "FTC_simplified.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# Expression de transfert symbolique simplifiée (méthode intelligente)\n")
                f.write(f"# Simplification par conservation des termes dominants\n")
                f.write("# État des variables après simplification :\n")
                for var_name, var_value, impact, status in variables_info:
                    f.write(f"# {var_name} (valeur: {var_value:.3e}, impact: {impact:.3f} dB) -> {status}\n")
                f.write("\n")
                f.write(simplified_expr)

            QMessageBox.information(self, "Sauvegarde", f"Expression simplifiée sauvegardée dans : {filename}")
        except Exception as e:
            QMessageBox.warning(self, "Erreur", f"Erreur lors de la sauvegarde : {e}")

    def plot_sensitivity(self):
        """Affiche les graphiques de sensibilité"""
        try:
            if not self.sensitivity_results:
                QMessageBox.warning(self, "Erreur", "Aucun résultat d'analyse de sensibilité disponible.")
                return

            # Créer une fenêtre de graphiques de sensibilité
            self.show_sensitivity_plots()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'affichage des graphiques: {e}")
            import traceback
            traceback.print_exc()

    def show_sensitivity_plots(self):
        """Affiche les graphiques de sensibilité dans une nouvelle fenêtre"""
        # Créer une nouvelle fenêtre plus grande
        plot_window = QDialog(self)
        plot_window.setWindowTitle("Graphiques de sensibilité")
        plot_window.resize(1200, 900)  # Fenêtre plus grande pour plus d'espace

        layout = QVBoxLayout(plot_window)

        # Titre
        title = QLabel("Analyse de sensibilité des variables")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Créer les graphiques avec matplotlib
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            from matplotlib.figure import Figure

            # Créer la figure matplotlib avec plus d'espace
            fig = Figure(figsize=(15, 12))  # Figure plus grande
            canvas = FigureCanvas(fig)
            layout.addWidget(canvas)

            # Préparer les données
            variables = [result['variable'] for result in self.sensitivity_results]
            impact_scores = [result['impact_score'] for result in self.sensitivity_results]
            max_mag_changes = [result['max_mag_change'] for result in self.sensitivity_results]
            avg_mag_changes = [result['avg_mag_change'] for result in self.sensitivity_results]
            max_phase_changes = [result['max_phase_change'] for result in self.sensitivity_results]

            # Graphique 1: Scores d'impact (barres)
            ax1 = fig.add_subplot(2, 2, 1)
            bars = ax1.bar(range(len(variables)), impact_scores, color='steelblue', alpha=0.7)
            ax1.set_title('Scores d\'impact (Moyenne géométrique)', fontsize=14, fontweight='bold', pad=20)
            ax1.set_xlabel('Variables', fontsize=12)
            ax1.set_ylabel('Impact (dB)', fontsize=12)
            ax1.set_xticks(range(len(variables)))
            ax1.set_xticklabels(variables, rotation=45, ha='right', fontsize=10)
            ax1.tick_params(axis='y', labelsize=10)
            ax1.grid(True, alpha=0.3)

            # Ajouter les valeurs sur les barres
            for i, (bar, score) in enumerate(zip(bars, impact_scores)):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(impact_scores)*0.02,
                        f'{score:.2f}', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Graphique 2: Variations de magnitude (scatter)
            ax2 = fig.add_subplot(2, 2, 2)
            scatter = ax2.scatter(max_mag_changes, avg_mag_changes,
                                c=impact_scores, cmap='viridis', s=80, alpha=0.7)
            ax2.set_title('Variations de magnitude', fontsize=14, fontweight='bold', pad=20)
            ax2.set_xlabel('Variation max (dB)', fontsize=12)
            ax2.set_ylabel('Variation moyenne (dB)', fontsize=12)
            ax2.tick_params(axis='both', labelsize=10)
            ax2.grid(True, alpha=0.3)

            # Ajouter les noms des variables avec un meilleur espacement
            for i, var in enumerate(variables):
                ax2.annotate(var, (max_mag_changes[i], avg_mag_changes[i]),
                           xytext=(8, 8), textcoords='offset points', fontsize=9,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))

            # Colorbar pour le scatter
            cbar = fig.colorbar(scatter, ax=ax2, shrink=0.8)
            cbar.set_label('Score d\'impact (dB)', fontsize=11)

            # Graphique 3: Variations de phase
            ax3 = fig.add_subplot(2, 2, 3)
            bars3 = ax3.bar(range(len(variables)), max_phase_changes, color='orange', alpha=0.7)
            ax3.set_title('Variations maximales de phase', fontsize=14, fontweight='bold', pad=20)
            ax3.set_xlabel('Variables', fontsize=12)
            ax3.set_ylabel('Variation phase (°)', fontsize=12)
            ax3.set_xticks(range(len(variables)))
            ax3.set_xticklabels(variables, rotation=45, ha='right', fontsize=10)
            ax3.tick_params(axis='y', labelsize=10)
            ax3.grid(True, alpha=0.3)

            # Ajouter les valeurs sur les barres
            for i, (bar, phase) in enumerate(zip(bars3, max_phase_changes)):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(max_phase_changes)*0.02,
                        f'{phase:.1f}°', ha='center', va='bottom', fontsize=9, fontweight='bold')

            # Graphique 4: Comparaison magnitude vs phase
            ax4 = fig.add_subplot(2, 2, 4)
            ax4.scatter(impact_scores, max_phase_changes, c='red', alpha=0.7, s=80)
            ax4.set_title('Impact magnitude vs phase', fontsize=14, fontweight='bold', pad=20)
            ax4.set_xlabel('Score d\'impact magnitude (dB)', fontsize=12)
            ax4.set_ylabel('Variation max phase (°)', fontsize=12)
            ax4.tick_params(axis='both', labelsize=10)
            ax4.grid(True, alpha=0.3)

            # Ajouter les noms des variables avec un meilleur espacement
            for i, var in enumerate(variables):
                ax4.annotate(var, (impact_scores[i], max_phase_changes[i]),
                           xytext=(8, 8), textcoords='offset points', fontsize=9,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))

            # Ajuster l'espacement avec plus de padding
            fig.tight_layout(pad=4.0)  # Plus d'espace entre les graphiques

            # Ajuster manuellement l'espacement pour éviter les chevauchements
            fig.subplots_adjust(
                left=0.08,    # Marge gauche
                bottom=0.12,  # Marge bas (pour les labels rotatés)
                right=0.95,   # Marge droite
                top=0.93,     # Marge haut
                wspace=0.35,  # Espacement horizontal entre graphiques
                hspace=0.45   # Espacement vertical entre graphiques
            )

            # Rafraîchir le canvas
            canvas.draw()

        except ImportError:
            # Si matplotlib n'est pas disponible, utiliser pyqtgraph
            self.create_pyqtgraph_sensitivity_plots(layout)

        # Bouton fermer
        close_btn = QPushButton("Fermer")
        close_btn.clicked.connect(plot_window.close)
        layout.addWidget(close_btn)

        # Afficher la fenêtre
        plot_window.exec_()

    def create_pyqtgraph_sensitivity_plots(self, layout):
        """Crée les graphiques de sensibilité avec pyqtgraph si matplotlib n'est pas disponible"""
        # Créer un widget de graphiques pyqtgraph
        plot_widget = pg.GraphicsLayoutWidget()
        layout.addWidget(plot_widget)

        # Préparer les données
        variables = [result['variable'] for result in self.sensitivity_results]
        impact_scores = [result['impact_score'] for result in self.sensitivity_results]
        max_mag_changes = [result['max_mag_change'] for result in self.sensitivity_results]

        # Graphique des scores d'impact
        plot1 = plot_widget.addPlot(title="Scores d'impact des variables")
        plot1.setLabel('left', 'Impact (dB)')
        plot1.setLabel('bottom', 'Variables')

        # Créer un graphique en barres avec pyqtgraph
        x_pos = list(range(len(variables)))
        bargraph = pg.BarGraphItem(x=x_pos, height=impact_scores, width=0.6, brush='steelblue')
        plot1.addItem(bargraph)

        # Ajouter les labels des variables
        ax = plot1.getAxis('bottom')
        ax.setTicks([[(i, var) for i, var in enumerate(variables)]])

        # Message d'information
        info_label = QLabel("Graphiques de sensibilité générés avec pyqtgraph")
        info_label.setStyleSheet("color: blue; font-style: italic; margin: 10px;")
        layout.addWidget(info_label)

    def show_simplified_bode_automatically(self):
        """Affiche automatiquement le diagramme de Bode de l'expression simplifiée en superposition"""
        if not self.simplified_expression:
            print("Aucune expression simplifiée disponible")
            return

        if not self.bode_analyzer:
            print("Référence à la fenêtre d'analyse de Bode non disponible")
            return

        try:
            # Ajouter les courbes simplifiées à la fenêtre existante
            self.bode_analyzer.add_simplified_curves(self.simplified_expression, self.component_values)
            print("Diagramme de Bode de l'expression simplifiée ajouté automatiquement en superposition")

        except Exception as e:
            print(f"Erreur lors de l'affichage automatique du Bode simplifié: {e}")
            import traceback
            traceback.print_exc()



class FrequencyAxisItem(pg.AxisItem):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def tickStrings(self, values, scale, spacing):
        strings = []
        for v in values:
            freq = 10 ** v
            if freq >= 1e9:
                if abs(freq - 1e9) < 1e8:
                    strings.append('1 GHz')
                elif abs(freq - 10e9) < 1e9:
                    strings.append('10 GHz')
                elif abs(freq - 100e9) < 10e9:
                    strings.append('100 GHz')
                else:
                    strings.append(f'{freq/1e9:.0f} GHz')
            elif freq >= 1e6:
                if abs(freq - 1e6) < 1e5:
                    strings.append('1 MHz')
                elif abs(freq - 10e6) < 1e6:
                    strings.append('10 MHz')
                elif abs(freq - 100e6) < 10e6:
                    strings.append('100 MHz')
                else:
                    strings.append(f'{freq/1e6:.0f} MHz')
            elif freq >= 1e3:
                if abs(freq - 1e3) < 1e2:
                    strings.append('1 kHz')
                elif abs(freq - 10e3) < 1e3:
                    strings.append('10 kHz')
                elif abs(freq - 100e3) < 10e3:
                    strings.append('100 kHz')
                else:
                    strings.append(f'{freq/1e3:.0f} kHz')
            else:
                if abs(freq - 1) < 0.1:
                    strings.append('1 Hz')
                elif abs(freq - 10) < 1:
                    strings.append('10 Hz')
                elif abs(freq - 100) < 10:
                    strings.append('100 Hz')
                else:
                    strings.append(f'{freq:.0f} Hz')
        return strings

class BodeAnalyzer(QMainWindow):
    def __init__(self, transfer_function, variables, values, freq_params, slider_ranges):
        super().__init__()
        self.setWindowTitle("Analyseur de Bode Interactif")
        self.setGeometry(100, 100, 1400, 800)

        self.transfer_function = transfer_function
        self.variables = variables
        self.current_values = values
        self.slider_ranges = slider_ranges
        self.frequencies = np.logspace(
            np.log10(freq_params['f_min']),
            np.log10(freq_params['f_max']),
            freq_params['num_points']
        )

        # Variables pour stocker l'expression simplifiée et ses courbes
        self.simplified_expression = None
        self.magnitude_curve_simplified = None
        self.phase_curve_simplified = None

        # Variables pour stocker l'expression manuelle et ses courbes
        self.manual_expression = None
        self.magnitude_curve_manual = None
        self.phase_curve_manual = None

        # Variable pour mémoriser la dernière expression modifiée manuellement
        self.last_manual_expression = None

        # Variables pour la sélection de courbe pour le curseur
        self.cursor_tracking_mode = "original"  # "original", "simplified", "manual"



        self.init_ui()
        self.update_plot()
    
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        
        plot_widget = QWidget()
        plot_layout = QVBoxLayout(plot_widget)
        
        # Créer les boutons de sélection de courbe pour le curseur
        cursor_selection_layout = QHBoxLayout()
        cursor_label = QLabel("Curseur suit la courbe :")
        cursor_label.setStyleSheet("font-weight: bold; margin-right: 10px;")
        cursor_selection_layout.addWidget(cursor_label)

        # Créer les boutons radio
        self.cursor_button_group = QButtonGroup()

        self.cursor_original_btn = QRadioButton("Originale")
        self.cursor_original_btn.setChecked(True)  # Par défaut
        self.cursor_original_btn.toggled.connect(lambda checked: self.set_cursor_tracking_mode("original") if checked else None)
        cursor_selection_layout.addWidget(self.cursor_original_btn)
        self.cursor_button_group.addButton(self.cursor_original_btn)

        self.cursor_simplified_btn = QRadioButton("Simplifiée")
        self.cursor_simplified_btn.setEnabled(False)  # Désactivé au début
        self.cursor_simplified_btn.toggled.connect(lambda checked: self.set_cursor_tracking_mode("simplified") if checked else None)
        cursor_selection_layout.addWidget(self.cursor_simplified_btn)
        self.cursor_button_group.addButton(self.cursor_simplified_btn)

        self.cursor_manual_btn = QRadioButton("Manuelle")
        self.cursor_manual_btn.setEnabled(False)  # Désactivé au début
        self.cursor_manual_btn.toggled.connect(lambda checked: self.set_cursor_tracking_mode("manual") if checked else None)
        cursor_selection_layout.addWidget(self.cursor_manual_btn)
        self.cursor_button_group.addButton(self.cursor_manual_btn)

        cursor_selection_layout.addStretch()  # Pousser les boutons vers la gauche
        plot_layout.addLayout(cursor_selection_layout)

        self.plot_widget = pg.GraphicsLayoutWidget()
        plot_layout.addWidget(self.plot_widget)
        
        self.magnitude_plot = self.plot_widget.addPlot(title="Magnitude (dB)")
        self.magnitude_plot.setLogMode(x=True, y=False)
        self.magnitude_plot.setLabel('left', 'Magnitude', units='dB')
        self.magnitude_plot.setLabel('bottom', 'Fréquence')
        self.magnitude_plot.showGrid(x=True, y=True)
        
        self.plot_widget.nextRow()
        
        self.phase_plot = self.plot_widget.addPlot(title="Phase (degrés)")
        self.phase_plot.setLogMode(x=True, y=False)
        self.phase_plot.setLabel('left', 'Phase', units='°')
        self.phase_plot.setLabel('bottom', 'Fréquence')
        self.phase_plot.showGrid(x=True, y=True)
        
        self.magnitude_curve = self.magnitude_plot.plot(pen=pg.mkPen('b', width=2), name='Expression originale')
        self.phase_curve = self.phase_plot.plot(pen=pg.mkPen('b', width=2), name='Expression originale')

        # Ajouter la légende dès la création des courbes originales
        try:
            self.magnitude_plot.addLegend()
            self.phase_plot.addLegend()
        except:
            pass  # La légende existe déjà

        self.setup_frequency_axis()
        self.setup_crosshair()
        
        sliders_widget = QWidget()
        sliders_layout = QVBoxLayout(sliders_widget)
        sliders_layout.setAlignment(Qt.AlignTop)
        
        sliders_grid = QGridLayout()
        
        self.sliders = {}
        self.labels = {}
        
        for i, var in enumerate(self.variables):
            value = self.current_values[var]
            min_val, max_val = self.slider_ranges[var]

            label = QLabel(f"{var}: {value:.2e}")
            self.labels[var] = label

            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(0)
            slider.setMaximum(1000)

            if max_val / min_val > 100:
                log_min = np.log10(abs(min_val)) if min_val > 0 else np.log10(1e-15)
                log_max = np.log10(abs(max_val)) if max_val > 0 else np.log10(1e-15)
                log_value = np.log10(abs(value)) if value > 0 else np.log10(1e-15)
                normalized_pos = (log_value - log_min) / (log_max - log_min)
                use_log = True
            else:
                normalized_pos = (value - min_val) / (max_val - min_val)
                use_log = False

            normalized_pos = max(0, min(1, normalized_pos))
            slider.setValue(int(normalized_pos * 1000))

            slider.valueChanged.connect(lambda v, variable=var, mn=min_val, mx=max_val, log=use_log: self.slider_changed(variable, mn, mx, v, log))

            self.sliders[var] = slider

            sliders_grid.addWidget(label, i, 0)
            sliders_grid.addWidget(slider, i, 1)
        
        sliders_layout.addLayout(sliders_grid)

        # Ajouter le bouton d'analyse de sensibilité
        sensitivity_btn = QPushButton("Analyse de sensibilité")
        sensitivity_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        sensitivity_btn.clicked.connect(self.run_sensitivity_analysis)
        sliders_layout.addWidget(sensitivity_btn)

        # Ajouter le bouton d'ajustement manuel
        self.manual_adjustment_btn = QPushButton("Ajustement manuel")
        self.manual_adjustment_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; padding: 10px; }")
        self.manual_adjustment_btn.clicked.connect(self.manual_adjustment)
        sliders_layout.addWidget(self.manual_adjustment_btn)

        # Ajouter le bouton Clear pour enlever toutes les courbes supplémentaires
        self.clear_all_btn = QPushButton("Clear - Enlever courbes ajoutées")
        self.clear_all_btn.setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; padding: 10px; }")
        self.clear_all_btn.clicked.connect(self.clear_all_additional_curves)
        self.clear_all_btn.setEnabled(False)  # Désactivé au début
        sliders_layout.addWidget(self.clear_all_btn)

        main_layout.addWidget(plot_widget, 3)
        main_layout.addWidget(sliders_widget, 1)
    
    def setup_frequency_axis(self):
        self.magnitude_plot.setAxisItems({'bottom': FrequencyAxisItem(orientation='bottom')})
        self.phase_plot.setAxisItems({'bottom': FrequencyAxisItem(orientation='bottom')})

    def setup_crosshair(self):
        self.magnitude_vline = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('k', width=1, style=pg.QtCore.Qt.DashLine))
        self.magnitude_hline = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('k', width=1, style=pg.QtCore.Qt.DashLine))
        self.phase_vline = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('k', width=1, style=pg.QtCore.Qt.DashLine))
        self.phase_hline = pg.InfiniteLine(angle=0, movable=False, pen=pg.mkPen('k', width=1, style=pg.QtCore.Qt.DashLine))

        self.magnitude_plot.addItem(self.magnitude_vline, ignoreBounds=True)
        self.magnitude_plot.addItem(self.magnitude_hline, ignoreBounds=True)
        self.phase_plot.addItem(self.phase_vline, ignoreBounds=True)
        self.phase_plot.addItem(self.phase_hline, ignoreBounds=True)

        self.magnitude_label = pg.TextItem(anchor=(0, 1), color='k', fill=pg.mkBrush(255, 255, 255, 200))
        self.phase_label = pg.TextItem(anchor=(0, 1), color='k', fill=pg.mkBrush(255, 255, 255, 200))
        self.magnitude_plot.addItem(self.magnitude_label)
        self.phase_plot.addItem(self.phase_label)

        self.magnitude_proxy = pg.SignalProxy(self.magnitude_plot.scene().sigMouseMoved, rateLimit=60, slot=self.mouse_moved_magnitude)
        self.phase_proxy = pg.SignalProxy(self.phase_plot.scene().sigMouseMoved, rateLimit=60, slot=self.mouse_moved_phase)

        self.magnitude_vline.hide()
        self.magnitude_hline.hide()
        self.phase_vline.hide()
        self.phase_hline.hide()
        self.magnitude_label.hide()
        self.phase_label.hide()

        self.magnitude_plot.scene().sigMouseMoved.connect(self.show_crosshair)
        self.phase_plot.scene().sigMouseMoved.connect(self.show_crosshair)
        self.magnitude_plot.leaveEvent = self.hide_crosshair_magnitude
        self.phase_plot.leaveEvent = self.hide_crosshair_phase
    
    def slider_changed(self, variable, min_val, max_val, slider_value, use_log=False):
        normalized_value = slider_value / 1000.0

        if use_log:
            log_min = np.log10(abs(min_val)) if min_val > 0 else np.log10(1e-15)
            log_max = np.log10(abs(max_val)) if max_val > 0 else np.log10(1e-15)
            log_actual = log_min + normalized_value * (log_max - log_min)
            actual_value = 10 ** log_actual
            if min_val < 0:
                actual_value = -actual_value
        else:
            actual_value = min_val + normalized_value * (max_val - min_val)

        self.current_values[variable] = actual_value
        self.labels[variable].setText(f"{variable}: {actual_value:.2e}")

        self.update_plot()
    
    def show_crosshair(self):
        self.magnitude_vline.show()
        self.magnitude_hline.show()
        self.phase_vline.show()
        self.phase_hline.show()
        self.magnitude_label.show()
        self.phase_label.show()

    def hide_crosshair_magnitude(self, event):
        self.magnitude_vline.hide()
        self.magnitude_hline.hide()
        self.magnitude_label.hide()

    def hide_crosshair_phase(self, event):
        self.phase_vline.hide()
        self.phase_hline.hide()
        self.phase_label.hide()

    def set_cursor_tracking_mode(self, mode):
        """Change le mode de suivi du curseur"""
        self.cursor_tracking_mode = mode
        print(f"Mode de suivi du curseur changé vers: {mode}")

    def get_interpolated_values(self, freq_log):
        try:
            freq = 10 ** freq_log

            # Choisir l'expression à utiliser selon le mode de suivi
            if self.cursor_tracking_mode == "simplified" and self.simplified_expression:
                expression = self.simplified_expression
                variables = extract_variables_from_expression(self.simplified_expression)
            elif self.cursor_tracking_mode == "manual" and self.manual_expression:
                expression = self.manual_expression
                variables = extract_variables_from_expression(self.manual_expression)
            else:
                # Mode original par défaut
                expression = self.transfer_function
                variables = self.variables

            magnitude, phase = evaluate_fast(
                expression,
                np.array([freq]),
                self.current_values,
                variables
            )
            mag_db = 20 * np.log10(magnitude[0]) if magnitude[0] > 0 else -200
            phase_deg = phase[0]
            return mag_db, phase_deg
        except:
            return 0, 0

    def mouse_moved_magnitude(self, evt):
        pos = evt[0]
        if self.magnitude_plot.sceneBoundingRect().contains(pos):
            mouse_point = self.magnitude_plot.vb.mapSceneToView(pos)
            freq = 10 ** mouse_point.x()

            mag_interp, phase_interp = self.get_interpolated_values(mouse_point.x())

            self.magnitude_vline.setPos(mouse_point.x())
            self.magnitude_hline.setPos(mag_interp)
            self.phase_vline.setPos(mouse_point.x())
            self.phase_hline.setPos(phase_interp)

            if freq >= 1e9:
                freq_str = f"{freq/1e9:.2f} GHz"
            elif freq >= 1e6:
                freq_str = f"{freq/1e6:.2f} MHz"
            elif freq >= 1e3:
                freq_str = f"{freq/1e3:.2f} kHz"
            else:
                freq_str = f"{freq:.2f} Hz"

            self.magnitude_label.setText(f"f: {freq_str}\nGain: {mag_interp:.2f} dB")
            self.magnitude_label.setPos(mouse_point.x(), mag_interp)

            self.phase_label.setText(f"f: {freq_str}\nPhase: {phase_interp:.2f}°")
            self.phase_label.setPos(mouse_point.x(), phase_interp)

    def mouse_moved_phase(self, evt):
        pos = evt[0]
        if self.phase_plot.sceneBoundingRect().contains(pos):
            mouse_point = self.phase_plot.vb.mapSceneToView(pos)
            freq = 10 ** mouse_point.x()

            mag_interp, phase_interp = self.get_interpolated_values(mouse_point.x())

            self.magnitude_vline.setPos(mouse_point.x())
            self.magnitude_hline.setPos(mag_interp)
            self.phase_vline.setPos(mouse_point.x())
            self.phase_hline.setPos(phase_interp)

            if freq >= 1e9:
                freq_str = f"{freq/1e9:.2f} GHz"
            elif freq >= 1e6:
                freq_str = f"{freq/1e6:.2f} MHz"
            elif freq >= 1e3:
                freq_str = f"{freq/1e3:.2f} kHz"
            else:
                freq_str = f"{freq:.2f} Hz"

            self.magnitude_label.setText(f"f: {freq_str}\nGain: {mag_interp:.2f} dB")
            self.magnitude_label.setPos(mouse_point.x(), mag_interp)

            self.phase_label.setText(f"f: {freq_str}\nPhase: {phase_interp:.2f}°")
            self.phase_label.setPos(mouse_point.x(), phase_interp)

    def update_plot(self):
        try:
            # Mettre à jour la courbe originale
            magnitude, phase = evaluate_fast(
                self.transfer_function,
                self.frequencies,
                self.current_values,
                self.variables
            )

            valid_mag = np.where(np.isfinite(magnitude) & (magnitude > 0), magnitude, 1e-12)
            valid_phase = np.where(np.isfinite(phase), phase, 0)

            self.magnitude_curve.setData(self.frequencies, 20 * np.log10(valid_mag))
            self.phase_curve.setData(self.frequencies, valid_phase)

            # Mettre à jour la courbe simplifiée si elle existe
            if self.simplified_expression and self.magnitude_curve_simplified and self.phase_curve_simplified:
                try:
                    magnitude_simplified, phase_simplified = evaluate_fast(
                        self.simplified_expression,
                        self.frequencies,
                        self.current_values,
                        extract_variables_from_expression(self.simplified_expression)
                    )

                    valid_mag_simplified = np.where(np.isfinite(magnitude_simplified) & (magnitude_simplified > 0), magnitude_simplified, 1e-12)
                    valid_phase_simplified = np.where(np.isfinite(phase_simplified), phase_simplified, 0)

                    self.magnitude_curve_simplified.setData(self.frequencies, 20 * np.log10(valid_mag_simplified))
                    self.phase_curve_simplified.setData(self.frequencies, valid_phase_simplified)

                except Exception as e:
                    print(f"Erreur lors de la mise à jour de la courbe simplifiée: {e}")

            # Mettre à jour la courbe manuelle si elle existe
            if self.manual_expression and self.magnitude_curve_manual and self.phase_curve_manual:
                try:
                    magnitude_manual, phase_manual = evaluate_fast(
                        self.manual_expression,
                        self.frequencies,
                        self.current_values,
                        extract_variables_from_expression(self.manual_expression)
                    )

                    valid_mag_manual = np.where(np.isfinite(magnitude_manual) & (magnitude_manual > 0), magnitude_manual, 1e-12)
                    valid_phase_manual = np.where(np.isfinite(phase_manual), phase_manual, 0)

                    self.magnitude_curve_manual.setData(self.frequencies, 20 * np.log10(valid_mag_manual))
                    self.phase_curve_manual.setData(self.frequencies, valid_phase_manual)

                except Exception as e:
                    print(f"Erreur lors de la mise à jour de la courbe manuelle: {e}")

        except Exception as e:
            print(f"Erreur lors de la mise à jour: {e}")

    def add_simplified_curves(self, simplified_expression, component_values):
        """Ajoute les courbes de l'expression simplifiée en superposition"""
        try:
            # Stocker l'expression simplifiée pour les mises à jour futures
            self.simplified_expression = simplified_expression

            # Supprimer les anciennes courbes simplifiées si elles existent
            if self.magnitude_curve_simplified:
                self.magnitude_plot.removeItem(self.magnitude_curve_simplified)
            if self.phase_curve_simplified:
                self.phase_plot.removeItem(self.phase_curve_simplified)

            # Calculer la réponse de l'expression simplifiée
            magnitude_simplified, phase_simplified = evaluate_fast(
                simplified_expression,
                self.frequencies,
                self.current_values,  # Utiliser les valeurs actuelles des sliders
                extract_variables_from_expression(simplified_expression)
            )

            # Vérifier que les données sont valides
            valid_mag = np.where(np.isfinite(magnitude_simplified) & (magnitude_simplified > 0), magnitude_simplified, 1e-12)
            valid_phase = np.where(np.isfinite(phase_simplified), phase_simplified, 0)

            # Ajouter les courbes simplifiées avec un style différent
            self.magnitude_curve_simplified = self.magnitude_plot.plot(
                self.frequencies,
                20 * np.log10(valid_mag),
                pen=pg.mkPen('r', width=3, style=pg.QtCore.Qt.DashLine),
                name='Expression simplifiée'
            )

            self.phase_curve_simplified = self.phase_plot.plot(
                self.frequencies,
                valid_phase,
                pen=pg.mkPen('r', width=3, style=pg.QtCore.Qt.DashLine),
                name='Expression simplifiée'
            )

            # Ajouter une légende (seulement si elle n'existe pas déjà)
            try:
                self.magnitude_plot.addLegend()
                self.phase_plot.addLegend()
            except:
                pass  # La légende existe déjà

            # Mettre à jour les titres pour indiquer la comparaison
            self.magnitude_plot.setTitle("Magnitude (dB) - Original vs Simplifié")
            self.phase_plot.setTitle("Phase (degrés) - Original vs Simplifié")

            # Activer le bouton Clear
            self.clear_all_btn.setEnabled(True)

            # Activer le bouton radio pour la courbe simplifiée
            self.cursor_simplified_btn.setEnabled(True)

            print("Courbes simplifiées ajoutées avec succès")

        except Exception as e:
            print(f"Erreur lors de l'ajout des courbes simplifiées: {e}")
            import traceback
            traceback.print_exc()

    def clear_all_additional_curves(self):
        """Enlève toutes les courbes supplémentaires (simplifiées et manuelles) du graphique"""
        try:
            curves_removed = []

            # Supprimer les courbes simplifiées si elles existent
            if self.magnitude_curve_simplified:
                self.magnitude_plot.removeItem(self.magnitude_curve_simplified)
                self.magnitude_curve_simplified = None
                curves_removed.append("magnitude simplifiée")

            if self.phase_curve_simplified:
                self.phase_plot.removeItem(self.phase_curve_simplified)
                self.phase_curve_simplified = None
                curves_removed.append("phase simplifiée")

            # Supprimer les courbes manuelles si elles existent
            if self.magnitude_curve_manual:
                self.magnitude_plot.removeItem(self.magnitude_curve_manual)
                self.magnitude_curve_manual = None
                curves_removed.append("magnitude manuelle")

            if self.phase_curve_manual:
                self.phase_plot.removeItem(self.phase_curve_manual)
                self.phase_curve_manual = None
                curves_removed.append("phase manuelle")

            # Réinitialiser les expressions
            self.simplified_expression = None
            self.manual_expression = None

            # Remettre les titres originaux
            self.magnitude_plot.setTitle("Magnitude (dB)")
            self.phase_plot.setTitle("Phase (degrés)")

            # Désactiver le bouton Clear
            self.clear_all_btn.setEnabled(False)

            # Désactiver les boutons radio et revenir au mode original
            self.cursor_simplified_btn.setEnabled(False)
            self.cursor_manual_btn.setEnabled(False)
            self.cursor_original_btn.setChecked(True)
            self.cursor_tracking_mode = "original"

            if curves_removed:
                print(f"Courbes supprimées: {', '.join(curves_removed)}")
                print("Toutes les courbes supplémentaires ont été supprimées avec succès")
            else:
                print("Aucune courbe supplémentaire à supprimer")

        except Exception as e:
            print(f"Erreur lors de la suppression des courbes: {e}")
            import traceback
            traceback.print_exc()

    def add_manual_curves(self, manual_expression, component_values):
        """Ajoute les courbes de l'expression modifiée manuellement en superposition"""
        try:
            # Stocker l'expression manuelle pour les mises à jour futures
            self.manual_expression = manual_expression

            # Supprimer les anciennes courbes manuelles si elles existent
            if self.magnitude_curve_manual:
                self.magnitude_plot.removeItem(self.magnitude_curve_manual)
            if self.phase_curve_manual:
                self.phase_plot.removeItem(self.phase_curve_manual)

            # Calculer la réponse de l'expression manuelle
            magnitude_manual, phase_manual = evaluate_fast(
                manual_expression,
                self.frequencies,
                self.current_values,  # Utiliser les valeurs actuelles des sliders
                extract_variables_from_expression(manual_expression)
            )

            # Vérifier que les données sont valides
            valid_mag = np.where(np.isfinite(magnitude_manual) & (magnitude_manual > 0), magnitude_manual, 1e-12)
            valid_phase = np.where(np.isfinite(phase_manual), phase_manual, 0)

            # Ajouter les courbes manuelles avec un style différent
            self.magnitude_curve_manual = self.magnitude_plot.plot(
                self.frequencies,
                20 * np.log10(valid_mag),
                pen=pg.mkPen('g', width=3, style=pg.QtCore.Qt.DotLine),
                name='Expression manuelle'
            )

            self.phase_curve_manual = self.phase_plot.plot(
                self.frequencies,
                valid_phase,
                pen=pg.mkPen('g', width=3, style=pg.QtCore.Qt.DotLine),
                name='Expression manuelle'
            )

            # Ajouter une légende (seulement si elle n'existe pas déjà)
            try:
                self.magnitude_plot.addLegend()
                self.phase_plot.addLegend()
            except:
                pass  # La légende existe déjà

            # Mettre à jour les titres pour indiquer la comparaison
            title_parts = []
            if self.simplified_expression:
                title_parts.append("Simplifié")
            if self.manual_expression:
                title_parts.append("Manuel")

            if title_parts:
                comparison_text = " vs ".join(["Original"] + title_parts)
                self.magnitude_plot.setTitle(f"Magnitude (dB) - {comparison_text}")
                self.phase_plot.setTitle(f"Phase (degrés) - {comparison_text}")

            # Activer le bouton Clear
            self.clear_all_btn.setEnabled(True)

            # Activer le bouton radio pour la courbe manuelle
            self.cursor_manual_btn.setEnabled(True)

            print("Courbes manuelles ajoutées avec succès")

        except Exception as e:
            print(f"Erreur lors de l'ajout des courbes manuelles: {e}")
            import traceback
            traceback.print_exc()

    def run_sensitivity_analysis(self):
        """Lance l'analyse de sensibilité"""
        try:
            # Utiliser automatiquement le nombre maximum de processus détectés
            num_cores = mp.cpu_count()
            num_processes = num_cores  # Utiliser tous les cœurs disponibles

            print(f"Lancement de l'analyse de sensibilité avec {num_processes} processus...")
            sensitivity_results = analyze_variable_sensitivity_parallel(
                self.transfer_function,
                self.current_values,
                num_processes
            )

            if sensitivity_results:
                # Afficher les résultats dans une fenêtre
                dialog = SensitivityResultsDialog(sensitivity_results, self.transfer_function, self.current_values, self)
                dialog.exec_()
            else:
                QMessageBox.warning(self, "Erreur", "Erreur lors de l'analyse de sensibilité")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'analyse: {e}")
            import traceback
            traceback.print_exc()

    def manual_adjustment(self):
        """Permet l'ajustement manuel de l'expression de transfert"""
        # Créer un dialogue pour l'édition manuelle
        dialog = QDialog(self)
        dialog.setWindowTitle("Ajustement manuel de l'expression")
        dialog.resize(800, 400)

        layout = QVBoxLayout(dialog)

        # Titre
        title = QLabel("Modification manuelle de l'expression de transfert")
        title.setStyleSheet("font-size: 14px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)

        # Instructions
        instructions = QLabel("Modifiez l'expression symbolique ci-dessous. L'expression doit être valide en syntaxe SymPy.")
        instructions.setStyleSheet("color: #666; margin: 5px;")
        layout.addWidget(instructions)

        # Zone de texte pour l'édition - pré-remplie avec l'expression actuelle
        self.expression_edit = QTextEdit()
        self.expression_edit.setFont(pg.QtGui.QFont("Courier", 11))

        # Priorité pour l'expression à afficher :
        # 1. Dernière expression modifiée manuellement (si elle existe)
        # 2. Expression simplifiée (si disponible)
        # 3. Expression originale
        if self.last_manual_expression:
            initial_expression = self.last_manual_expression
            print("Utilisation de la dernière expression modifiée manuellement")
        elif hasattr(self, 'simplified_expression') and self.simplified_expression:
            initial_expression = self.simplified_expression
            print("Utilisation de l'expression simplifiée")
        else:
            initial_expression = self.transfer_function
            print("Utilisation de l'expression originale")

        self.expression_edit.setPlainText(initial_expression)
        self.expression_edit.setMinimumHeight(150)
        layout.addWidget(self.expression_edit)

        # Boutons
        button_layout = QHBoxLayout()

        validate_btn = QPushButton("Valider et afficher")
        validate_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        validate_btn.clicked.connect(lambda: self.apply_manual_expression(dialog))
        button_layout.addWidget(validate_btn)

        reset_btn = QPushButton("Remettre l'expression originale")
        reset_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; }")
        reset_btn.clicked.connect(lambda: self.expression_edit.setPlainText(self.transfer_function))
        button_layout.addWidget(reset_btn)

        clear_btn = QPushButton("Effacer la mémorisation")
        clear_btn.setStyleSheet("QPushButton { background-color: #FF5722; color: white; padding: 8px; }")
        clear_btn.clicked.connect(lambda: self.clear_manual_memory())
        button_layout.addWidget(clear_btn)

        cancel_btn = QPushButton("Annuler")
        cancel_btn.clicked.connect(dialog.close)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        dialog.exec_()

    def apply_manual_expression(self, dialog):
        """Applique l'expression modifiée manuellement"""
        try:
            # Récupérer l'expression modifiée
            manual_expr_str = self.expression_edit.toPlainText().strip()

            if not manual_expr_str:
                QMessageBox.warning(dialog, "Erreur", "L'expression ne peut pas être vide.")
                return

            # Valider l'expression avec SymPy
            try:
                test_expr = sp.sympify(manual_expr_str)
                print(f"Expression manuelle validée: {test_expr}")
            except Exception as e:
                QMessageBox.warning(dialog, "Erreur de syntaxe",
                                  f"L'expression n'est pas valide:\n{str(e)}\n\nVeuillez corriger la syntaxe.")
                return

            # Sauvegarder l'expression modifiée pour la prochaine fois
            self.last_manual_expression = manual_expr_str
            print(f"Expression manuelle sauvegardée: {manual_expr_str[:100]}...")

            # Ajouter les courbes manuelles au diagramme de Bode
            self.add_manual_curves(manual_expr_str, self.current_values)

            # Fermer le dialogue
            dialog.close()

            QMessageBox.information(self, "Succès",
                                  "Expression manuelle appliquée et ajoutée au diagramme de Bode.\n"
                                  "La courbe verte pointillée répond maintenant aux changements de sliders.\n"
                                  "L'expression sera conservée lors de la prochaine ouverture.")

        except Exception as e:
            QMessageBox.critical(dialog, "Erreur", f"Erreur lors de l'application de l'expression manuelle: {e}")
            import traceback
            traceback.print_exc()

    def clear_manual_memory(self):
        """Efface la mémorisation de l'expression manuelle"""
        self.last_manual_expression = None
        print("Mémorisation de l'expression manuelle effacée")

        # Remettre l'expression par défaut dans le champ de texte
        if hasattr(self, 'simplified_expression') and self.simplified_expression:
            default_expression = self.simplified_expression
            print("Retour à l'expression simplifiée")
        else:
            default_expression = self.transfer_function
            print("Retour à l'expression originale")

        if hasattr(self, 'expression_edit'):
            self.expression_edit.setPlainText(default_expression)

def main():
    app = QApplication(sys.argv)

    try:
        # Définir le nom de l'application pour la barre des tâches Windows
        app.setApplicationName("Analyseur de Bode")
        app.setApplicationDisplayName("Analyseur de Bode - Fonctions de Transfert")
        app.setApplicationVersion("1.0")

        # IMPORTANT: Pour Windows, définir l'App User Model ID pour grouper correctement dans la barre des tâches
        try:
            import ctypes
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("AugmentCode.BodeAnalyzer.1.0")
        except:
            pass  # Ignore si pas sur Windows ou si erreur

        # Configurer l'icône de l'application APRÈS avoir défini l'App ID
        set_application_icon(app)

        pg.setConfigOptions(antialias=True)
        pg.setConfigOption('background', 'w')
        pg.setConfigOption('foreground', 'k')

        transfer_function = read_transfer_function("FTC.txt")
        variables = extract_variables_from_expression(transfer_function)

        values, freq_params, slider_ranges = get_user_input(variables)
        if values is None:
            sys.exit()

        window = BodeAnalyzer(transfer_function, variables, values, freq_params, slider_ranges)

        # Appliquer l'icône à la fenêtre principale aussi
        set_application_icon(app, window)

        window.show()
        sys.exit(app.exec_())

    except FileNotFoundError:
        QMessageBox.critical(None, "Erreur", "Le fichier FTC.txt n'a pas été trouvé.")
    except Exception as e:
        QMessageBox.critical(None, "Erreur", f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
