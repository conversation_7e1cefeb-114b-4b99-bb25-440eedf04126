#!/usr/bin/env python3
"""
Test simple pour créer une icône et tester l'affichage dans la barre des tâches Windows
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt5.QtGui import QPixmap, QPainter, QPen, QBrush, QIcon
from PyQt5.QtCore import Qt

def create_simple_icon():
    """Crée une icône simple pour test"""
    pixmap = QPixmap(32, 32)
    pixmap.fill(Qt.white)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # Cercle bleu
    painter.setBrush(QBrush(Qt.blue))
    painter.setPen(QPen(Qt.darkBlue, 2))
    painter.drawEllipse(4, 4, 24, 24)
    
    # Lettre B au centre
    painter.setPen(QPen(Qt.white, 2))
    painter.drawText(12, 20, "B")
    
    painter.end()
    
    # Sauvegarder pour test
    pixmap.save("test_icon.png", "PNG")
    print("Icône de test créée: test_icon.png")
    
    return QIcon(pixmap)

def main():
    app = QApplication(sys.argv)
    
    # Définir l'App User Model ID pour Windows
    try:
        import ctypes
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("Test.BodeIcon.1.0")
        print("App User Model ID défini pour Windows")
    except:
        print("Pas sur Windows ou erreur App ID")
    
    # Créer et définir l'icône
    icon = create_simple_icon()
    app.setWindowIcon(icon)
    
    # Créer une fenêtre de test
    window = QMainWindow()
    window.setWindowTitle("Test Icône Bode - Regardez la barre des tâches!")
    window.setWindowIcon(icon)
    window.resize(400, 200)
    
    # Widget central
    central_widget = QWidget()
    layout = QVBoxLayout()
    
    label = QLabel("🎯 TEST D'ICÔNE DANS LA BARRE DES TÂCHES\n\n"
                   "Regardez l'icône dans la barre des tâches Windows.\n"
                   "Elle devrait être un cercle bleu avec 'B' au lieu de l'icône Python.\n\n"
                   "Si vous voyez encore l'icône Python, fermez complètement\n"
                   "l'application et relancez-la.")
    label.setAlignment(Qt.AlignCenter)
    label.setStyleSheet("font-size: 14px; padding: 20px;")
    
    layout.addWidget(label)
    central_widget.setLayout(layout)
    window.setCentralWidget(central_widget)
    
    window.show()
    
    print("Fenêtre de test ouverte. Vérifiez l'icône dans la barre des tâches Windows!")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
