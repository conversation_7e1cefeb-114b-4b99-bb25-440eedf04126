#!/usr/bin/env python3
"""
Test simple pour vérifier la correction de la modélisation de la diode
"""

# Simulation du circuit simple: V1 1 0 DC 5, R1 1 2 100, D1 2 0 2.2

def test_diode_circuit():
    print("=== Test du circuit avec diode corrigé ===")
    print("Circuit: V1(5V DC, 3V AC) → R1(100Ω) → D1(2.2V, rd=10Ω) → GND")
    print()

    # Analyse DC manuelle attendue:
    # V1 = 5V (imposé par la source)
    # V2 = 2.2V (imposé par la tension de seuil de la diode)
    # I_D1 = I_R1 = (V1 - V2) / R1 = (5 - 2.2) / 100 = 0.028 A = 28 mA
    # I_V1 = -I_R1 = -0.028 A

    print("Résultats attendus DC:")
    print("V1 = 5.0 V")
    print("V2 = 2.2 V")
    print("I_R1 = 0.028 A (28 mA)")
    print("I_D1 = 0.028 A (28 mA)")
    print("I_V1 = -0.028 A (-28 mA)")
    print()

    # Analyse AC manuelle attendue avec résistance dynamique calculée:
    # rd = Vt / Id_dc = 0.026 / 0.028 ≈ 0.929 Ω
    # Diviseur de tension: V2 = V1 × rd/(R1 + rd) = 3 × 0.929/(100 + 0.929) ≈ 0.0276 V
    # I_AC = V1/(R1 + rd) = 3/100.929 ≈ 0.0297 A

    print("Résultats attendus AC (avec rd calculée):")
    print("V1 = 3.0 V")
    print("rd ≈ 0.929 Ω (calculée: Vt/Id_dc = 0.026/0.028)")
    print("V2 ≈ 0.0276 V (diviseur de tension)")
    print("I_R1 ≈ 0.0297 A")
    print("I_D1 ≈ 0.0297 A")
    print("I_V1 ≈ -0.0297 A")
    print()
    
    print("Modélisation corrigée:")
    print("- La diode est traitée comme un composant actif avec variable de courant")
    print("- Contrainte: V_anode - V_cathode = Vth_D1")
    print("- Équations KCL aux nœuds avec le courant de diode")
    print()
    
    print("Matrice système (conceptuel):")
    print("Variables: [V1, V2, I_D1]")
    print("Équations:")
    print("1. V1 = 5 (source de tension)")
    print("2. V2 - 0 = 2.2 (contrainte diode: V_anode - V_cathode = Vth)")
    print("3. KCL nœud 2: (V1-V2)/R1 - I_D1 = 0")
    print()
    
    # Vérification manuelle DC
    V1_dc = 5.0
    V2_dc = 2.2  # Imposé par la diode
    R1 = 100.0
    I_R1_dc = (V1_dc - V2_dc) / R1
    I_D1_dc = I_R1_dc  # Par KCL au nœud 2
    I_V1_dc = -I_R1_dc  # Par KCL au nœud 1

    print("Vérification manuelle DC:")
    print(f"V1 = {V1_dc} V")
    print(f"V2 = {V2_dc} V")
    print(f"I_R1 = {I_R1_dc} A")
    print(f"I_D1 = {I_D1_dc} A")
    print(f"I_V1 = {I_V1_dc} A")
    print()

    # Vérification manuelle AC avec résistance dynamique calculée
    V1_ac = 3.0
    Vt = 0.026  # Tension thermique
    rd_calculated = Vt / I_R1_dc  # Résistance dynamique calculée
    V2_ac = V1_ac * rd_calculated / (R1 + rd_calculated)  # Diviseur de tension
    I_ac = V1_ac / (R1 + rd_calculated)

    print("Vérification manuelle AC (avec rd calculée):")
    print(f"V1 = {V1_ac} V")
    print(f"rd = Vt/Id_dc = {Vt}/{I_R1_dc} = {rd_calculated:.3f} Ω")
    print(f"V2 = {V2_ac:.6f} V (diviseur: {V1_ac} × {rd_calculated:.3f}/{R1+rd_calculated:.3f})")
    print(f"I_R1 = {I_ac:.6f} A")
    print(f"I_D1 = {I_ac:.6f} A")
    print(f"I_V1 = {-I_ac:.6f} A")
    print()

    print("✓ La résistance dynamique est maintenant calculée physiquement")
    print("✓ Basée sur le point de fonctionnement DC réel de la diode")

if __name__ == "__main__":
    test_diode_circuit()
