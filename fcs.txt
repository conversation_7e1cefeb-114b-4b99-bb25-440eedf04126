H1(s) = V7/V1 = (-7158278820000000000*C1*C2*C3*R1*R2*R3*R4*R5*R6*gm_Q1*ro_Q1*rpi_Q1*s^4-3579497323941000001*C1*C2*C3*R2*R3*R4*R5*R6*gm_Q1*ro_Q1*rpi_Q1*s^4-3579139410000010000000000*C1*C2*C3*R2*R3*R4*R5*R6*gm_Q1*ro_Q1*rpi_Q1*s^3-7158278820000000000*C1*C3*R1*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^3+7158278820000000000*C1*C3*R1*R2*R3*R4*R5*R6*s^3-7158278820000000000*C1*C3*R1*R2*R3*R4*R6*gm_Q1*ro_Q1*rpi_Q1*s^3-3579497323941000001*C1*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^3-3579139410000010000000000*C1*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R4*R5*R6*s^3+3579139410000010000000000*C1*C3*R2*R3*R4*R5*R6*s^2-3579497323941000001*C1*C3*R2*R3*R4*R6*gm_Q1*ro_Q1*rpi_Q1*s^3-3579139410000010000000000*C1*C3*R2*R3*R4*R6*gm_Q1*ro_Q1*rpi_Q1*s^2-7158278820000000000*C2*C3*R2*R3*R4*R5*R6*gm_Q1*ro_Q1*rpi_Q1*s^3-7158278820000000000*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^2+7158278820000000000*C3*R2*R3*R4*R5*R6*s^2-7158278820000000000*C3*R2*R3*R4*R6*gm_Q1*ro_Q1*rpi_Q1*s^2)/(357913941*C1*C2*C3*R1*R2*R3*R4*R5*R6*rpi_Q1*s^5+3579139410000000000*C1*C2*C3*R1*R2*R3*R4*R5*R6*rpi_Q1*s^4+3579497323941000001*C1*C2*C3*R1*R2*R3*R4*R5*R6*s^4+3579139410000010000000000*C1*C2*C3*R1*R2*R3*R4*R5*R6*s^3+357913941*C1*C2*C3*R1*R2*R3*R4*R5*ro_Q1*rpi_Q1*s^5+3579139410000000000*C1*C2*C3*R1*R2*R3*R4*R5*ro_Q1*rpi_Q1*s^4+3579497323941000001*C1*C2*C3*R1*R2*R3*R4*R5*ro_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R2*R3*R4*R5*ro_Q1*s^3+357913941*C1*C2*C3*R1*R2*R3*R5*R6*ro_Q1*rpi_Q1*s^5+3579139410000000000*C1*C2*C3*R1*R2*R3*R5*R6*ro_Q1*rpi_Q1*s^4+3579497323941000001*C1*C2*C3*R1*R2*R3*R5*R6*ro_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R2*R3*R5*R6*ro_Q1*s^3+3579497323941000001*C1*C2*C3*R1*R2*R4*R5*R6*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R2*R4*R5*R6*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R1*R2*R4*R5*ro_Q1*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R2*R4*R5*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R1*R2*R5*R6*ro_Q1*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R2*R5*R6*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R1*R3*R4*R5*R6*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R3*R4*R5*R6*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R1*R3*R4*R5*ro_Q1*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R3*R4*R5*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R1*R3*R5*R6*ro_Q1*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R1*R3*R5*R6*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R2*R3*R4*R5*R6*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R2*R3*R4*R5*R6*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R2*R3*R4*R5*ro_Q1*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R2*R3*R4*R5*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C2*C3*R2*R3*R5*R6*ro_Q1*rpi_Q1*s^4+3579139410000010000000000*C1*C2*C3*R2*R3*R5*R6*ro_Q1*rpi_Q1*s^3+357913941*C1*C2*R1*R2*R3*R4*R5*rpi_Q1*s^4+3579139410000000000*C1*C2*R1*R2*R3*R4*R5*rpi_Q1*s^3+3579497323941000001*C1*C2*R1*R2*R3*R4*R5*s^3+3579139410000010000000000*C1*C2*R1*R2*R3*R4*R5*s^2+357913941*C1*C2*R1*R2*R3*R5*ro_Q1*rpi_Q1*s^4+3579139410000000000*C1*C2*R1*R2*R3*R5*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C2*R1*R2*R3*R5*ro_Q1*s^3+3579139410000010000000000*C1*C2*R1*R2*R3*R5*ro_Q1*s^2+3579497323941000001*C1*C2*R1*R2*R4*R5*rpi_Q1*s^3+3579139410000010000000000*C1*C2*R1*R2*R4*R5*rpi_Q1*s^2+3579497323941000001*C1*C2*R1*R2*R5*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C2*R1*R2*R5*ro_Q1*rpi_Q1*s^2+3579497323941000001*C1*C2*R1*R3*R4*R5*rpi_Q1*s^3+3579139410000010000000000*C1*C2*R1*R3*R4*R5*rpi_Q1*s^2+3579497323941000001*C1*C2*R1*R3*R5*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C2*R1*R3*R5*ro_Q1*rpi_Q1*s^2+3579497323941000001*C1*C2*R2*R3*R4*R5*rpi_Q1*s^3+3579139410000010000000000*C1*C2*R2*R3*R4*R5*rpi_Q1*s^2+3579497323941000001*C1*C2*R2*R3*R5*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C2*R2*R3*R5*ro_Q1*rpi_Q1*s^2-357913941*C1*C3*R1*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^4-3579139410000000000*C1*C3*R1*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^3+357913941*C1*C3*R1*R2*R3*R4*R5*R6*s^4+3579139410000000000*C1*C3*R1*R2*R3*R4*R5*R6*s^3+357913941*C1*C3*R1*R2*R3*R4*R5*ro_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R4*R5*ro_Q1*s^3+357913941*C1*C3*R1*R2*R3*R4*R5*rpi_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R4*R5*rpi_Q1*s^3+3579497323941000001*C1*C3*R1*R2*R3*R4*R5*s^3+3579139410000010000000000*C1*C3*R1*R2*R3*R4*R5*s^2+357913941*C1*C3*R1*R2*R3*R4*R6*rpi_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R4*R6*rpi_Q1*s^3+3579497323941000001*C1*C3*R1*R2*R3*R4*R6*s^3+3579139410000010000000000*C1*C3*R1*R2*R3*R4*R6*s^2+357913941*C1*C3*R1*R2*R3*R4*ro_Q1*rpi_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R4*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C3*R1*R2*R3*R4*ro_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R3*R4*ro_Q1*s^2+357913941*C1*C3*R1*R2*R3*R5*R6*ro_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R5*R6*ro_Q1*s^3+357913941*C1*C3*R1*R2*R3*R5*R6*rpi_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R5*R6*rpi_Q1*s^3+3579497323941000001*C1*C3*R1*R2*R3*R5*R6*s^3+3579139410000010000000000*C1*C3*R1*R2*R3*R5*R6*s^2+357913941*C1*C3*R1*R2*R3*R6*ro_Q1*rpi_Q1*s^4+3579139410000000000*C1*C3*R1*R2*R3*R6*ro_Q1*rpi_Q1*s^3+3579497323941000001*C1*C3*R1*R2*R3*R6*ro_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R3*R6*ro_Q1*s^2-3579497323941000001*C1*C3*R1*R2*R4*R5*R6*gm_Q1*rpi_Q1*s^3-3579139410000010000000000*C1*C3*R1*R2*R4*R5*R6*gm_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R4*R5*R6*s^3+3579139410000010000000000*C1*C3*R1*R2*R4*R5*R6*s^2+3579497323941000001*C1*C3*R1*R2*R4*R5*ro_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R4*R5*ro_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R4*R5*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R4*R5*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R4*R6*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R4*R6*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R4*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R4*ro_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R5*R6*ro_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R5*R6*ro_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R5*R6*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R5*R6*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R2*R6*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R2*R6*ro_Q1*rpi_Q1*s^2-3579497323941000001*C1*C3*R1*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^3-3579139410000010000000000*C1*C3*R1*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R4*R5*R6*s^3+3579139410000010000000000*C1*C3*R1*R3*R4*R5*R6*s^2+3579497323941000001*C1*C3*R1*R3*R4*R5*ro_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R4*R5*ro_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R4*R5*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R4*R5*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R4*R6*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R4*R6*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R4*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R4*ro_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R5*R6*ro_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R5*R6*ro_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R5*R6*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R5*R6*rpi_Q1*s^2+3579497323941000001*C1*C3*R1*R3*R6*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R1*R3*R6*ro_Q1*rpi_Q1*s^2-3579497323941000001*C1*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^3-3579139410000010000000000*C1*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R4*R5*R6*s^3+3579139410000010000000000*C1*C3*R2*R3*R4*R5*R6*s^2+3579497323941000001*C1*C3*R2*R3*R4*R5*ro_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R4*R5*ro_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R4*R5*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R4*R5*rpi_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R4*R6*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R4*R6*rpi_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R4*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R4*ro_Q1*rpi_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R5*R6*ro_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R5*R6*ro_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R5*R6*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R5*R6*rpi_Q1*s^2+3579497323941000001*C1*C3*R2*R3*R6*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C1*C3*R2*R3*R6*ro_Q1*rpi_Q1*s^2-357913941*C1*R1*R2*R3*R4*R5*gm_Q1*rpi_Q1*s^3-3579139410000000000*C1*R1*R2*R3*R4*R5*gm_Q1*rpi_Q1*s^2+357913941*C1*R1*R2*R3*R4*R5*s^3+3579139410000000000*C1*R1*R2*R3*R4*R5*s^2+357913941*C1*R1*R2*R3*R4*rpi_Q1*s^3+3579139410000000000*C1*R1*R2*R3*R4*rpi_Q1*s^2+3579497323941000001*C1*R1*R2*R3*R4*s^2+3579139410000010000000000*C1*R1*R2*R3*R4*s+357913941*C1*R1*R2*R3*R5*ro_Q1*s^3+3579139410000000000*C1*R1*R2*R3*R5*ro_Q1*s^2+357913941*C1*R1*R2*R3*R5*rpi_Q1*s^3+3579139410000000000*C1*R1*R2*R3*R5*rpi_Q1*s^2+3579497323941000001*C1*R1*R2*R3*R5*s^2+3579139410000010000000000*C1*R1*R2*R3*R5*s+357913941*C1*R1*R2*R3*ro_Q1*rpi_Q1*s^3+3579139410000000000*C1*R1*R2*R3*ro_Q1*rpi_Q1*s^2+3579497323941000001*C1*R1*R2*R3*ro_Q1*s^2+3579139410000010000000000*C1*R1*R2*R3*ro_Q1*s-3579497323941000001*C1*R1*R2*R4*R5*gm_Q1*rpi_Q1*s^2-3579139410000010000000000*C1*R1*R2*R4*R5*gm_Q1*rpi_Q1*s+3579497323941000001*C1*R1*R2*R4*R5*s^2+3579139410000010000000000*C1*R1*R2*R4*R5*s+3579497323941000001*C1*R1*R2*R4*rpi_Q1*s^2+3579139410000010000000000*C1*R1*R2*R4*rpi_Q1*s+3579497323941000001*C1*R1*R2*R5*ro_Q1*s^2+3579139410000010000000000*C1*R1*R2*R5*ro_Q1*s+3579497323941000001*C1*R1*R2*R5*rpi_Q1*s^2+3579139410000010000000000*C1*R1*R2*R5*rpi_Q1*s+3579497323941000001*C1*R1*R2*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C1*R1*R2*ro_Q1*rpi_Q1*s-3579497323941000001*C1*R1*R3*R4*R5*gm_Q1*rpi_Q1*s^2-3579139410000010000000000*C1*R1*R3*R4*R5*gm_Q1*rpi_Q1*s+3579497323941000001*C1*R1*R3*R4*R5*s^2+3579139410000010000000000*C1*R1*R3*R4*R5*s+3579497323941000001*C1*R1*R3*R4*rpi_Q1*s^2+3579139410000010000000000*C1*R1*R3*R4*rpi_Q1*s+3579497323941000001*C1*R1*R3*R5*ro_Q1*s^2+3579139410000010000000000*C1*R1*R3*R5*ro_Q1*s+3579497323941000001*C1*R1*R3*R5*rpi_Q1*s^2+3579139410000010000000000*C1*R1*R3*R5*rpi_Q1*s+3579497323941000001*C1*R1*R3*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C1*R1*R3*ro_Q1*rpi_Q1*s-3579497323941000001*C1*R2*R3*R4*R5*gm_Q1*rpi_Q1*s^2-3579139410000010000000000*C1*R2*R3*R4*R5*gm_Q1*rpi_Q1*s+3579497323941000001*C1*R2*R3*R4*R5*s^2+3579139410000010000000000*C1*R2*R3*R4*R5*s+3579497323941000001*C1*R2*R3*R4*rpi_Q1*s^2+3579139410000010000000000*C1*R2*R3*R4*rpi_Q1*s+3579497323941000001*C1*R2*R3*R5*ro_Q1*s^2+3579139410000010000000000*C1*R2*R3*R5*ro_Q1*s+3579497323941000001*C1*R2*R3*R5*rpi_Q1*s^2+3579139410000010000000000*C1*R2*R3*R5*rpi_Q1*s+3579497323941000001*C1*R2*R3*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C1*R2*R3*ro_Q1*rpi_Q1*s+357913941*C2*C3*R2*R3*R4*R5*R6*rpi_Q1*s^4+3579139410000000000*C2*C3*R2*R3*R4*R5*R6*rpi_Q1*s^3+3579497323941000001*C2*C3*R2*R3*R4*R5*R6*s^3+3579139410000010000000000*C2*C3*R2*R3*R4*R5*R6*s^2+357913941*C2*C3*R2*R3*R4*R5*ro_Q1*rpi_Q1*s^4+3579139410000000000*C2*C3*R2*R3*R4*R5*ro_Q1*rpi_Q1*s^3+3579497323941000001*C2*C3*R2*R3*R4*R5*ro_Q1*s^3+3579139410000010000000000*C2*C3*R2*R3*R4*R5*ro_Q1*s^2+357913941*C2*C3*R2*R3*R5*R6*ro_Q1*rpi_Q1*s^4+3579139410000000000*C2*C3*R2*R3*R5*R6*ro_Q1*rpi_Q1*s^3+3579497323941000001*C2*C3*R2*R3*R5*R6*ro_Q1*s^3+3579139410000010000000000*C2*C3*R2*R3*R5*R6*ro_Q1*s^2+3579497323941000001*C2*C3*R2*R4*R5*R6*rpi_Q1*s^3+3579139410000010000000000*C2*C3*R2*R4*R5*R6*rpi_Q1*s^2+3579497323941000001*C2*C3*R2*R4*R5*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C2*C3*R2*R4*R5*ro_Q1*rpi_Q1*s^2+3579497323941000001*C2*C3*R2*R5*R6*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C2*C3*R2*R5*R6*ro_Q1*rpi_Q1*s^2+3579497323941000001*C2*C3*R3*R4*R5*R6*rpi_Q1*s^3+3579139410000010000000000*C2*C3*R3*R4*R5*R6*rpi_Q1*s^2+3579497323941000001*C2*C3*R3*R4*R5*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C2*C3*R3*R4*R5*ro_Q1*rpi_Q1*s^2+3579497323941000001*C2*C3*R3*R5*R6*ro_Q1*rpi_Q1*s^3+3579139410000010000000000*C2*C3*R3*R5*R6*ro_Q1*rpi_Q1*s^2+357913941*C2*R2*R3*R4*R5*rpi_Q1*s^3+3579139410000000000*C2*R2*R3*R4*R5*rpi_Q1*s^2+3579497323941000001*C2*R2*R3*R4*R5*s^2+3579139410000010000000000*C2*R2*R3*R4*R5*s+357913941*C2*R2*R3*R5*ro_Q1*rpi_Q1*s^3+3579139410000000000*C2*R2*R3*R5*ro_Q1*rpi_Q1*s^2+3579497323941000001*C2*R2*R3*R5*ro_Q1*s^2+3579139410000010000000000*C2*R2*R3*R5*ro_Q1*s+3579497323941000001*C2*R2*R4*R5*rpi_Q1*s^2+3579139410000010000000000*C2*R2*R4*R5*rpi_Q1*s+3579497323941000001*C2*R2*R5*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C2*R2*R5*ro_Q1*rpi_Q1*s+3579497323941000001*C2*R3*R4*R5*rpi_Q1*s^2+3579139410000010000000000*C2*R3*R4*R5*rpi_Q1*s+3579497323941000001*C2*R3*R5*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C2*R3*R5*ro_Q1*rpi_Q1*s-357913941*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^3-3579139410000000000*C3*R2*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^2+357913941*C3*R2*R3*R4*R5*R6*s^3+3579139410000000000*C3*R2*R3*R4*R5*R6*s^2+357913941*C3*R2*R3*R4*R5*ro_Q1*s^3+3579139410000000000*C3*R2*R3*R4*R5*ro_Q1*s^2+357913941*C3*R2*R3*R4*R5*rpi_Q1*s^3+3579139410000000000*C3*R2*R3*R4*R5*rpi_Q1*s^2+3579497323941000001*C3*R2*R3*R4*R5*s^2+3579139410000010000000000*C3*R2*R3*R4*R5*s+357913941*C3*R2*R3*R4*R6*rpi_Q1*s^3+3579139410000000000*C3*R2*R3*R4*R6*rpi_Q1*s^2+3579497323941000001*C3*R2*R3*R4*R6*s^2+3579139410000010000000000*C3*R2*R3*R4*R6*s+357913941*C3*R2*R3*R4*ro_Q1*rpi_Q1*s^3+3579139410000000000*C3*R2*R3*R4*ro_Q1*rpi_Q1*s^2+3579497323941000001*C3*R2*R3*R4*ro_Q1*s^2+3579139410000010000000000*C3*R2*R3*R4*ro_Q1*s+357913941*C3*R2*R3*R5*R6*ro_Q1*s^3+3579139410000000000*C3*R2*R3*R5*R6*ro_Q1*s^2+357913941*C3*R2*R3*R5*R6*rpi_Q1*s^3+3579139410000000000*C3*R2*R3*R5*R6*rpi_Q1*s^2+3579497323941000001*C3*R2*R3*R5*R6*s^2+3579139410000010000000000*C3*R2*R3*R5*R6*s+357913941*C3*R2*R3*R6*ro_Q1*rpi_Q1*s^3+3579139410000000000*C3*R2*R3*R6*ro_Q1*rpi_Q1*s^2+3579497323941000001*C3*R2*R3*R6*ro_Q1*s^2+3579139410000010000000000*C3*R2*R3*R6*ro_Q1*s-3579497323941000001*C3*R2*R4*R5*R6*gm_Q1*rpi_Q1*s^2-3579139410000010000000000*C3*R2*R4*R5*R6*gm_Q1*rpi_Q1*s+3579497323941000001*C3*R2*R4*R5*R6*s^2+3579139410000010000000000*C3*R2*R4*R5*R6*s+3579497323941000001*C3*R2*R4*R5*ro_Q1*s^2+3579139410000010000000000*C3*R2*R4*R5*ro_Q1*s+3579497323941000001*C3*R2*R4*R5*rpi_Q1*s^2+3579139410000010000000000*C3*R2*R4*R5*rpi_Q1*s+3579497323941000001*C3*R2*R4*R6*rpi_Q1*s^2+3579139410000010000000000*C3*R2*R4*R6*rpi_Q1*s+3579497323941000001*C3*R2*R4*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C3*R2*R4*ro_Q1*rpi_Q1*s+3579497323941000001*C3*R2*R5*R6*ro_Q1*s^2+3579139410000010000000000*C3*R2*R5*R6*ro_Q1*s+3579497323941000001*C3*R2*R5*R6*rpi_Q1*s^2+3579139410000010000000000*C3*R2*R5*R6*rpi_Q1*s+3579497323941000001*C3*R2*R6*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C3*R2*R6*ro_Q1*rpi_Q1*s-3579497323941000001*C3*R3*R4*R5*R6*gm_Q1*rpi_Q1*s^2-3579139410000010000000000*C3*R3*R4*R5*R6*gm_Q1*rpi_Q1*s+3579497323941000001*C3*R3*R4*R5*R6*s^2+3579139410000010000000000*C3*R3*R4*R5*R6*s+3579497323941000001*C3*R3*R4*R5*ro_Q1*s^2+3579139410000010000000000*C3*R3*R4*R5*ro_Q1*s+3579497323941000001*C3*R3*R4*R5*rpi_Q1*s^2+3579139410000010000000000*C3*R3*R4*R5*rpi_Q1*s+3579497323941000001*C3*R3*R4*R6*rpi_Q1*s^2+3579139410000010000000000*C3*R3*R4*R6*rpi_Q1*s+3579497323941000001*C3*R3*R4*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C3*R3*R4*ro_Q1*rpi_Q1*s+3579497323941000001*C3*R3*R5*R6*ro_Q1*s^2+3579139410000010000000000*C3*R3*R5*R6*ro_Q1*s+3579497323941000001*C3*R3*R5*R6*rpi_Q1*s^2+3579139410000010000000000*C3*R3*R5*R6*rpi_Q1*s+3579497323941000001*C3*R3*R6*ro_Q1*rpi_Q1*s^2+3579139410000010000000000*C3*R3*R6*ro_Q1*rpi_Q1*s-357913941*R2*R3*R4*R5*gm_Q1*rpi_Q1*s^2-3579139410000000000*R2*R3*R4*R5*gm_Q1*rpi_Q1*s+357913941*R2*R3*R4*R5*s^2+3579139410000000000*R2*R3*R4*R5*s+357913941*R2*R3*R4*rpi_Q1*s^2+3579139410000000000*R2*R3*R4*rpi_Q1*s+3579497323941000001*R2*R3*R4*s+3579139410000010000000000*R2*R3*R4+357913941*R2*R3*R5*ro_Q1*s^2+3579139410000000000*R2*R3*R5*ro_Q1*s+357913941*R2*R3*R5*rpi_Q1*s^2+3579139410000000000*R2*R3*R5*rpi_Q1*s+3579497323941000001*R2*R3*R5*s+3579139410000010000000000*R2*R3*R5+357913941*R2*R3*ro_Q1*rpi_Q1*s^2+3579139410000000000*R2*R3*ro_Q1*rpi_Q1*s+3579497323941000001*R2*R3*ro_Q1*s+3579139410000010000000000*R2*R3*ro_Q1-3579497323941000001*R2*R4*R5*gm_Q1*rpi_Q1*s-3579139410000010000000000*R2*R4*R5*gm_Q1*rpi_Q1+3579497323941000001*R2*R4*R5*s+3579139410000010000000000*R2*R4*R5+3579497323941000001*R2*R4*rpi_Q1*s+3579139410000010000000000*R2*R4*rpi_Q1+3579497323941000001*R2*R5*ro_Q1*s+3579139410000010000000000*R2*R5*ro_Q1+3579497323941000001*R2*R5*rpi_Q1*s+3579139410000010000000000*R2*R5*rpi_Q1+3579497323941000001*R2*ro_Q1*rpi_Q1*s+3579139410000010000000000*R2*ro_Q1*rpi_Q1-3579497323941000001*R3*R4*R5*gm_Q1*rpi_Q1*s-3579139410000010000000000*R3*R4*R5*gm_Q1*rpi_Q1+3579497323941000001*R3*R4*R5*s+3579139410000010000000000*R3*R4*R5+3579497323941000001*R3*R4*rpi_Q1*s+3579139410000010000000000*R3*R4*rpi_Q1+3579497323941000001*R3*R5*ro_Q1*s+3579139410000010000000000*R3*R5*ro_Q1+3579497323941000001*R3*R5*rpi_Q1*s+3579139410000010000000000*R3*R5*rpi_Q1+3579497323941000001*R3*ro_Q1*rpi_Q1*s+3579139410000010000000000*R3*ro_Q1*rpi_Q1)

Fonction de transfert finale: (-1.908874352e+25*s^4-9.54532619716e+22*s^4-9.54437176e+28*s^3-5.726623056e+25*s^3+5.726623056e+23*s^3-1.908874352e+27*s^3-2.86359785915e+23*s^3-2.863311528e+29*s^2+2.86359785915e+21*s^3+2.863311528e+27*s^2-9.54532619716e+24*s^3-9.54437176e+30*s^2-1.908874352e+30*s^3-5.726623056e+30*s^2+5.726623056e+28*s^2-1.908874352e+32*s^2)/(238609294000*s^5+2.38609294e+21*s^4+2.86359785915e+18*s^4+2.863311528e+24*s^3+9.94205391666e+12*s^5+9.94205391666e+22*s^4+1.19316577465e+20*s^4+1.19304647e+26*s^3+7.95364313332e+12*s^5+7.95364313332e+22*s^4+9.54532619716e+19*s^4+9.54437175999e+25*s^3+2.38633154929e+17*s^4+2.38609294e+23*s^3+9.94304812208e+18*s^4+9.94205391666e+24*s^3+7.95443849763e+18*s^4+7.95364313332e+24*s^3+2.38633154929e+17*s^4+2.38609294e+23*s^3+9.94304812208e+18*s^4+9.94205391666e+24*s^3+7.95443849763e+18*s^4+7.95364313332e+24*s^3+2.38633154929e+19*s^4+2.38609294e+25*s^3+9.94304812208e+20*s^4+9.94205391666e+26*s^3+7.95443849763e+20*s^4+7.95364313332e+26*s^3+2.982616175e+15*s^4+2.982616175e+25*s^3+3.57949732394e+22*s^3+3.57913941e+28*s^2+9.94205391666e+16*s^4+9.94205391666e+26*s^3+1.19316577465e+24*s^3+1.19304647e+30*s^2+2.98291443662e+21*s^3+2.982616175e+27*s^2+9.94304812208e+22*s^3+9.94205391666e+28*s^2+2.98291443662e+21*s^3+2.982616175e+27*s^2+9.94304812208e+22*s^3+9.94205391666e+28*s^2+2.98291443662e+23*s^3+2.982616175e+29*s^2+9.94304812208e+24*s^3+9.94205391666e+30*s^2-2.863311528e+15*s^4-2.863311528e+25*s^3+2.863311528e+13*s^4+2.863311528e+23*s^3+1.19304647e+15*s^4+1.19304647e+25*s^3+2.982616175e+13*s^4+2.982616175e+23*s^3+3.57949732394e+20*s^3+3.57913941e+26*s^2+2.38609294e+13*s^4+2.38609294e+23*s^3+2.86359785915e+20*s^3+2.863311528e+26*s^2+9.94205391666e+14*s^4+9.94205391666e+24*s^3+1.19316577465e+22*s^3+1.19304647e+28*s^2+9.54437175999e+14*s^4+9.54437175999e+24*s^3+2.38609294e+13*s^4+2.38609294e+23*s^3+2.86359785915e+20*s^3+2.863311528e+26*s^2+7.95364313332e+14*s^4+7.95364313332e+24*s^3+9.54532619716e+21*s^3+9.54437175999e+27*s^2-2.86359785915e+21*s^3-2.863311528e+27*s^2+2.86359785915e+19*s^3+2.863311528e+25*s^2+1.19316577465e+21*s^3+1.19304647e+27*s^2+2.98291443662e+19*s^3+2.982616175e+25*s^2+2.38633154929e+19*s^3+2.38609294e+25*s^2+9.94304812208e+20*s^3+9.94205391666e+26*s^2+9.54532619716e+20*s^3+9.54437175999e+26*s^2+2.38633154929e+19*s^3+2.38609294e+25*s^2+7.95443849763e+20*s^3+7.95364313332e+26*s^2-2.86359785915e+21*s^3-2.863311528e+27*s^2+2.86359785915e+19*s^3+2.863311528e+25*s^2+1.19316577465e+21*s^3+1.19304647e+27*s^2+2.98291443662e+19*s^3+2.982616175e+25*s^2+2.38633154929e+19*s^3+2.38609294e+25*s^2+9.94304812208e+20*s^3+9.94205391666e+26*s^2+9.54532619716e+20*s^3+9.54437175999e+26*s^2+2.38633154929e+19*s^3+2.38609294e+25*s^2+7.95443849763e+20*s^3+7.95364313332e+26*s^2-2.86359785915e+23*s^3-2.863311528e+29*s^2+2.86359785915e+21*s^3+2.863311528e+27*s^2+1.19316577465e+23*s^3+1.19304647e+29*s^2+2.98291443662e+21*s^3+2.982616175e+27*s^2+2.38633154929e+21*s^3+2.38609294e+27*s^2+9.94304812208e+22*s^3+9.94205391666e+28*s^2+9.54532619716e+22*s^3+9.54437175999e+28*s^2+2.38633154929e+21*s^3+2.38609294e+27*s^2+7.95443849763e+22*s^3+7.95364313332e+28*s^2-3.57913941e+19*s^3-3.57913941e+29*s^2+3.57913941e+17*s^3+3.57913941e+27*s^2+2.982616175e+17*s^3+2.982616175e+27*s^2+3.57949732394e+24*s^2+3.57913941e+30*s+1.19304647e+19*s^3+1.19304647e+29*s^2+2.982616175e+17*s^3+2.982616175e+27*s^2+3.57949732394e+24*s^2+3.57913941e+30*s+9.94205391666e+18*s^3+9.94205391666e+28*s^2+1.19316577465e+26*s^2+1.19304647e+32*s-3.57949732394e+25*s^2-3.57913941e+31*s+3.57949732394e+23*s^2+3.57913941e+29*s+2.98291443662e+23*s^2+2.982616175e+29*s+1.19316577465e+25*s^2+1.19304647e+31*s+2.98291443662e+23*s^2+2.982616175e+29*s+9.94304812208e+24*s^2+9.94205391666e+30*s-3.57949732394e+25*s^2-3.57913941e+31*s+3.57949732394e+23*s^2+3.57913941e+29*s+2.98291443662e+23*s^2+2.982616175e+29*s+1.19316577465e+25*s^2+1.19304647e+31*s+2.98291443662e+23*s^2+2.982616175e+29*s+9.94304812208e+24*s^2+9.94205391666e+30*s-3.57949732394e+27*s^2-3.57913941e+33*s+3.57949732394e+25*s^2+3.57913941e+31*s+2.98291443662e+25*s^2+2.982616175e+31*s+1.19316577465e+27*s^2+1.19304647e+33*s+2.98291443662e+25*s^2+2.982616175e+31*s+9.94304812208e+26*s^2+9.94205391666e+32*s+2.38609294e+16*s^4+2.38609294e+26*s^3+2.86359785915e+23*s^3+2.863311528e+29*s^2+9.94205391666e+17*s^4+9.94205391666e+27*s^3+1.19316577465e+25*s^3+1.19304647e+31*s^2+7.95364313332e+17*s^4+7.95364313332e+27*s^3+9.54532619716e+24*s^3+9.54437175999e+30*s^2+2.38633154929e+22*s^3+2.38609294e+28*s^2+9.94304812208e+23*s^3+9.94205391666e+29*s^2+7.95443849763e+23*s^3+7.95364313332e+29*s^2+2.38633154929e+22*s^3+2.38609294e+28*s^2+9.94304812208e+23*s^3+9.94205391666e+29*s^2+7.95443849763e+23*s^3+7.95364313332e+29*s^2+2.982616175e+20*s^3+2.982616175e+30*s^2+3.57949732394e+27*s^2+3.57913941e+33*s+9.94205391666e+21*s^3+9.94205391666e+31*s^2+1.19316577465e+29*s^2+1.19304647e+35*s+2.98291443662e+26*s^2+2.982616175e+32*s+9.94304812208e+27*s^2+9.94205391666e+33*s+2.98291443662e+26*s^2+2.982616175e+32*s+9.94304812208e+27*s^2+9.94205391666e+33*s-2.863311528e+20*s^3-2.863311528e+30*s^2+2.863311528e+18*s^3+2.863311528e+28*s^2+1.19304647e+20*s^3+1.19304647e+30*s^2+2.982616175e+18*s^3+2.982616175e+28*s^2+3.57949732394e+25*s^2+3.57913941e+31*s+2.38609294e+18*s^3+2.38609294e+28*s^2+2.86359785915e+25*s^2+2.863311528e+31*s+9.94205391666e+19*s^3+9.94205391666e+29*s^2+1.19316577465e+27*s^2+1.19304647e+33*s+9.54437175999e+19*s^3+9.54437175999e+29*s^2+2.38609294e+18*s^3+2.38609294e+28*s^2+2.86359785915e+25*s^2+2.863311528e+31*s+7.95364313332e+19*s^3+7.95364313332e+29*s^2+9.54532619716e+26*s^2+9.54437175999e+32*s-2.86359785915e+26*s^2-2.863311528e+32*s+2.86359785915e+24*s^2+2.863311528e+30*s+1.19316577465e+26*s^2+1.19304647e+32*s+2.98291443662e+24*s^2+2.982616175e+30*s+2.38633154929e+24*s^2+2.38609294e+30*s+9.94304812208e+25*s^2+9.94205391666e+31*s+9.54532619716e+25*s^2+9.54437175999e+31*s+2.38633154929e+24*s^2+2.38609294e+30*s+7.95443849763e+25*s^2+7.95364313332e+31*s-2.86359785915e+26*s^2-2.863311528e+32*s+2.86359785915e+24*s^2+2.863311528e+30*s+1.19316577465e+26*s^2+1.19304647e+32*s+2.98291443662e+24*s^2+2.982616175e+30*s+2.38633154929e+24*s^2+2.38609294e+30*s+9.94304812208e+25*s^2+9.94205391666e+31*s+9.54532619716e+25*s^2+9.54437175999e+31*s+2.38633154929e+24*s^2+2.38609294e+30*s+7.95443849763e+25*s^2+7.95364313332e+31*s-3.57913941e+24*s^2-3.57913941e+34*s+3.57913941e+22*s^2+3.57913941e+32*s+2.982616175e+22*s^2+2.982616175e+32*s+3.57949732394e+29*s+3.57913941e+35+1.19304647e+24*s^2+1.19304647e+34*s+2.982616175e+22*s^2+2.982616175e+32*s+3.57949732394e+29*s+3.57913941e+35+9.94205391666e+23*s^2+9.94205391666e+33*s+1.19316577465e+31*s+1.19304647e+37-3.57949732394e+30*s-3.57913941e+36+3.57949732394e+28*s+3.57913941e+34+2.98291443662e+28*s+2.982616175e+34+1.19316577465e+30*s+1.19304647e+36+2.98291443662e+28*s+2.982616175e+34+9.94304812208e+29*s+9.94205391666e+35-3.57949732394e+30*s-3.57913941e+36+3.57949732394e+28*s+3.57913941e+34+2.98291443662e+28*s+2.982616175e+34+1.19316577465e+30*s+1.19304647e+36+2.98291443662e+28*s+2.982616175e+34+9.94304812208e+29*s+9.94205391666e+35)

Substitutions numeriques appliquees:

  ro_Q1 = 33333.3333333

  R1 = 100.0

  C1 = 1e-07

  R2 = 10000.0

  R3 = 10000.0

  R4 = 1000.0

  R5 = 1000.0

  C2 = 1e-05

  C3 = 1e-07

  R6 = 800.0

  gm_Q1 = 0.12

  rpi_Q1 = 833.333333333