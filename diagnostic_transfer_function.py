#!/usr/bin/env python3
"""
Diagnostic pour comparer les fonctions de transfert avant/après modification
"""
import sys
import os

def restore_original_display():
    """Restaure temporairement l'affichage original pour comparaison"""
    
    print("=== RESTAURATION TEMPORAIRE DE L'AFFICHAGE ORIGINAL ===")
    
    # Lire le fichier actuel
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Sauvegarder la version actuelle
    with open('test_current.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    # Restaurer l'affichage des deux fonctions
    old_line = 'file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function_with_fc_values}")'
    new_lines = '''file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function_with_fc_values}")
                                file_print(f"Fonction symbolique pure (sans substitutions): {transfer_function}")'''
    
    restored_content = content.replace(old_line, new_lines)
    
    # Sauvegarder la version restaurée
    with open('test_restored.py', 'w', encoding='utf-8') as f:
        f.write(restored_content)
    
    print("✓ Version actuelle sauvegardée: test_current.py")
    print("✓ Version restaurée créée: test_restored.py")

def test_both_versions():
    """Teste les deux versions avec le même circuit"""
    
    print("\n=== TEST DES DEUX VERSIONS ===")
    
    # Circuit de test
    test_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit de test:")
    print(test_circuit)
    print()
    
    # Test de la version actuelle
    print("1. TEST DE LA VERSION ACTUELLE:")
    test_version("test_current.py", test_circuit, "actuelle")
    
    print("\n2. TEST DE LA VERSION RESTAURÉE:")
    test_version("test_restored.py", test_circuit, "restauree")

def test_version(test_file, circuit, version_name):
    """Teste une version spécifique"""
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

# Remplacer test.py par la version à tester
import shutil
shutil.copy("{test_file}", "test.py")

try:
    from test import solve_circuit
    
    netlist_str = """{circuit}"""
    
    print("=== Test version {version_name} ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "7", 1.0, 1e12, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    script_name = f'run_test_{version_name}.py'
    with open(script_name, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        import subprocess
        result = subprocess.run(['python', script_name], 
                              capture_output=True, text=True, timeout=120)
        
        output = result.stdout
        error = result.stderr
        
        print(f"Sortie version {version_name}:")
        print("-" * 50)
        
        # Extraire les fonctions de transfert
        lines = output.split('\n')
        for line in lines:
            if "H1(s) = V7/V1 =" in line:
                print(f"H1(s) trouvée: {line}")
            elif "Fonction symbolique pure" in line:
                print(f"Fonction pure: {line}")
            elif "Fonction de transfert finale:" in line:
                print(f"Fonction finale: {line}")
            elif "ATTENTION" in line or "OK Coherence" in line:
                print(f"Message: {line}")
        
        print("-" * 50)
        
        if error:
            print(f"Erreurs: {error}")
        
    except subprocess.TimeoutExpired:
        print(f"❌ Test version {version_name} interrompu (timeout)")
    except Exception as e:
        print(f"❌ Erreur test version {version_name}: {e}")
    finally:
        # Nettoyer
        if os.path.exists(script_name):
            os.remove(script_name)

def analyze_differences():
    """Analyse les différences entre les versions"""
    
    print("\n=== ANALYSE DES DIFFÉRENCES ===")
    
    # Comparer les fichiers
    try:
        with open('test_current.py', 'r', encoding='utf-8') as f:
            current_content = f.read()
        
        with open('test_restored.py', 'r', encoding='utf-8') as f:
            restored_content = f.read()
        
        # Trouver les différences dans plot_bode
        current_plot_bode = extract_plot_bode_function(current_content)
        restored_plot_bode = extract_plot_bode_function(restored_content)
        
        print("Différences dans plot_bode:")
        if current_plot_bode == restored_plot_bode:
            print("✓ Fonction plot_bode identique")
        else:
            print("❌ Fonction plot_bode différente")
        
        # Trouver les différences dans calculate_transfer_function
        current_calc = extract_calculate_function(current_content)
        restored_calc = extract_calculate_function(restored_content)
        
        print("Différences dans calculate_transfer_function:")
        if current_calc == restored_calc:
            print("❌ Fonction calculate_transfer_function identique - le problème est ailleurs")
        else:
            print("✓ Fonction calculate_transfer_function différente - voici le problème")
            print("Différence trouvée dans l'affichage des fonctions de transfert")
        
    except Exception as e:
        print(f"Erreur lors de l'analyse: {e}")

def extract_plot_bode_function(content):
    """Extrait la fonction plot_bode du contenu"""
    import re
    match = re.search(r'def plot_bode\(.*?\n(?=def |\nif __name__|$)', content, re.DOTALL)
    return match.group(0) if match else ""

def extract_calculate_function(content):
    """Extrait la fonction calculate_transfer_function du contenu"""
    import re
    match = re.search(r'def calculate_transfer_function\(.*?\n(?=                    step|$)', content, re.DOTALL)
    return match.group(0) if match else ""

def restore_current_version():
    """Restaure la version actuelle"""
    
    print("\n=== RESTAURATION DE LA VERSION ACTUELLE ===")
    
    try:
        import shutil
        shutil.copy('test_current.py', 'test.py')
        print("✓ Version actuelle restaurée")
        
        # Nettoyer les fichiers temporaires
        for file in ['test_current.py', 'test_restored.py']:
            if os.path.exists(file):
                os.remove(file)
        
    except Exception as e:
        print(f"Erreur lors de la restauration: {e}")

def main():
    """Fonction principale"""
    
    print("DIAGNOSTIC DES FONCTIONS DE TRANSFERT")
    print("=" * 50)
    
    # Restaurer l'affichage original pour comparaison
    restore_original_display()
    
    # Tester les deux versions
    test_both_versions()
    
    # Analyser les différences
    analyze_differences()
    
    # Restaurer la version actuelle
    restore_current_version()
    
    print("\n" + "=" * 50)
    print("CONCLUSION")
    print("=" * 50)
    print("Comparez les sorties des deux versions ci-dessus.")
    print("Si les fonctions H1(s) sont différentes, le problème vient de la modification.")
    print("Si elles sont identiques, le problème vient d'ailleurs.")

if __name__ == "__main__":
    main()
