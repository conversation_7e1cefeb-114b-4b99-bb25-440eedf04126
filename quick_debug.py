#!/usr/bin/env python3
"""
Debug rapide et direct du problème de cohérence
"""
import sys
import re

def analyze_plot_bode_function():
    """Analyse la fonction plot_bode pour identifier le problème"""
    
    print("=== ANALYSE DE LA FONCTION plot_bode ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extraire la fonction plot_bode
    plot_bode_match = re.search(r'def plot_bode\(.*?\n(?=def |\nif __name__|$)', content, re.DOTALL)
    
    if plot_bode_match:
        plot_bode_code = plot_bode_match.group(0)
        
        print("Fonction plot_bode trouvée")
        print(f"Longueur: {len(plot_bode_code)} caractères")
        
        # Analyser les étapes clés
        steps = [
            ("force_fc_substitution", "force_fc_substitution"),
            ("Fonction symbolique", "Fonction de transfert symbolique"),
            ("Fonction numérique", "Fonction de transfert numerique"),
            ("Vérification cohérence", "verify_symbolic_numeric_consistency"),
            ("Substitution prioritaire", "priority_vars"),
            ("Substitution manuelle", "for var, val in values.items")
        ]
        
        print("\nÉtapes trouvées dans plot_bode:")
        for step_name, pattern in steps:
            if pattern in plot_bode_code:
                print(f"✓ {step_name}")
            else:
                print(f"❌ {step_name} - MANQUANT")
        
        # Vérifier la logique de substitution
        print("\nAnalyse de la logique de substitution:")
        
        # Compter les substitutions
        subst_count = plot_bode_code.count("subst(")
        evalf_count = plot_bode_code.count("evalf(")
        
        print(f"Nombre de substitutions: {subst_count}")
        print(f"Nombre d'évaluations: {evalf_count}")
        
        return True
    else:
        print("❌ Fonction plot_bode non trouvée")
        return False

def test_verify_function():
    """Test direct de la fonction verify_symbolic_numeric_consistency"""
    
    print("\n=== TEST DE verify_symbolic_numeric_consistency ===")
    
    try:
        sys.path.append('.')
        from test import verify_symbolic_numeric_consistency
        
        # Test 1: Expressions identiques
        print("Test 1 - Expressions identiques:")
        result1 = verify_symbolic_numeric_consistency("1.0", "1.0", {})
        print(f"  Résultat: {result1}")
        
        # Test 2: Expressions différentes mais équivalentes
        print("Test 2 - Expressions équivalentes:")
        result2 = verify_symbolic_numeric_consistency("2.0", "2.000000", {})
        print(f"  Résultat: {result2}")
        
        # Test 3: Expressions vraiment différentes
        print("Test 3 - Expressions différentes:")
        result3 = verify_symbolic_numeric_consistency("1.0", "2.0", {})
        print(f"  Résultat: {result3}")
        
        # Test 4: Expressions complexes
        print("Test 4 - Expressions complexes:")
        result4 = verify_symbolic_numeric_consistency("(s+1)/(s+2)", "(s+1)/(s+2)", {})
        print(f"  Résultat: {result4}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_substitution_order():
    """Analyse l'ordre des substitutions dans plot_bode"""
    
    print("\n=== ANALYSE DE L'ORDRE DES SUBSTITUTIONS ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extraire la fonction plot_bode
    plot_bode_match = re.search(r'def plot_bode\(.*?\n(?=def |\nif __name__|$)', content, re.DOTALL)
    
    if plot_bode_match:
        plot_bode_code = plot_bode_match.group(0)
        
        # Analyser les étapes de substitution
        lines = plot_bode_code.split('\n')
        
        substitution_steps = []
        for i, line in enumerate(lines):
            if 'subst(' in line or 'evalf(' in line or 'force_fc_substitution' in line:
                substitution_steps.append((i, line.strip()))
        
        print("Ordre des substitutions dans plot_bode:")
        for i, (line_num, line) in enumerate(substitution_steps):
            print(f"  {i+1}. Ligne {line_num}: {line}")
        
        # Vérifier si les substitutions sont identiques pour symbolique et numérique
        symbolic_substitutions = []
        numeric_substitutions = []
        
        in_symbolic_section = False
        in_numeric_section = False
        
        for line in lines:
            if 'tf_symbolic' in line and 'subst(' in line:
                symbolic_substitutions.append(line.strip())
            elif 'tf_numeric' in line and 'subst(' in line:
                numeric_substitutions.append(line.strip())
        
        print(f"\nSubstitutions symboliques: {len(symbolic_substitutions)}")
        for sub in symbolic_substitutions:
            print(f"  {sub}")
        
        print(f"\nSubstitutions numériques: {len(numeric_substitutions)}")
        for sub in numeric_substitutions:
            print(f"  {sub}")
        
        # Comparer
        if symbolic_substitutions == numeric_substitutions:
            print("✓ Les substitutions sont identiques")
        else:
            print("❌ Les substitutions sont différentes")
            print("PROBLÈME IDENTIFIÉ: Substitutions non cohérentes")
        
        return True
    else:
        print("❌ Fonction plot_bode non trouvée")
        return False

def propose_fix():
    """Propose une correction basée sur l'analyse"""
    
    print("\n=== PROPOSITION DE CORRECTION ===")
    
    print("Problème identifié:")
    print("- Les substitutions pour les fonctions symbolique et numérique ne sont pas identiques")
    print("- L'ordre ou la méthode de substitution diffère")
    
    print("\nSolution proposée:")
    print("1. Utiliser une fonction unique de substitution pour les deux")
    print("2. Appliquer exactement les mêmes étapes dans le même ordre")
    print("3. Vérifier que force_fc_substitution est appliqué de manière identique")
    
    # Créer une version corrigée
    correction_code = '''
def apply_consistent_substitution(expression, values):
    """Applique une substitution cohérente pour symbolique et numérique"""
    
    result = expression
    
    # 1. Appliquer force_fc_substitution
    result = force_fc_substitution(result, values)
    
    # 2. Variables prioritaires
    priority_vars = [k for k in values.keys() if k.startswith('ro_') or k.startswith('beta_') or k.startswith('Ic_ac_') or k.startswith('Av_') or k.startswith('Fc1_') or k.startswith('Fc2_') or k.startswith('Fc3_') or k.startswith('Fc4_')]
    
    for var in priority_vars:
        comp_value = values.get(var, '1000')
        try:
            comp_value_num = xcas(f"evalf({comp_value})")
            float(comp_value_num)
            result = xcas(f"subst({result},{var},{comp_value_num})")
        except:
            # Valeurs par défaut
            default_values = {
                'ro_': '1000', 'beta_': '100', 'Ic_ac_': '0.001', 'Av_': '100000',
                'Fc1_': '100000000', 'Fc2_': '1000000000', 'Fc3_': '10000000000', 'Fc4_': '100000000000'
            }
            for prefix, default_val in default_values.items():
                if var.startswith(prefix):
                    result = xcas(f"subst({result},{var},{default_val})")
                    break
    
    # 3. Résistances de compensation BJT
    bjt_compensation_resistors = set()
    for var, val in values.items():
        if var.startswith('comp_BJT_res_') and val == 'true':
            resistor_name = var.replace('comp_BJT_res_', '')
            bjt_compensation_resistors.add(resistor_name)
    
    # 4. Autres composants
    for var, val in values.items():
        if var not in priority_vars and var != 'k' and not var.startswith('comp_BJT_'):
            if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                try:
                    comp_value_num = xcas(f"evalf({val})")
                    float(comp_value_num)
                    if var in str(result):
                        result = xcas(f"subst({result},{var},{comp_value_num})")
                except: 
                    continue
    
    # 5. Substitutions finales
    result = xcas(f"subst({result},k,1000)")
    result = xcas(f"subst({result},pi,3.14159265359)")
    result = xcas(f"evalf(simplify({result}))")
    
    return result
'''
    
    print("\nCode de correction proposé:")
    print(correction_code)
    
    return correction_code

def main():
    """Fonction principale de debug"""
    
    print("DEBUG RAPIDE DU PROBLÈME DE COHÉRENCE")
    print("=" * 50)
    
    # Analyser la fonction plot_bode
    plot_bode_ok = analyze_plot_bode_function()
    
    # Tester la fonction de vérification
    verify_ok = test_verify_function()
    
    # Analyser l'ordre des substitutions
    substitution_ok = analyze_substitution_order()
    
    # Proposer une correction
    if not substitution_ok:
        correction_code = propose_fix()
    
    print("\n" + "=" * 50)
    print("RÉSUMÉ DU DEBUG")
    print("=" * 50)
    
    print(f"Fonction plot_bode: {'✓' if plot_bode_ok else '❌'}")
    print(f"Fonction verify: {'✓' if verify_ok else '❌'}")
    print(f"Ordre substitutions: {'✓' if substitution_ok else '❌'}")
    
    if not substitution_ok:
        print("\n🔧 CORRECTION NÉCESSAIRE:")
        print("Le problème vient de substitutions non cohérentes entre symbolique et numérique")
        print("Utilisez le code de correction proposé ci-dessus")

if __name__ == "__main__":
    main()
