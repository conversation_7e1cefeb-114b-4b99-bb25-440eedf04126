#!/usr/bin/env python3
"""
Test spécifique avec le circuit de l'utilisateur pour identifier le problème
"""
import subprocess
import sys
import os

def test_user_circuit():
    """Teste le circuit spécifique de l'utilisateur"""
    
    print("=== TEST DU CIRCUIT UTILISATEUR ===")
    
    # Circuit exact de l'utilisateur
    user_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit utilisateur:")
    print(user_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{user_circuit}"""
    
    print("=== Test du circuit utilisateur ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "7", 1.0, 1e12, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    with open('run_user_circuit.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_user_circuit.py")
    print("Exécution du test...")
    print()
    
    try:
        result = subprocess.run(['python', 'run_user_circuit.py'], 
                              capture_output=True, text=True, timeout=180)
        
        output = result.stdout
        error = result.stderr
        
        print("=== RÉSULTATS DU TEST ===")
        print("STDOUT:")
        print(output)
        
        if error:
            print("\nSTDERR:")
            print(error)
        
        # Analyser les fonctions de transfert
        analyze_transfer_functions(output)
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_user_circuit.py'):
            os.remove('run_user_circuit.py')

def analyze_transfer_functions(output):
    """Analyse les fonctions de transfert dans la sortie"""
    
    print("\n=== ANALYSE DES FONCTIONS DE TRANSFERT ===")
    
    lines = output.split('\n')
    
    h1_function = None
    pure_function = None
    final_function = None
    coherence_message = None
    
    for line in lines:
        if "H1(s) = V7/V1 =" in line:
            h1_function = line.strip()
            print(f"✓ H1(s) trouvée: {h1_function}")
        elif "Fonction symbolique pure (sans substitutions):" in line:
            pure_function = line.strip()
            print(f"✓ Fonction pure trouvée: {pure_function}")
        elif "Fonction de transfert finale:" in line:
            final_function = line.strip()
            print(f"✓ Fonction finale trouvée: {final_function}")
        elif "ATTENTION" in line or "OK Coherence" in line or "Substitutions coherentes" in line:
            coherence_message = line.strip()
            print(f"✓ Message cohérence: {coherence_message}")
    
    print("\n=== COMPARAISON DES FONCTIONS ===")
    
    if h1_function and pure_function:
        # Extraire les expressions
        h1_expr = extract_expression(h1_function)
        pure_expr = extract_expression(pure_function)
        
        print(f"Expression H1(s): {h1_expr}")
        print(f"Expression pure: {pure_expr}")
        
        if h1_expr == pure_expr:
            print("⚠️  PROBLÈME: H1(s) et fonction pure sont identiques")
            print("   → force_fc_substitution ne fait rien (pas d'amplis op ?)")
        else:
            print("✓ H1(s) et fonction pure sont différentes (normal)")
    
    if final_function:
        final_expr = extract_expression(final_function)
        print(f"Expression finale: {final_expr}")
        
        # Vérifier si l'expression finale semble correcte
        if "s" in final_expr and any(char.isdigit() for char in final_expr):
            print("✓ Expression finale contient s et des nombres (semble correcte)")
        else:
            print("❌ Expression finale suspecte")
    
    # Vérifier la cohérence
    if coherence_message:
        if "OK Coherence" in coherence_message or "Substitutions coherentes" in coherence_message:
            print("✓ Cohérence confirmée")
        elif "ATTENTION" in coherence_message:
            print("❌ Problème de cohérence détecté")
    
    return h1_function, pure_function, final_function

def extract_expression(line):
    """Extrait l'expression mathématique d'une ligne"""
    
    if "=" in line:
        # Prendre tout après le dernier "="
        parts = line.split("=")
        return parts[-1].strip()
    return line.strip()

def compare_with_expected():
    """Compare avec les résultats attendus"""
    
    print("\n=== COMPARAISON AVEC LES ATTENTES ===")
    
    print("Pour le circuit BJT donné, on s'attend à:")
    print("1. H1(s) = expression avec R1, R2, C1, etc. (pas de Fc car pas d'ampli op)")
    print("2. Fonction pure = même chose que H1(s) (pas d'ampli op)")
    print("3. Fonction finale = expression avec valeurs numériques (100, 10000, 1e-7, etc.)")
    print("4. Message de cohérence positif")
    
    print("\nSi vous voyez une fonction 'fausse', vérifiez:")
    print("- Les coefficients numériques sont-ils corrects ?")
    print("- La structure de l'expression est-elle logique ?")
    print("- Y a-t-il des termes manquants ou en trop ?")

def main():
    """Fonction principale"""
    
    print("TEST SPÉCIFIQUE DU CIRCUIT UTILISATEUR")
    print("=" * 50)
    
    # Tester le circuit
    success = test_user_circuit()
    
    if success:
        # Comparer avec les attentes
        compare_with_expected()
        
        print("\n" + "=" * 50)
        print("DIAGNOSTIC")
        print("=" * 50)
        print("Regardez les fonctions de transfert ci-dessus.")
        print("Dites-moi laquelle est 'fausse' et pourquoi.")
        print("Je pourrai alors identifier et corriger le problème précis.")
    else:
        print("\n❌ Impossible de tester le circuit")

if __name__ == "__main__":
    main()
