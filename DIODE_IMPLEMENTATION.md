# Implémentation du composant Diode dans le simulateur

## Résumé des modifications

Le composant diode a été ajouté au simulateur avec le minimum de modifications possible. La diode est définie uniquement par sa tension de seuil, qui apparaît comme paramètre symbolique dans les expressions.

## Syntaxe utilisateur

```
Dnom anode cathode tension_seuil
```

**Exemple:** `D1 1 2 0.7`
- D1: nom de la diode
- 1: nœud anode  
- 2: nœud cathode
- 0.7: tension de seuil en volts

## Paramètres symboliques

- `Vth_Dnom`: Tension de seuil (définie par l'utilisateur)
- `Vt_Dnom`: Tension thermique (26 mV par défaut)
- `rd_Dnom`: Résistance dynamique (calculée automatiquement)

## Modélisation

### Analyse DC
La diode est modélisée comme un composant actif avec une contrainte de tension:
```
V_anode - V_cathode = Vth_D1
```
Cette contrainte impose que la tension aux bornes de la diode soit égale à sa tension de seuil. Le courant de la diode est une variable indépendante résolue par les équations KCL aux nœuds.

**Correction importante**: La version initiale modélisait incorrectement la diode comme une résistance avec source de tension, ce qui donnait des résultats erronés. La version corrigée traite la diode comme un composant actif similaire aux BJT.

### Analyse AC
La diode se comporte comme une résistance dynamique calculée à partir du point DC:
```
rd_D1 = Vt / |Id_dc|
I_D1 = (V_anode - V_cathode) / rd_D1
```
où:
- `Vt` = tension thermique (≈ 26 mV à 25°C)
- `Id_dc` = courant DC de la diode au point de fonctionnement

## Modifications apportées au code

### 1. `get_user_netlist()` (ligne 696)
- Ajout de la syntaxe diode dans l'aide utilisateur

### 2. `solve_circuit()` (ligne 992)
- Ajout du traitement du type 'D' dans la boucle de parsing
- Initialisation des paramètres `Vth_Dnom` et `rd_Dnom`

### 3. `dc_analysis()` (lignes 143-303)
- Ajout de `diode_components_info` et `num_diode_current_vars`
- Traitement des diodes dans la matrice conductance
- Ajout des variables de courant pour les diodes
- Gestion des erreurs pour les diodes

### 4. `ac_analysis()` (lignes 467-587)
- Traitement des diodes comme résistances dynamiques en AC
- Calcul des courants de diode en AC

### 5. Affichage des résultats (lignes 1214-1348)
- Affichage des paramètres des diodes
- Affichage des courants de diode dans tous les types d'analyse

## Avantages de cette implémentation

1. **Paramètre symbolique**: La tension de seuil apparaît comme `Vth_Dnom` dans les expressions symboliques
2. **Modifications minimales**: Réutilise l'architecture existante du simulateur
3. **Compatibilité**: Compatible avec tous les autres composants (BJT, op-amp, etc.)
4. **Simplicité**: Modèle simple mais efficace pour l'analyse de circuits
5. **Extensibilité**: Facile d'ajouter d'autres paramètres si nécessaire

## Exemple d'utilisation

```
V1 1 0 DC 5 AC 1
R1 1 2 1k
D1 2 0 0.7
R2 2 3 2k
```

Ce circuit contient une diode D1 avec une tension de seuil de 0.7V entre les nœuds 2 (anode) et 0 (cathode).

## Correction de la modélisation

### Problème initial
La première implémentation modélisait incorrectement la diode dans l'analyse DC, traitant la diode comme un composant passif dans la matrice conductance. Cela donnait des résultats erronés:

**Circuit test**: `V1 1 0 DC 5`, `R1 1 2 100`, `D1 2 0 2.2`
- **Résultat incorrect**: V2 = 5V (même tension qu'à l'entrée)
- **Résultat attendu**: V2 = 2.2V (tension de seuil de la diode)

### Solution
La diode est maintenant correctement modélisée comme un composant actif avec:
1. **Variable de courant**: I_D1 (variable indépendante)
2. **Contrainte de tension**: V_anode - V_cathode = Vth_D1
3. **Équations KCL**: Le courant de diode participe aux bilans de courant aux nœuds

### Résultat corrigé
Pour le circuit test:
- **DC**: V1 = 5.0V, V2 = 2.2V, I_D1 = 0.028A
- **AC**: V1 = 3.0V, V2 ≈ 0.273V, I_D1 ≈ 0.027A

### Évolution de la résistance dynamique

**Version 1**: `rd_D1 = 1e-3 Ω` (1 mΩ) → Résultats irréalistes
**Version 2**: `rd_D1 = 10 Ω` (fixe) → Amélioration mais pas physique
**Version 3**: `rd_D1 = Vt / |Id_dc|` → Calcul physiquement correct
**Version 4**: **Analyse AC avec point de polarisation DC** → **Approche rigoureuse**

### Calcul automatique de rd
La résistance dynamique est maintenant calculée automatiquement après l'analyse DC:

```python
Id_dc = courant_dc_de_la_diode  # Calculé par l'analyse DC
Vt = 0.026  # Tension thermique (26 mV)
rd = Vt / abs(Id_dc)  # Résistance dynamique physique
```

## Analyse AC avancée avec point de polarisation

### Principe
L'analyse AC prend maintenant en compte le **point de fonctionnement DC** de chaque diode :

1. **Analyse de l'état DC** : Conductrice vs Bloquée
2. **Calcul de rd appropriée** selon l'état
3. **Vérification du régime** : Petit signal vs Grand signal
4. **Avertissements** si l'analyse linéaire n'est pas fiable

### États des diodes

#### Diode conductrice
- **Critères** : `Id_dc > 1nA` ET `V_dc > 0.5×Vth`
- **Résistance AC** : `rd = Vt / Id_dc`
- **Comportement** : Suit les variations AC

#### Diode bloquée
- **Critères** : `Id_dc ≤ 1nA` OU `V_dc ≤ 0.5×Vth`
- **Résistance AC** : `rd = 1TΩ` (circuit ouvert)
- **Comportement** : N'affecte pas le signal AC

### Régimes AC

#### Petit signal (fiable)
- **Critère** : Amplitude AC < 10% × Vt (≈ 2.6mV)
- **Validité** : rd constante, analyse linéaire valide
- **Résultats** : Fiables

#### Grand signal (approximatif)
- **Critère** : Amplitude AC > 10% × Vt
- **Problème** : Diode peut changer d'état pendant le cycle
- **Résultats** : Approximatifs, écrêtage possible

### Exemple pour le circuit test
- **État** : Conductrice (Id_dc = 0.028A, V_dc = 2.2V)
- **rd calculée** : 0.026 / 0.028 = **0.929 Ω**
- **Amplitude AC estimée** : ≈ 0.028V aux bornes de la diode
- **Régime** : Grand signal (0.028V >> 0.0026V)
- **Conclusion** : ⚠️ Résultats AC approximatifs

## Tests

- `test_diode.py`: Tests de parsing et initialisation
- `test_diode_simple.py`: Vérification de la correction de modélisation
