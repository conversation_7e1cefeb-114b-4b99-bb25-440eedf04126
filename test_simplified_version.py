#!/usr/bin/env python3
"""
Test de la version simplifiée avec une seule fonction de transfert
"""
import subprocess
import sys
import os

def test_simplified_circuit():
    """Teste le circuit avec la version simplifiée"""
    
    print("=== TEST DE LA VERSION SIMPLIFIÉE ===")
    
    # Circuit de test
    test_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit de test:")
    print(test_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
import os
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{test_circuit}"""
    
    print("=== Test de la version simplifiee ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "7", 1.0, 1e12, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    with open('run_simplified_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_simplified_test.py")
    print("Exécution du test...")
    print()
    
    try:
        result = subprocess.run(['python', 'run_simplified_test.py'], 
                              capture_output=True, text=True, timeout=120)
        
        output = result.stdout
        error = result.stderr
        
        print("=== RÉSULTATS DU TEST ===")
        print("STDOUT:")
        print(output)
        
        if error:
            print("\nSTDERR:")
            print(error)
        
        # Analyser les résultats
        if "Fonction de transfert finale:" in output:
            print("\n✅ SUCCÈS: Fonction de transfert finale affichée")
            success = True
        else:
            print("\n❌ PROBLÈME: Fonction de transfert finale non trouvée")
            success = False
        
        if "Substitutions coherentes appliquees" in output:
            print("✅ SUCCÈS: Message de cohérence affiché")
        else:
            print("❌ PROBLÈME: Message de cohérence non trouvé")
            success = False
        
        if "Diagramme de Bode affiche" in output:
            print("✅ SUCCÈS: Diagramme de Bode affiché")
        else:
            print("⚠️  INFO: Diagramme de Bode non confirmé")
        
        # Vérifier qu'il n'y a plus de messages redondants
        if "ATTENTION Difference detectee" in output:
            print("❌ PROBLÈME: Message d'erreur encore présent")
            success = False
        elif "OK Coherence" in output:
            print("⚠️  INFO: Ancien message de cohérence encore présent (à supprimer)")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_simplified_test.py'):
            os.remove('run_simplified_test.py')

def verify_simplification():
    """Vérifie que la simplification a bien été appliquée"""
    
    print("\n=== VÉRIFICATION DE LA SIMPLIFICATION ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier que les éléments redondants ont été supprimés
    checks = [
        ("Une seule fonction finale", "tf_final = apply_consistent_substitution" in content),
        ("Plus de tf_symbolic_final", "tf_symbolic_final" not in content),
        ("Plus de tf_numeric_final", "tf_numeric_final" not in content),
        ("Plus de vérification cohérence", "verify_symbolic_numeric_consistency" not in content),
        ("Message simplifié", "Fonction de transfert finale:" in content),
        ("Message cohérence simplifié", "Substitutions coherentes appliquees" in content),
        ("Diagramme de Bode confirmé", "Diagramme de Bode affiche" in content)
    ]
    
    print("Vérifications :")
    all_good = True
    for check_name, check_result in checks:
        status = "✅" if check_result else "❌"
        print(f"  {status} {check_name}")
        if not check_result:
            all_good = False
    
    return all_good

def main():
    """Fonction principale"""
    
    print("TEST DE LA VERSION SIMPLIFIÉE")
    print("=" * 50)
    
    # Vérifier la simplification
    simplification_ok = verify_simplification()
    
    if simplification_ok:
        print("\n✅ Simplification correctement appliquée")
        
        # Tester le circuit
        test_ok = test_simplified_circuit()
        
        if test_ok:
            print("\n🎉 SUCCÈS COMPLET!")
            print("\n📝 RÉSUMÉ DES AMÉLIORATIONS :")
            print("• Plus de redondance entre versions 3 et 4")
            print("• Une seule fonction de transfert finale")
            print("• Plus de vérification de cohérence inutile")
            print("• Messages simplifiés et clairs")
            print("• Diagramme de Bode affiché directement")
            print("\n✨ Le code est maintenant plus propre et plus compréhensible !")
        else:
            print("\n⚠️  Simplification appliquée mais test échoué")
    else:
        print("\n❌ Problème avec la simplification")

if __name__ == "__main__":
    main()
