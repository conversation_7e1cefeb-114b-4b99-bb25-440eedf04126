#!/usr/bin/env python3
"""
Script pour créer une icône personnalisée pour l'application Bode
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPixmap, QPainter, QPen, QBrush, QFont
from PyQt5.QtCore import Qt
import sys

def create_bode_icon(size=64):
    """Crée une icône représentant un diagramme de Bode"""
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.transparent)
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # Fond avec dégradé bleu
    painter.setBrush(QBrush(Qt.blue))
    painter.setPen(QPen(Qt.darkBlue, 2))
    painter.drawRoundedRect(2, 2, size-4, size-4, 8, 8)
    
    # Grille
    painter.setPen(QPen(Qt.lightGray, 1))
    for i in range(1, 4):
        x = i * size // 4
        y = i * size // 4
        painter.drawLine(x, 8, x, size-8)  # Lignes verticales
        painter.drawLine(8, y, size-8, y)  # Lignes horizontales
    
    # Axes principaux
    painter.setPen(QPen(Qt.white, 2))
    painter.drawLine(8, size//2, size-8, size//2)  # Axe horizontal
    painter.drawLine(size//4, 8, size//4, size-8)  # Axe vertical
    
    # Courbe de magnitude (décroissante)
    painter.setPen(QPen(Qt.yellow, 3))
    points_mag = [
        (size//4, size//3),
        (size//2, size//2),
        (3*size//4, 2*size//3)
    ]
    for i in range(len(points_mag)-1):
        painter.drawLine(points_mag[i][0], points_mag[i][1], 
                        points_mag[i+1][0], points_mag[i+1][1])
    
    # Courbe de phase (en S)
    painter.setPen(QPen(Qt.cyan, 2))
    points_phase = [
        (size//4, 3*size//4),
        (size//2, 2*size//3),
        (3*size//4, size//2)
    ]
    for i in range(len(points_phase)-1):
        painter.drawLine(points_phase[i][0], points_phase[i][1], 
                        points_phase[i+1][0], points_phase[i+1][1])
    
    # Texte "B" pour Bode
    painter.setPen(QPen(Qt.white, 1))
    font = QFont("Arial", size//6, QFont.Bold)
    painter.setFont(font)
    painter.drawText(size-size//3, size//4, "B")
    
    painter.end()
    return pixmap

def save_icon_files():
    """Sauvegarde l'icône dans différents formats"""
    app = QApplication(sys.argv)
    
    # Créer des icônes de différentes tailles
    sizes = [16, 32, 48, 64, 128, 256]
    
    for size in sizes:
        pixmap = create_bode_icon(size)
        
        # Sauvegarder en PNG
        pixmap.save(f"bode_icon_{size}.png", "PNG")
        print(f"Icône PNG {size}x{size} créée: bode_icon_{size}.png")
    
    # Créer l'icône principale
    main_icon = create_bode_icon(64)
    main_icon.save("bode_icon.png", "PNG")
    print("Icône principale créée: bode_icon.png")
    
    # Essayer de créer un fichier ICO (Windows)
    try:
        main_icon.save("bode_icon.ico", "ICO")
        print("Icône ICO créée: bode_icon.ico")
    except:
        print("Impossible de créer le fichier ICO (format non supporté)")
    
    print("\nPour utiliser ces icônes:")
    print("1. Placez bode_icon.png ou bode_icon.ico dans le même dossier que bode_H1_final.py")
    print("2. L'application chargera automatiquement l'icône au démarrage")

if __name__ == "__main__":
    save_icon_files()
