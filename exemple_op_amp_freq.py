#!/usr/bin/env python3

# Exemple comparatif : Amplificateur opérationnel idéal vs réaliste

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

print("=== Comparaison Amplificateur Opérationnel : Idéal vs Réaliste ===\n")

# Configuration commune : Amplificateur non-inverseur avec gain de 11 (1 + R2/R1)
base_netlist = """V1 1 0 DC 0 AC 1
R1 1 2 1k
R2 3 0 10k
R3 3 4 1k
R4 4 0 1k"""

print("Configuration de base (amplificateur non-inverseur, gain théorique = 11):")
print("V1 1 0 DC 0 AC 1")
print("R1 1 2 1k") 
print("R2 3 0 10k")
print("R3 3 4 1k")
print("R4 4 0 1k")
print()

# Test 1: Amplificateur quasi-idéal
print("1. Amplificateur quasi-idéal (fc1=1MHz, fc2=100MHz)")
print("   OP1 0 2 3 1000000 1000000 100000000")
netlist_ideal = base_netlist + "\nOP1 0 2 3 1000000 1000000 100000000"

try:
    solve_circuit(netlist_ideal, 
                 frequency_hz=1000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=1.0, 
                 freq_max=1000000000.0, 
                 laplace_domain=True)
    print("   ✓ Simulation terminée\n")
except Exception as e:
    print(f"   ✗ Erreur: {e}\n")

# Test 2: Amplificateur réaliste type 741
print("2. Amplificateur réaliste type 741 (fc1=10Hz, fc2=1MHz)")
print("   OP1 0 2 3 200000 10 1000000")
netlist_741 = base_netlist + "\nOP1 0 2 3 200000 10 1000000"

try:
    solve_circuit(netlist_741, 
                 frequency_hz=1000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=0.1, 
                 freq_max=100000000.0, 
                 laplace_domain=True)
    print("   ✓ Simulation terminée\n")
except Exception as e:
    print(f"   ✗ Erreur: {e}\n")

# Test 3: Amplificateur haute fréquence
print("3. Amplificateur haute fréquence (fc1=1kHz, fc2=10MHz)")
print("   OP1 0 2 3 1000000 1000 10000000")
netlist_hf = base_netlist + "\nOP1 0 2 3 1000000 1000 10000000"

try:
    solve_circuit(netlist_hf, 
                 frequency_hz=10000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=1.0, 
                 freq_max=1000000000.0, 
                 laplace_domain=True)
    print("   ✓ Simulation terminée\n")
except Exception as e:
    print(f"   ✗ Erreur: {e}\n")

print("=== Analyse des résultats ===")
print("Comparez les diagrammes de Bode générés pour observer :")
print("1. L'amplificateur quasi-idéal : gain constant sur une large bande")
print("2. L'amplificateur 741 : limitation à basse fréquence (10Hz)")
print("3. L'amplificateur HF : bon compromis gain/bande passante")
print()
print("Observez les pentes :")
print("- Gain constant jusqu'à fc1")
print("- -20dB/décade entre fc1 et fc2") 
print("- -40dB/décade au-delà de fc2")
