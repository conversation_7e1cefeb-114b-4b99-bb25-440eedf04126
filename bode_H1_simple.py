#!/usr/bin/env python3
"""
Script simplifié pour tracer le diagramme de Bode de H1(s) = V7/V1
à partir du fichier Results_Simulation_20250614_1139.txt
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import re

def extract_h1_functions(filename):
    """Extrait les fonctions de transfert H1(s) = V7/V1 du fichier"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire la fonction symbolique
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert numérique|$)', content, re.DOTALL)
    
    if not h1_symbolic_match:
        print("Erreur: Fonction de transfert symbolique H1(s) non trouvée")
        return None, None
    
    h1_symbolic = h1_symbolic_match.group(1).strip()
    print(f"Fonction symbolique trouvée: {len(h1_symbolic)} caractères")
    
    # Extraire la fonction numérique
    h1_numeric_match = re.search(r'Fonction de transfert num.*?rique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    
    if not h1_numeric_match:
        print("Erreur: Fonction de transfert numérique non trouvée")
        return h1_symbolic, None
    
    h1_numeric = h1_numeric_match.group(1).strip()
    print(f"Fonction numérique trouvée: {len(h1_numeric)} caractères")
    
    return h1_symbolic, h1_numeric

def extract_parameters(filename):
    """Extrait les paramètres numériques du circuit"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    parameters = {}
    
    # Extraire la netlist
    netlist_match = re.search(r'Netlist originale:(.*?)(?=Temps de calcul|$)', content, re.DOTALL)
    if netlist_match:
        netlist = netlist_match.group(1)
        
        # Résistances
        r_matches = re.findall(r'R(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[kmMG]?)', netlist)
        for r_num, r_val in r_matches:
            val = parse_value(r_val)
            parameters[f'R{r_num}'] = val
        
        # Capacités
        c_matches = re.findall(r'C(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[pnumkMG]?[Ff]?)', netlist)
        for c_num, c_val in c_matches:
            val = parse_value(c_val)
            parameters[f'C{c_num}'] = val
    
    # Paramètres BJT
    bjt_match = re.search(r'--- Valeurs numériques retenues pour Q1 ---(.*?)(?====|$)', content, re.DOTALL)
    if bjt_match:
        bjt_section = bjt_match.group(1)
        
        param_patterns = {
            'Vbe_on_Q1': r'Vbe_on_Q1 = ([0-9.e-]+)',
            'beta_Q1': r'beta_Q1 = ([0-9.e-]+)',
            'Cbc_Q1': r'Cbc_Q1 = ([0-9.e-]+)',
            'ro_Q1': r'ro_Q1 = ([0-9.e-]+[kmMG]?)',
            'rpi_Q1': r'rpi_Q1 = ([0-9.e-]+)',
            'Cbe_Q1': r'Cbe_Q1 = ([0-9.e-]+)',
            'gm_Q1': r'gm_Q1 = ([0-9.e-]+)'
        }
        
        for param_name, pattern in param_patterns.items():
            match = re.search(pattern, bjt_section)
            if match:
                val_str = match.group(1)
                if 'k' in val_str.lower():
                    val = float(val_str.replace('k', '').replace('K', '')) * 1000
                elif 'M' in val_str:
                    val = float(val_str.replace('M', '')) * 1e6
                elif 'G' in val_str:
                    val = float(val_str.replace('G', '')) * 1e9
                else:
                    val = float(val_str)
                parameters[param_name] = val
    
    return parameters

def parse_value(value_str):
    """Parse une valeur avec unité"""
    value_str = value_str.strip().upper()
    value_str = re.sub(r'[FHΩ]$', '', value_str)
    
    multipliers = {
        'P': 1e-12, 'N': 1e-9, 'U': 1e-6, 'M': 1e-3,
        'K': 1e3, 'MEG': 1e6, 'G': 1e9
    }
    
    for mult, factor in multipliers.items():
        if value_str.endswith(mult):
            base_val = float(value_str[:-len(mult)])
            return base_val * factor
    
    return float(value_str)

def create_simplified_tf():
    """Crée une fonction de transfert simplifiée pour test"""
    
    # Basé sur l'analyse du circuit: amplificateur à transistor avec couplage capacitif
    # Approximation: passe-haut du 1er ordre suivi d'un gain et d'un passe-bas
    
    # Paramètres approximatifs du circuit
    R1, R2, R3, R6 = 100, 10000, 10000, 800
    C1, C2, C3 = 100e-9, 10e-6, 100e-9
    
    # Fréquences de coupure approximatives
    f_hp = 1 / (2 * np.pi * R1 * C1)  # Passe-haut d'entrée
    f_lp = 1 / (2 * np.pi * R6 * C3)  # Passe-bas de sortie
    
    # Gain approximatif (très faible pour V7 car c'est après R6)
    gain = 1e-6  # Très faible car V7 est après une résistance de charge
    
    print(f"Fréquences approximatives:")
    print(f"  Passe-haut: {f_hp:.1f} Hz")
    print(f"  Passe-bas: {f_lp:.1f} Hz")
    print(f"  Gain: {gain:.2e} ({20*np.log10(gain):.1f} dB)")
    
    # Fonction de transfert: H(s) = K * s/(s + ωhp) * 1/(s + ωlp)
    # = K * s / ((s + ωhp)(s + ωlp))
    
    w_hp = 2 * np.pi * f_hp
    w_lp = 2 * np.pi * f_lp
    
    # Numérateur: K * s
    num = [gain, 0]
    
    # Dénominateur: (s + ωhp)(s + ωlp) = s² + (ωhp + ωlp)s + ωhp*ωlp
    den = [1, w_hp + w_lp, w_hp * w_lp]
    
    tf = signal.TransferFunction(num, den)
    
    return tf

def plot_bode_diagram(tf, title="H1(s) = V7/V1"):
    """Trace le diagramme de Bode"""
    
    # Gamme de fréquences
    frequencies = np.logspace(-1, 9, 10000)  # 0.1 Hz à 1 GHz
    omega = 2 * np.pi * frequencies
    
    # Calcul de la réponse fréquentielle
    _, h = signal.freqresp(tf, omega)
    
    # Conversion en dB et phase en degrés
    magnitude_db = 20 * np.log10(np.abs(h))
    phase_deg = np.angle(h) * 180 / np.pi
    
    # Création du graphique
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Graphique de magnitude
    ax1.semilogx(frequencies, magnitude_db, 'b-', linewidth=2)
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title(f'Diagramme de Bode - {title}')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(0.1, 1e9)
    
    # Graphique de phase
    ax2.semilogx(frequencies, phase_deg, 'r-', linewidth=2)
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(0.1, 1e9)
    
    plt.tight_layout()
    
    # Sauvegarder
    output_filename = 'bode_H1_V7_V1_simplified.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    # Informations
    print(f"\n=== Analyse du diagramme ===")
    print(f"Gain à 1 kHz: {magnitude_db[np.argmin(np.abs(frequencies - 1000))]:.1f} dB")
    print(f"Diagramme sauvegardé: {output_filename}")

def main():
    """Fonction principale"""
    
    print("=== Analyse simplifiée de H1(s) = V7/V1 ===")
    
    filename = "Results_Simulation_20250614_1139.txt"
    
    # Vérifier que le fichier existe
    import os
    if not os.path.exists(filename):
        print(f"Erreur: Le fichier {filename} n'existe pas.")
        return
    
    # Extraire les fonctions de transfert
    h1_symbolic, h1_numeric = extract_h1_functions(filename)
    
    # Extraire les paramètres
    parameters = extract_parameters(filename)
    
    print(f"\n=== Paramètres extraits ===")
    for param, value in parameters.items():
        print(f"{param} = {value}")
    
    # Pour l'instant, utiliser une fonction de transfert simplifiée
    print(f"\n=== Création d'une fonction de transfert simplifiée ===")
    tf = create_simplified_tf()
    
    # Tracer le diagramme de Bode
    plot_bode_diagram(tf)
    
    print(f"\nNote: Cette version utilise une approximation simplifiée.")
    print(f"Les expressions symbolique et numérique complètes sont très complexes")
    print(f"et nécessitent un traitement plus avancé pour être converties en")
    print(f"fonctions de transfert utilisables par scipy.signal.")

if __name__ == "__main__":
    main()
