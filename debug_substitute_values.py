#!/usr/bin/env python3
"""
Debug de la fonction substitute_values
"""
import sys
sys.path.append('.')

def test_substitute_values():
    """Teste la fonction substitute_values directement"""
    
    print("=== TEST DE substitute_values ===")
    
    try:
        from test import substitute_values, force_fc_substitution
        
        # Test simple
        test_expr = "R1*s + 1"
        test_values = {
            'R1': '1000',
            'C1': '1e-7'
        }
        
        print(f"Expression test: {test_expr}")
        print(f"Valeurs test: {test_values}")
        
        # Test substitute_values
        result = substitute_values({'test': test_expr}, test_values)
        print(f"Résultat substitute_values: {result}")
        
        # Test force_fc_substitution
        fc_result = force_fc_substitution(test_expr, test_values)
        print(f"Résultat force_fc_substitution: {fc_result}")
        
        # Test combiné (comme dans plot_bode)
        tf_with_fc = force_fc_substitution(test_expr, test_values)
        tf_final = substitute_values({'tf': tf_with_fc}, test_values)['tf']
        print(f"Résultat combiné: {tf_final}")
        
        return True
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plot_bode_directly():
    """Teste la fonction plot_bode directement"""
    
    print("\n=== TEST DE plot_bode DIRECTEMENT ===")
    
    try:
        from test import plot_bode
        
        # Test simple
        test_expr = "1000*s + 1"
        test_values = {
            'R1': '1000',
            'C1': '1e-7'
        }
        freq_range = (1.0, 1e6)
        
        print(f"Expression: {test_expr}")
        print(f"Valeurs: {test_values}")
        print(f"Fréquences: {freq_range}")
        
        # Appeler plot_bode
        plot_bode(test_expr, test_values, freq_range)
        
        return True
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    
    print("DEBUG DE substitute_values ET plot_bode")
    print("=" * 50)
    
    # Test substitute_values
    success1 = test_substitute_values()
    
    # Test plot_bode
    success2 = test_plot_bode_directly()
    
    if success1 and success2:
        print("\n✅ Les fonctions fonctionnent correctement")
    else:
        print("\n❌ Il y a un problème dans les fonctions")

if __name__ == "__main__":
    main()
