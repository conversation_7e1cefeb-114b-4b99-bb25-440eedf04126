#!/usr/bin/env python3
"""
Test du circuit original avec la correction appliquée
"""
import subprocess
import sys
import os

def test_original_circuit():
    """Teste le circuit original problématique"""
    
    print("=== TEST DU CIRCUIT ORIGINAL AVEC CORRECTION ===")
    
    original_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit:")
    print(original_circuit)
    print()
    
    # Créer un script de test avec timeout plus court
    test_script = f'''
import sys
import signal
import os
sys.path.append('.')

def timeout_handler(signum, frame):
    print("TIMEOUT: Test interrompu après 30 secondes")
    sys.exit(1)

# Configurer le timeout
signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(30)  # 30 secondes

try:
    from test import solve_circuit
    
    netlist_str = """{original_circuit}"""
    
    print("=== Test du circuit original corrigé ===")
    print("Début du calcul...")
    
    # Paramètres de test
    frequency_hz = 1000.0
    do_transfer_function = True
    input_node = "1"
    output_node = "7"
    freq_min = 1.0
    freq_max = 1e6
    
    solve_circuit(netlist_str, frequency_hz, do_transfer_function, 
                 input_node, output_node, freq_min, freq_max, laplace_domain=True)
    
    print("Test terminé avec succès")
    
except KeyboardInterrupt:
    print("Test interrompu par l'utilisateur")
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
finally:
    signal.alarm(0)  # Désactiver le timeout
'''
    
    # Sauvegarder le script
    with open('test_original_fixed.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: test_original_fixed.py")
    print("Exécution du test (timeout: 30s)...")
    print()
    
    try:
        # Exécuter le test avec un timeout
        result = subprocess.run(['python', 'test_original_fixed.py'], 
                              capture_output=True, text=True, timeout=45)
        
        output = result.stdout
        error = result.stderr
        
        print("=== RÉSULTATS DU TEST ===")
        print("STDOUT:")
        print(output)
        
        if error:
            print("\nSTDERR:")
            print(error)
        
        # Analyser les résultats
        if "OK Coherence symbolique/numerique verifiee" in output:
            print("\n🎉 SUCCÈS: Cohérence vérifiée!")
            return True
        elif "ATTENTION Difference detectee" in output:
            print("\n⚠ ATTENTION: Différence encore détectée")
            # Extraire les détails
            import re
            diff_match = re.search(r'ATTENTION Difference detectee: (.+)', output)
            if diff_match:
                print(f"Détail: {diff_match.group(1)}")
            return False
        elif "Test terminé avec succès" in output:
            print("\n✓ Test exécuté sans erreur")
            return True
        else:
            print("\n? Résultat incertain")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout 45s)")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('test_original_fixed.py'):
            os.remove('test_original_fixed.py')

def check_latest_results():
    """Vérifie les derniers fichiers de résultats"""
    
    print("\n=== VÉRIFICATION DES DERNIERS RÉSULTATS ===")
    
    # Chercher les fichiers de résultats récents
    result_files = [f for f in os.listdir('.') if f.startswith('Results_Simulation_') and f.endswith('.txt')]
    
    if not result_files:
        print("Aucun fichier de résultats trouvé")
        return False
    
    # Prendre le plus récent
    latest_file = max(result_files, key=lambda f: os.path.getctime(f))
    print(f"Fichier le plus récent: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Chercher les messages de cohérence
        if "OK Coherence symbolique/numerique verifiee" in content:
            print("✓ Cohérence vérifiée dans le dernier fichier")
            return True
        elif "ATTENTION Difference detectee" in content:
            print("⚠ Différence détectée dans le dernier fichier")
            # Extraire les détails
            import re
            diff_matches = re.findall(r'ATTENTION Difference detectee: (.+)', content)
            for diff in diff_matches:
                print(f"  {diff}")
            return False
        else:
            print("? Aucun message de cohérence trouvé")
            return False
            
    except Exception as e:
        print(f"Erreur lors de la lecture: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("TEST DU CIRCUIT ORIGINAL AVEC CORRECTION APPLIQUÉE")
    print("=" * 60)
    
    # Tester le circuit original
    test_success = test_original_circuit()
    
    # Vérifier les derniers résultats
    results_ok = check_latest_results()
    
    print("\n" + "=" * 60)
    print("RÉSUMÉ")
    print("=" * 60)
    
    if test_success and results_ok:
        print("🎉 SUCCÈS COMPLET: La correction fonctionne!")
    elif test_success:
        print("✓ Test exécuté mais vérification manuelle nécessaire")
    elif results_ok:
        print("✓ Cohérence trouvée dans les fichiers existants")
    else:
        print("❌ Problème persistant - Investigation supplémentaire nécessaire")
    
    print("\nActions recommandées:")
    print("1. Vérifier manuellement les derniers fichiers Results_Simulation_*.txt")
    print("2. Chercher les messages 'OK Coherence' ou 'ATTENTION Difference'")
    print("3. Si le problème persiste, analyser les expressions caractère par caractère")

if __name__ == "__main__":
    main()
