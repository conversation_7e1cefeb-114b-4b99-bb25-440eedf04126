#!/usr/bin/env python3
"""
Test pour vérifier que les paramètres Fc sont remplacés par leurs valeurs numériques
dans l'analyse AC symbolique.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def test_ac_symbolic_fc_substitution():
    """Test du remplacement des paramètres Fc dans l'analyse AC symbolique"""
    print("=== Test de substitution des paramètres Fc dans AC Analysis (Symbolic) ===")
    
    netlist_str = """
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 1M 1G
R2 2 3 2k
"""
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    original_nodes = sorted(list(set(comp[i] for comp in original_netlist for i in [1,2]) - {'0'}))
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse AC
    print("Analyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    
    print("\n=== Comparaison AC Analysis (Symbolic) ===")
    
    # Tester pour chaque nœud
    fc_substitution_success = True
    
    for node in original_nodes:
        if node in voltages_ac:
            # Expression originale (avec Fc symboliques)
            original_expr = voltages_ac[node]
            
            # Expression avec Fc remplacés
            expr_with_fc = force_fc_substitution(original_expr, values)
            
            print(f"\nNœud V{node}:")
            print(f"  Original: {original_expr}")
            print(f"  Avec Fc numériques: {expr_with_fc}")
            
            # Vérifier que les paramètres Fc ont été remplacés
            fc_params_found = []
            for var in values.keys():
                if var.startswith('Fc') and '_' in var:
                    if var in str(original_expr):
                        fc_params_found.append(var)
            
            fc_params_remaining = []
            for var in fc_params_found:
                if var in str(expr_with_fc):
                    fc_params_remaining.append(var)
            
            if fc_params_found:
                print(f"  Fc trouvés: {fc_params_found}")
                print(f"  Fc restants: {fc_params_remaining}")
                
                if fc_params_remaining:
                    print(f"  ❌ {len(fc_params_remaining)} paramètres Fc non remplacés")
                    fc_substitution_success = False
                else:
                    print(f"  ✅ Tous les paramètres Fc remplacés")
            else:
                print(f"  ℹ️  Aucun paramètre Fc dans cette expression")
    
    # Tester pour les courants des composants actifs
    for comp in netlist:
        if comp[0][0].upper() == 'X':  # Amplificateur opérationnel
            current_key = f"{comp[0]}_out"
            if current_key in currents_ac:
                # Expression originale (avec Fc symboliques)
                original_expr = currents_ac[current_key]
                
                # Expression avec Fc remplacés
                expr_with_fc = force_fc_substitution(original_expr, values)
                
                print(f"\nCourant I_{comp[0]}_out:")
                print(f"  Original: {original_expr}")
                print(f"  Avec Fc numériques: {expr_with_fc}")
                
                # Vérifier que les paramètres Fc ont été remplacés
                fc_params_found = []
                for var in values.keys():
                    if var.startswith('Fc') and '_' in var:
                        if var in str(original_expr):
                            fc_params_found.append(var)
                
                fc_params_remaining = []
                for var in fc_params_found:
                    if var in str(expr_with_fc):
                        fc_params_remaining.append(var)
                
                if fc_params_found:
                    print(f"  Fc trouvés: {fc_params_found}")
                    print(f"  Fc restants: {fc_params_remaining}")
                    
                    if fc_params_remaining:
                        print(f"  ❌ {len(fc_params_remaining)} paramètres Fc non remplacés")
                        fc_substitution_success = False
                    else:
                        print(f"  ✅ Tous les paramètres Fc remplacés")
                else:
                    print(f"  ℹ️  Aucun paramètre Fc dans cette expression")
    
    # Afficher les valeurs des paramètres Fc
    print(f"\n=== Valeurs des paramètres Fc ===")
    for var, val in values.items():
        if var.startswith('Fc') and '_' in var:
            print(f"  {var} = {val}")
    
    return fc_substitution_success

if __name__ == "__main__":
    print("Test de substitution des paramètres Fc dans AC Analysis (Symbolic)")
    print("=" * 75)
    
    success = test_ac_symbolic_fc_substitution()
    
    print("\n" + "=" * 75)
    if success:
        print("🎉 Test réussi! Les paramètres Fc sont correctement remplacés dans AC Analysis (Symbolic).")
    else:
        print("❌ Test échoué. Certains paramètres Fc ne sont pas correctement remplacés.")
