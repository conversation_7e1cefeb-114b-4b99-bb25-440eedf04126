#!/usr/bin/env python3
"""
Test de la fonctionnalité d'ajout améliorée avec critères assouplis
"""

import sys
import os
import numpy as np
import sympy as sp

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bode_H1_final import *

def test_improved_criteria():
    """Test des nouveaux critères assouplis"""
    
    print("=== Test des critères d'ajout améliorés ===\n")
    
    # Simuler une situation réaliste
    current_mag_error = 5.0  # Erreur actuelle de 5 dB
    current_phase_error = 30.0  # Erreur de phase de 30°
    
    # Test des différents cas d'amélioration
    test_cases = [
        # (new_mag_error, new_phase_error, description)
        (4.9, 25.0, "Petite amélioration magnitude + phase"),
        (4.95, 30.0, "Très petite amélioration magnitude"),
        (5.1, 20.0, "Légère dégradation magnitude mais grosse amélioration phase"),
        (5.5, 35.0, "Dégradation modérée"),
        (10.0, 60.0, "Grosse dégradation"),
        (2.0, 15.0, "Grosse amélioration"),
    ]
    
    print("Test des critères bénéfiques:")
    for new_mag_error, new_phase_error, description in test_cases:
        mag_improvement = current_mag_error - new_mag_error
        phase_improvement = current_phase_error - new_phase_error
        total_improvement = mag_improvement + 0.1 * phase_improvement
        
        # Critères adaptatifs
        is_beneficial_1 = (
            mag_improvement > 0.01 and  # Amélioration magnitude même petite
            new_mag_error < current_mag_error * 1.5 and  # Tolérance de dégradation raisonnable
            total_improvement > 0  # Amélioration globale positive
        )
        
        is_beneficial_2 = (
            mag_improvement > -0.5 and  # Accepter même une légère dégradation
            new_phase_error < current_phase_error * 0.8 and  # Si amélioration significative de phase
            total_improvement > 0
        )
        
        is_beneficial = is_beneficial_1 or is_beneficial_2
        
        print(f"  {description}:")
        print(f"    Mag: {current_mag_error:.1f} -> {new_mag_error:.1f} dB (Δ: {mag_improvement:+.1f})")
        print(f"    Phase: {current_phase_error:.1f} -> {new_phase_error:.1f}° (Δ: {phase_improvement:+.1f})")
        print(f"    Total: {total_improvement:+.2f}, Bénéfique: {'✓' if is_beneficial else '✗'}")
        print()
    
    print("Test des critères de fallback (moins nuisible):")
    for new_mag_error, new_phase_error, description in test_cases:
        mag_degradation = new_mag_error - current_mag_error
        phase_degradation = new_phase_error - current_phase_error
        total_degradation = mag_degradation + 0.1 * phase_degradation
        
        # Critères de fallback
        is_acceptable = (
            new_mag_error < current_mag_error * 2.0 and  # Pas plus du double d'erreur
            total_degradation < 10.0  # Dégradation raisonnable
        )
        
        print(f"  {description}:")
        print(f"    Dégradation totale: {total_degradation:+.2f} dB")
        print(f"    Acceptable: {'✓' if is_acceptable else '✗'}")
        print()

def test_expression_reconstruction():
    """Test de reconstruction d'expressions avec validation"""
    
    print("=== Test de reconstruction d'expressions ===\n")
    
    # Expressions de test
    test_expressions = [
        "1/(R1*C1*s + 1)",
        "gm_Q1*R2/(R1*C1*s + 1)",
        "(R1 + R2)/(R3*C1*s + R4*C2*s + 1)",
    ]
    
    terms_to_add = [
        ("R2*C2*s", "dénominateur"),
        ("gm_Q1*R3", "numérateur"),
        ("C3*s", "dénominateur"),
    ]
    
    for expr_str in test_expressions:
        print(f"Expression de base: {expr_str}")
        
        for term_str, part_name in terms_to_add:
            try:
                # Parser les expressions
                expr = sp.sympify(expr_str)
                term = sp.sympify(term_str)
                
                print(f"  Ajout de '{term_str}' au {part_name}:")
                
                # Reconstruction
                if part_name == "numérateur":
                    if hasattr(expr, 'as_numer_denom'):
                        num, den = expr.as_numer_denom()
                        new_expr = (num + term) / den
                    else:
                        new_expr = expr + term
                else:  # dénominateur
                    if hasattr(expr, 'as_numer_denom'):
                        num, den = expr.as_numer_denom()
                        new_expr = num / (den + term)
                    else:
                        new_expr = expr / (1 + term)
                
                # Simplification
                simplified = sp.simplify(new_expr)
                
                print(f"    Résultat: {simplified}")
                
                # Test de validation basique
                try:
                    # Test avec valeurs numériques
                    test_vals = {'R1': 1000, 'R2': 2000, 'R3': 1500, 'R4': 500,
                                'C1': 1e-6, 'C2': 2e-6, 'C3': 5e-7, 'gm_Q1': 0.1, 's': 1j*2*np.pi*1000}
                    
                    result = complex(simplified.subs(test_vals))
                    magnitude = abs(result)
                    
                    is_valid = np.isfinite(magnitude) and 1e-10 < magnitude < 1e10
                    print(f"    Validation: {'✓' if is_valid else '✗'} (|H| = {magnitude:.2e})")
                    
                except Exception as e:
                    print(f"    Validation: ✗ (erreur: {e})")
                
                print()
                
            except Exception as e:
                print(f"    Erreur: {e}")
                print()

def test_fallback_selection():
    """Test de la sélection de fallback"""
    
    print("=== Test de sélection de fallback ===\n")
    
    # Simuler une liste de termes avec différents impacts
    terms_data = [
        ("R1*C1*s", 2.5, "dénominateur"),
        ("gm_Q1*R2", 1.8, "numérateur"),
        ("R3*C2*s", 0.9, "dénominateur"),
        ("C3*s", 0.3, "dénominateur"),
    ]
    
    print("Termes disponibles (triés par impact décroissant):")
    for i, (term_str, impact, part) in enumerate(terms_data):
        print(f"  {i+1}. {term_str} (impact: {impact:.1f} dB, {part})")
    
    print(f"\nSélection par défaut (plus gros impact): {terms_data[0][0]} ({terms_data[0][1]:.1f} dB)")
    
    # Test de tri par impact
    sorted_terms = sorted(terms_data, key=lambda x: x[1], reverse=True)
    print(f"\nVérification du tri:")
    for term_str, impact, part in sorted_terms:
        print(f"  {term_str}: {impact:.1f} dB")

if __name__ == "__main__":
    try:
        test_improved_criteria()
        test_expression_reconstruction()
        test_fallback_selection()
        
        print("=== Résumé des améliorations ===")
        print("✓ Critères assouplis: amélioration minimale de 0.01 dB au lieu de 0.1 dB")
        print("✓ Tolérance de dégradation: jusqu'à 50% d'erreur supplémentaire")
        print("✓ Critères alternatifs: amélioration de phase compensant dégradation magnitude")
        print("✓ Fallback intelligent: sélection du terme le moins nuisible")
        print("✓ Dernier recours: terme avec le plus gros impact original")
        print("\nCes améliorations devraient permettre de toujours trouver un terme à ajouter.")
        
    except Exception as e:
        print(f"Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
