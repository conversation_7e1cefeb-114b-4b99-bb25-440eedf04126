#!/usr/bin/env python3
"""
Test avec le fichier FTC.txt réel
"""

from exeml import (
    read_symbolic_expression, 
    extract_variables, 
    define_component_values,
    analyze_variable_sensitivity_parallel,
    simplify_transfer_function_symbolic,
    plot_original_vs_simplified
)

def test_with_ftc():
    """Test avec le fichier FTC.txt réel"""
    
    # Utiliser le fichier FTC.txt
    file_path = r"C:\Users\<USER>\Documents\Julia\FTC.txt"
    print(f"Test avec le fichier FTC.txt : {file_path}")
    
    expression = read_symbolic_expression(file_path)
    if expression is None:
        print("Erreur : impossible de lire le fichier FTC.txt")
        return
    
    print(f"Expression FTC.txt :")
    print(f"{expression[:200]}..." if len(expression) > 200 else expression)
    print(f"Longueur totale : {len(expression)} caractères")
    
    # Extraire les variables
    variables = extract_variables(expression)
    component_values = define_component_values(variables)
    
    print(f"\nNombre de variables trouvées : {len(variables)}")
    print(f"Variables : {sorted(variables)}")
    
    # Analyse de sensibilité rapide
    print(f"\nAnalyse de sensibilité (mode rapide)...")
    sensitivity_results = analyze_variable_sensitivity_parallel(expression, component_values, num_processes=4)
    
    if sensitivity_results is None:
        print("Erreur dans l'analyse de sensibilité")
        return
    
    print(f"\nTop 10 variables les plus sensibles :")
    for i, result in enumerate(sensitivity_results[:10]):
        print(f"{i+1:2d}. {result['variable']:<12}: {result['impact_score']:8.3f} dB")
    
    print(f"\nTop 10 variables les moins sensibles :")
    sorted_by_impact = sorted(sensitivity_results, key=lambda x: x['impact_score'])
    for i, result in enumerate(sorted_by_impact[:10]):
        print(f"{i+1:2d}. {result['variable']:<12}: {result['impact_score']:8.3f} dB")
    
    # Test de simplification avec seuil conservateur
    print(f"\n{'='*60}")
    print("TEST DE SIMPLIFICATION AVEC SEUIL CONSERVATEUR")
    print(f"{'='*60}")
    
    simplified_expr, removed_vars = simplify_transfer_function_symbolic(
        expression, component_values, sensitivity_results, max_error_db=1.0
    )
    
    if simplified_expr and removed_vars:
        print(f"\n{'='*60}")
        print("RÉSULTAT DE LA SIMPLIFICATION")
        print(f"{'='*60}")
        
        print(f"Expression originale : {len(expression)} caractères")
        print(f"Expression simplifiée : {len(simplified_expr)} caractères")
        
        reduction = ((len(expression) - len(simplified_expr)) / len(expression)) * 100
        print(f"Réduction de taille : {reduction:.1f}%")
        print(f"Variables supprimées : {len(removed_vars)}")
        print(f"Variables supprimées : {[var[0] for var in removed_vars]}")
        
        print(f"\nExpression simplifiée :")
        if len(simplified_expr) > 500:
            print(f"{simplified_expr[:500]}...")
        else:
            print(simplified_expr)
        
        # Demander si on veut afficher la comparaison
        show_plot = input(f"\nAfficher la comparaison Bode ? (o/n) [défaut: o]: ").strip().lower()
        if show_plot != 'n':
            plot_original_vs_simplified(expression, simplified_expr, component_values)
    
    elif simplified_expr:
        print("Aucune variable n'a pu être supprimée avec le seuil d'erreur de 1.0 dB.")
        print("Essayez avec un seuil plus élevé (2.0 ou 5.0 dB).")
    else:
        print("Erreur lors de la simplification.")

if __name__ == "__main__":
    test_with_ftc()
