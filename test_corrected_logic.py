#!/usr/bin/env python3
"""
Test de la logique corrigée qui utilise substitute_values
"""
import subprocess
import sys
import os

def test_corrected_circuit():
    """Teste le circuit avec la logique corrigée"""
    
    print("=== TEST DE LA LOGIQUE CORRIGÉE ===")
    
    # Circuit de l'utilisateur
    user_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit utilisateur:")
    print(user_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{user_circuit}"""
    
    print("=== Test avec logique corrigee ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "7", 1.0, 1e12, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # <PERSON>uvegarder et exécuter le script
    with open('run_corrected_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_corrected_test.py")
    print("Exécution du test (timeout: 60s)...")
    print()
    
    try:
        result = subprocess.run(['python', 'run_corrected_test.py'], 
                              capture_output=True, text=True, timeout=60)
        
        output = result.stdout
        error = result.stderr
        
        print("=== RÉSULTATS DU TEST ===")
        
        if result.returncode == 0:
            print("✅ Test exécuté avec succès")
            
            # Analyser les fonctions de transfert
            analyze_functions(output)
            
            return True
        else:
            print("❌ Erreur lors de l'exécution")
            print("STDOUT:", output)
            print("STDERR:", error)
            return False
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_corrected_test.py'):
            os.remove('run_corrected_test.py')

def analyze_functions(output):
    """Analyse les fonctions de transfert dans la sortie"""
    
    print("\n=== ANALYSE DES FONCTIONS ===")
    
    lines = output.split('\n')
    
    h1_function = None
    pure_function = None
    final_function = None
    
    for line in lines:
        if "H1(s) = V7/V1 =" in line:
            h1_function = line.strip()
        elif "Fonction symbolique pure" in line:
            pure_function = line.strip()
        elif "Fonction de transfert finale:" in line:
            final_function = line.strip()
    
    print("Fonctions trouvées :")
    
    if h1_function:
        print(f"✅ H1(s) (avec Fc): {h1_function}")
    else:
        print("❌ H1(s) non trouvée")
    
    if pure_function:
        print(f"✅ Fonction pure: {pure_function}")
    else:
        print("❌ Fonction pure non trouvée")
    
    if final_function:
        print(f"✅ Fonction finale: {final_function}")
        
        # Vérifier que la fonction finale contient des valeurs numériques
        final_expr = extract_expression(final_function)
        if has_numeric_values(final_expr):
            print("✅ La fonction finale contient des valeurs numériques (correct)")
        else:
            print("❌ La fonction finale ne contient pas de valeurs numériques")
    else:
        print("❌ Fonction finale non trouvée")
    
    # Vérifier la cohérence
    if "Substitutions coherentes appliquees" in output:
        print("✅ Message de cohérence trouvé")
    else:
        print("❌ Message de cohérence non trouvé")

def extract_expression(line):
    """Extrait l'expression mathématique d'une ligne"""
    if ":" in line:
        return line.split(":", 1)[1].strip()
    elif "=" in line:
        return line.split("=", 1)[1].strip()
    return line.strip()

def has_numeric_values(expression):
    """Vérifie si l'expression contient des valeurs numériques"""
    import re
    # Chercher des nombres (entiers, décimaux, scientifiques)
    numeric_pattern = r'\d+(?:\.\d+)?(?:[eE][+-]?\d+)?'
    numbers = re.findall(numeric_pattern, expression)
    return len(numbers) > 0

def verify_code_fix():
    """Vérifie que la correction du code est bien appliquée"""
    
    print("\n=== VÉRIFICATION DE LA CORRECTION ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier que plot_bode utilise maintenant substitute_values
    if "tf_final = substitute_values({'tf': tf_with_fc}, values)['tf']" in content:
        print("✅ plot_bode utilise maintenant substitute_values (correct)")
    else:
        print("❌ plot_bode n'utilise pas substitute_values")
    
    # Vérifier que apply_consistent_substitution a été supprimée
    if "def apply_consistent_substitution" not in content:
        print("✅ apply_consistent_substitution supprimée (correct)")
    else:
        print("❌ apply_consistent_substitution encore présente")
    
    # Vérifier que force_fc_substitution est toujours utilisée
    if "tf_with_fc = force_fc_substitution(transfer_function, values)" in content:
        print("✅ force_fc_substitution toujours utilisée (correct)")
    else:
        print("❌ force_fc_substitution non utilisée")

def main():
    """Fonction principale"""
    
    print("TEST DE LA LOGIQUE CORRIGÉE")
    print("=" * 50)
    
    # Vérifier la correction du code
    verify_code_fix()
    
    # Tester le circuit
    success = test_corrected_circuit()
    
    print("\n" + "=" * 50)
    print("CONCLUSION")
    print("=" * 50)
    
    if success:
        print("🎉 CORRECTION RÉUSSIE!")
        print("\n✅ CHANGEMENTS APPLIQUÉS :")
        print("• plot_bode utilise maintenant force_fc_substitution + substitute_values")
        print("• Même logique que le reste du code")
        print("• Plus de fonction apply_consistent_substitution personnalisée")
        print("• Cohérence garantie avec le reste du programme")
        print("\n📝 La fonction de transfert finale devrait maintenant être correcte !")
    else:
        print("⚠️  Test échoué - Investigation supplémentaire nécessaire")

if __name__ == "__main__":
    main()
