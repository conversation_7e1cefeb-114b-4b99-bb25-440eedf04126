import sympy as sp
import numpy as np
import matplotlib.pyplot as plt
from sympy import symbols, I, lambdify, log, pi
import re
import multiprocessing as mp
from functools import partial
import time

def read_symbolic_expression(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()
    except:
        return None

def extract_variables(expression_str):
    variables = re.findall(r'[a-zA-Z_][a-zA-Z0-9_]*', expression_str)
    return sorted(list(set(var for var in variables if var != 's')))

def define_component_values(variables_in_expression):
    all_values = {
        'C1': 4.49e-07, 'C2': 3.04e-07, 'C3': 8.95e-08,
        'Cbc_Q1': 2.3e-11, 'Cbe_Q1': 2.34e-11,
        'R1': 1.5e+04, 'R2': 5.97e+04, 'R3': 9.88e+04,
        'R4': 7.76e+04, 'R5': 7.16e+04, 'R6': 2.51e+04,
        'V1': 1.0, 'V2': 1.0, 'gm_Q1': 0.0714,
        'ro_Q1': 4.88e+04, 'rpi_Q1': 587.0,
    }
    return {var: all_values.get(var, 1.0) for var in variables_in_expression}

def is_voltage_source(var_name):
    """Vérifie si une variable est une source de tension (commence par 'V')"""
    return var_name.startswith('V')

def filter_non_voltage_variables(component_values):
    """Filtre les variables pour exclure les sources de tension"""
    return {var: value for var, value in component_values.items() if not is_voltage_source(var)}

def calculate_bode_full_range(expression_str, component_values, freq_range):
    """Calcule la réponse de Bode sur une plage de fréquences complète"""
    try:
        s = symbols('s')
        expr = sp.sympify(expression_str)
        for var in sorted(component_values.keys(), key=len, reverse=True):
            if symbols(var) in expr.free_symbols:
                expr = expr.subs(symbols(var), component_values[var])
        H_func = lambdify(s, expr, 'numpy')
        s_values = 1j * 2 * np.pi * freq_range
        H_jw = H_func(s_values)
        return 20 * np.log10(np.abs(H_jw)), np.angle(H_jw) * 180 / np.pi
    except Exception as e:
        print(f"Erreur dans le calcul : {e}")
        return None, None

def calculate_bode_at_frequencies(expression_str, component_values, test_frequencies):
    try:
        s = symbols('s')
        expr = sp.sympify(expression_str)
        for var in sorted(component_values.keys(), key=len, reverse=True):
            if symbols(var) in expr.free_symbols:
                expr = expr.subs(symbols(var), component_values[var])
        H_func = lambdify(s, expr, 'numpy')
        s_values = 1j * 2 * np.pi * test_frequencies
        H_jw = H_func(s_values)
        return 20 * np.log10(np.abs(H_jw)), np.angle(H_jw) * 180 / np.pi
    except:
        return None, None

def calculate_geometric_mean_magnitude_impact(mag_rel_change):
    """Calcule la moyenne géométrique des variations de magnitude"""
    non_zero_changes = mag_rel_change[mag_rel_change > 1e-12]
    
    if len(non_zero_changes) == 0:
        return 0.0
    
    try:
        geometric_mean = np.exp(np.mean(np.log(non_zero_changes)))
        return geometric_mean
    except:
        return np.mean(mag_rel_change)

# ============================================================================
# FONCTIONS POUR MULTIPROCESSING
# ============================================================================

def analyze_single_variable_worker(args):
    """
    Fonction worker pour analyser une seule variable en parallèle
    Args: tuple contenant (var_name, expression_str, component_values, test_frequencies, mag_ref, phase_ref)
    """
    var_name, expression_str, component_values, test_frequencies, mag_ref, phase_ref = args
    
    try:
        # Créer une copie des valeurs avec la variable doublée
        modified_values = component_values.copy()
        modified_values[var_name] = component_values[var_name] * 2.0  # +100%
        
        # Calculer les nouvelles valeurs
        mag_mod, phase_mod = calculate_bode_at_frequencies(expression_str, modified_values, test_frequencies)
        
        if mag_mod is None:
            return None
        
        # Calculer les variations
        mag_diff = mag_mod - mag_ref
        phase_diff = phase_mod - phase_ref
        
        # Calculer les variations relatives
        mag_rel_change = np.abs(mag_diff)
        phase_rel_change = np.abs(phase_diff)
        
        # Métriques d'impact
        max_mag_change = np.max(mag_rel_change)
        max_phase_change = np.max(phase_rel_change)
        avg_mag_change = np.mean(mag_rel_change)
        avg_phase_change = np.mean(phase_rel_change)
        
        # Score d'impact = moyenne géométrique des variations de magnitude
        geometric_mean_mag = calculate_geometric_mean_magnitude_impact(mag_rel_change)
        impact_score = geometric_mean_mag
        
        result = {
            'variable': var_name,
            'max_mag_change': max_mag_change,
            'avg_mag_change': avg_mag_change,
            'geometric_mean_mag': geometric_mean_mag,
            'max_phase_change': max_phase_change,
            'avg_phase_change': avg_phase_change,
            'impact_score': impact_score,
            'original_value': component_values[var_name],
            'mag_diff': mag_diff,
            'phase_diff': phase_diff,
            'mag_mod': mag_mod,
            'phase_mod': phase_mod
        }
        
        return result
        
    except Exception as e:
        print(f"Erreur lors de l'analyse de {var_name}: {e}")
        return None

def analyze_variable_sensitivity_parallel(expression_str, component_values, num_processes=None):
    """
    Version parallélisée de l'analyse de sensibilité
    """
    # Déterminer le nombre de processus à utiliser
    if num_processes is None:
        num_processes = min(mp.cpu_count(), 8)  # Limiter à 8 pour éviter la surcharge
    
    print(f"Utilisation de {num_processes} processus pour l'analyse parallèle")
    
    # Fréquences de test
    test_frequencies = np.array([10, 100, 1e3, 1e4, 1e5, 1e6, 1e7])
    
    # Séparer les variables selon leur type
    non_voltage_vars = filter_non_voltage_variables(component_values)
    voltage_vars = {var: value for var, value in component_values.items() if is_voltage_source(var)}
    
    print(f"Variables à analyser (hors sources V) : {list(non_voltage_vars.keys())}")
    print(f"Sources de tension ignorées : {list(voltage_vars.keys())}")
    
    # Calcul des valeurs de référence
    print("\nCalcul des valeurs de référence...")
    start_time = time.time()
    mag_ref, phase_ref = calculate_bode_at_frequencies(expression_str, component_values, test_frequencies)
    ref_time = time.time() - start_time
    print(f"Temps de calcul de référence : {ref_time:.2f}s")
    
    if mag_ref is None:
        print("Erreur dans le calcul de référence")
        return None
    
    print(f"\nValeurs de référence :")
    print(f"{'Fréquence (Hz)':<12} {'Magnitude (dB)':<15} {'Phase (°)':<12}")
    print("-" * 40)
    for i, freq in enumerate(test_frequencies):
        print(f"{freq:<12.0e} {mag_ref[i]:<15.3f} {phase_ref[i]:<12.3f}")
    
    # Préparer les arguments pour le multiprocessing
    var_names = list(non_voltage_vars.keys())
    args_list = [(var_name, expression_str, component_values, test_frequencies, mag_ref, phase_ref) 
                 for var_name in var_names]
    
    print(f"\n{'='*90}")
    print("ANALYSE DE SENSIBILITÉ PARALLÈLE - SCORE = MOYENNE GÉOMÉTRIQUE (HORS SOURCES V)")
    print(f"{'='*90}")
    print(f"Analyse de {len(var_names)} variables en parallèle...")
    
    # Lancer l'analyse en parallèle
    start_time = time.time()
    
    with mp.Pool(processes=num_processes) as pool:
        # Afficher la progression
        results = []
        for i, result in enumerate(pool.imap(analyze_single_variable_worker, args_list)):
            if result is not None:
                results.append(result)
                print(f"Analysé {i+1}/{len(var_names)}: {result['variable']} "
                      f"(Score: {result['impact_score']:.3f} dB)")
            else:
                print(f"Erreur pour la variable {i+1}/{len(var_names)}")
    
    parallel_time = time.time() - start_time
    print(f"\nTemps d'analyse parallèle : {parallel_time:.2f}s")
    print(f"Temps estimé en séquentiel : {ref_time * len(var_names):.2f}s")
    print(f"Accélération : {(ref_time * len(var_names)) / parallel_time:.1f}x")
    
    # Tri par ordre d'impact décroissant
    results.sort(key=lambda x: x['impact_score'], reverse=True)
    
    # Affichage des résultats détaillés
    print(f"\n{'='*100}")
    print("CLASSEMENT DES VARIABLES PAR MOYENNE GÉOMÉTRIQUE (HORS SOURCES V)")
    print(f"{'='*100}")
    
    print(f"{'Rang':<4} {'Variable':<12} {'Moy.Géom(dB)':<13} {'Max Mag':<9} {'Moy Arith':<11} {'Max Phase':<11} {'Valeur Orig.':<15}")
    print("-" * 100)
    
    for i, result in enumerate(results, 1):
        print(f"{i:<4} {result['variable']:<12} {result['impact_score']:<13.3f} "
              f"{result['max_mag_change']:<9.3f} {result['avg_mag_change']:<11.3f} "
              f"{result['max_phase_change']:<11.3f} {result['original_value']:<15.3e}")
    
    # Afficher les sources de tension pour information
    if voltage_vars:
        print(f"\n{'='*60}")
        print("SOURCES DE TENSION (NON ANALYSÉES)")
        print(f"{'='*60}")
        for var_name, value in voltage_vars.items():
            print(f"{var_name:<12} {value:<15.3e} (ignorée dans le calcul de score)")
    
    return results

def analyze_variable_sensitivity_sequential(expression_str, component_values):
    """
    Version séquentielle originale pour comparaison
    """
    print("Analyse séquentielle (pour comparaison)...")
    start_time = time.time()
    
    # Fréquences de test
    test_frequencies = np.array([10, 100, 1e3, 1e4, 1e5, 1e6, 1e7])
    
    # Séparer les variables selon leur type
    non_voltage_vars = filter_non_voltage_variables(component_values)
    
    # Calcul des valeurs de référence
    mag_ref, phase_ref = calculate_bode_at_frequencies(expression_str, component_values, test_frequencies)
    
    if mag_ref is None:
        return None
    
    sensitivity_results = []
    
    # Analyser seulement les variables non-tension
    for var_name in non_voltage_vars.keys():
        # Créer une copie des valeurs avec la variable doublée
        modified_values = component_values.copy()
        modified_values[var_name] = component_values[var_name] * 2.0
        
        # Calculer les nouvelles valeurs
        mag_mod, phase_mod = calculate_bode_at_frequencies(expression_str, modified_values, test_frequencies)
        
        if mag_mod is None:
            continue
        
        # Calculer les variations
        mag_diff = mag_mod - mag_ref
        phase_diff = phase_mod - phase_ref
        mag_rel_change = np.abs(mag_diff)
        phase_rel_change = np.abs(phase_diff)
        
        # Métriques d'impact
        max_mag_change = np.max(mag_rel_change)
        max_phase_change = np.max(phase_rel_change)
        avg_mag_change = np.mean(mag_rel_change)
        avg_phase_change = np.mean(phase_rel_change)
        geometric_mean_mag = calculate_geometric_mean_magnitude_impact(mag_rel_change)
        
        sensitivity_results.append({
            'variable': var_name,
            'max_mag_change': max_mag_change,
            'avg_mag_change': avg_mag_change,
            'geometric_mean_mag': geometric_mean_mag,
            'max_phase_change': max_phase_change,
            'avg_phase_change': avg_phase_change,
            'impact_score': geometric_mean_mag,
            'original_value': component_values[var_name]
        })
    
    sequential_time = time.time() - start_time
    print(f"Temps d'analyse séquentielle : {sequential_time:.2f}s")
    
    return sensitivity_results

# ============================================================================
# FONCTIONS DE PLOTTING (inchangées)
# ============================================================================

def plot_all_variations_single_bode(expression_str, component_values, sensitivity_results):
    """Affiche toutes les variations sur un seul diagramme de Bode"""
    freq_range = np.logspace(1, 7, 1000)
    
    # Calcul de référence
    print("Calcul de la réponse de référence pour le graphique...")
    mag_ref, phase_ref = calculate_bode_full_range(expression_str, component_values, freq_range)
    
    if mag_ref is None:
        print("Erreur dans le calcul de référence")
        return
    
    # Créer la figure
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # Tracer la référence en noir épais
    ax1.semilogx(freq_range, mag_ref, 'k-', linewidth=4, label='Référence', alpha=0.9)
    ax2.semilogx(freq_range, phase_ref, 'k-', linewidth=4, label='Référence', alpha=0.9)
    
    # Palette de couleurs étendue
    colors = [
        'red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 
        'olive', 'cyan', 'magenta', 'yellow', 'lime', 'indigo', 'teal', 
        'coral', 'navy', 'maroon', 'gold', 'darkgreen'
    ]
    
    # Styles de ligne pour différencier si trop de variables
    line_styles = ['-', '--', '-.', ':']
    
    # Filtrer les variables pour exclure les sources de tension
    non_voltage_vars = [var for var in component_values.keys() if not is_voltage_source(var)]
    voltage_vars = [var for var in component_values.keys() if is_voltage_source(var)]
    
    print(f"Variables analysées (hors sources de tension) : {len(non_voltage_vars)}")
    print(f"Sources de tension ignorées : {voltage_vars}")
    
    # Dictionnaire pour stocker les impacts pour le tri
    impacts = {}
    
    # Traiter d'abord les variables non-tension
    for i, var_name in enumerate(non_voltage_vars):
        print(f"Traitement graphique de la variable {i+1}/{len(non_voltage_vars)}: {var_name}")
        
        # Créer les valeurs modifiées (+100%)
        modified_values = component_values.copy()
        modified_values[var_name] = component_values[var_name] * 2.0
        
        # Calculer la réponse modifiée
        mag_mod, phase_mod = calculate_bode_full_range(expression_str, modified_values, freq_range)
        
        if mag_mod is None:
            print(f"Erreur pour la variable {var_name}")
            continue
        
        # Calculer l'impact (moyenne géométrique des variations de magnitude)
        mag_rel_change = np.abs(mag_mod - mag_ref)
        impact_score = calculate_geometric_mean_magnitude_impact(mag_rel_change)
        impacts[var_name] = impact_score
        
        # Choisir couleur et style
        color = colors[i % len(colors)]
        line_style = line_styles[(i // len(colors)) % len(line_styles)]
        
        # Épaisseur de ligne basée sur l'impact
        linewidth = max(1, min(3, impact_score))
        alpha = max(0.6, min(1.0, 0.5 + impact_score/10))
        
        # Tracer magnitude
        max_mag_diff = np.max(mag_rel_change)
        ax1.semilogx(freq_range, mag_mod, linestyle=line_style, color=color, 
                    linewidth=linewidth, alpha=alpha,
                    label=f'{var_name} +100% (Moy.Géom={impact_score:.3f}dB, Max={max_mag_diff:.2f}dB)')
        
        # Tracer phase
        max_phase_diff = np.max(np.abs(phase_mod - phase_ref))
        ax2.semilogx(freq_range, phase_mod, linestyle=line_style, color=color, 
                    linewidth=linewidth, alpha=alpha,
                    label=f'{var_name} +100% (Moy.Géom={impact_score:.3f}dB, Δ={max_phase_diff:.1f}°)')
    
    # Traiter les sources de tension avec un style différent
    for i, var_name in enumerate(voltage_vars):
        print(f"Traitement de la source de tension {i+1}/{len(voltage_vars)}: {var_name} (affichage uniquement)")
        
        modified_values = component_values.copy()
        modified_values[var_name] = component_values[var_name] * 2.0
        
        mag_mod, phase_mod = calculate_bode_full_range(expression_str, modified_values, freq_range)
        
        if mag_mod is None:
            continue
        
        max_mag_diff = np.max(np.abs(mag_mod - mag_ref))
        max_phase_diff = np.max(np.abs(phase_mod - phase_ref))
        
        ax1.semilogx(freq_range, mag_mod, ':', color='lightgray', linewidth=1, alpha=0.5,
                    label=f'{var_name} +100% (Source V, Δ={max_mag_diff:.2f}dB)')
        ax2.semilogx(freq_range, phase_mod, ':', color='lightgray', linewidth=1, alpha=0.5,
                    label=f'{var_name} +100% (Source V, Δ={max_phase_diff:.1f}°)')
    
    # Configuration du graphique
    ax1.set_ylabel('Magnitude (dB)', fontsize=14, fontweight='bold')
    ax1.set_title('Analyse de sensibilité complète - Magnitude\n(Score = Moyenne géométrique, Sources V ignorées dans le score)', 
                  fontsize=16, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    ax2.set_xlabel('Fréquence (Hz)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Phase (degrés)', fontsize=14, fontweight='bold')
    ax2.set_title('Analyse de sensibilité complète - Phase\n(Classement par moyenne géométrique, hors sources V)', 
                  fontsize=16, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    plt.tight_layout()
    plt.subplots_adjust(right=0.75)
    
    # Afficher les statistiques
    sorted_impacts = sorted(impacts.items(), key=lambda x: x[1], reverse=True)
    
    info_text = "Variables les plus sensibles (hors V):\n"
    for i, (var, impact) in enumerate(sorted_impacts[:5]):
        info_text += f"{i+1}. {var}: {impact:.3f} dB\n"
    
    ax1.text(0.02, 0.02, info_text, transform=ax1.transAxes, 
             verticalalignment='bottom', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.show()

def plot_top_variations_only(expression_str, component_values, sensitivity_results, top_n=10):
    """Affiche seulement les N variables les plus sensibles sur un diagramme de Bode"""
    freq_range = np.logspace(1, 7, 1000)
    
    # Calcul de référence
    mag_ref, phase_ref = calculate_bode_full_range(expression_str, component_values, freq_range)
    
    if mag_ref is None:
        return
    
    # Sélectionner les top N variables
    top_vars = [result['variable'] for result in sensitivity_results[:top_n]]
    
    # Créer la figure
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # Tracer la référence
    ax1.semilogx(freq_range, mag_ref, 'k-', linewidth=4, label='Référence', alpha=0.9)
    ax2.semilogx(freq_range, phase_ref, 'k-', linewidth=4, label='Référence', alpha=0.9)
    
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, var_name in enumerate(top_vars):
        modified_values = component_values.copy()
        modified_values[var_name] = component_values[var_name] * 2.0
        
        mag_mod, phase_mod = calculate_bode_full_range(expression_str, modified_values, freq_range)
        
        if mag_mod is None:
            continue
        
        color = colors[i % len(colors)]
        
        # Trouver le score d'impact
        impact_score = next(r['impact_score'] for r in sensitivity_results if r['variable'] == var_name)
        max_mag_diff = np.max(np.abs(mag_mod - mag_ref))
        max_phase_diff = np.max(np.abs(phase_mod - phase_ref))
        
        # Épaisseur basée sur l'impact
        linewidth = max(2, min(4, 2 + impact_score))
        
        ax1.semilogx(freq_range, mag_mod, '--', color=color, linewidth=linewidth, 
                    label=f'{var_name} +100% (Moy.Géom: {impact_score:.3f}dB, Max: {max_mag_diff:.2f}dB)', 
                    alpha=0.8)
        ax2.semilogx(freq_range, phase_mod, '--', color=color, linewidth=linewidth, 
                    label=f'{var_name} +100% (Moy.Géom: {impact_score:.3f}dB, Δ: {max_phase_diff:.1f}°)', 
                    alpha=0.8)
    
    ax1.set_ylabel('Magnitude (dB)', fontsize=12, fontweight='bold')
    ax1.set_title(f'Top {top_n} variables - Magnitude (hors sources V, classement par moy. géométrique)', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=10)
    
    ax2.set_xlabel('Fréquence (Hz)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Phase (degrés)', fontsize=12, fontweight='bold')
    ax2.set_title(f'Top {top_n} variables - Phase (hors sources V, classement par moy. géométrique)', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=10)
    
    plt.tight_layout()
    plt.show()

def simplify_transfer_function_symbolic(expression_str, component_values, sensitivity_results, max_error_db=5.0):
    """
    Simplifie la fonction de transfert en identifiant les variables peu sensibles et en créant une expression simplifiée
    """
    print(f"\n{'='*80}")
    print("SIMPLIFICATION SYMBOLIQUE DE LA FONCTION DE TRANSFERT")
    print(f"{'='*80}")

    # Trier par sensibilité croissante (les moins sensibles en premier)
    sorted_results = sorted(sensitivity_results, key=lambda x: x['impact_score'])

    # Fréquences de test pour validation
    test_frequencies = np.logspace(1, 7, 100)

    # Calculer la réponse de référence
    mag_ref, phase_ref = calculate_bode_full_range(expression_str, component_values, test_frequencies)
    if mag_ref is None:
        print("Erreur dans le calcul de référence")
        return None, None

    # Variables à conserver et à supprimer
    variables_to_remove = []
    current_component_values = component_values.copy()

    print(f"Expression originale : {expression_str}")
    print(f"Nombre de variables : {len(sensitivity_results)}")
    print(f"Seuil d'erreur maximum : {max_error_db} dB")

    # Essayer de supprimer les variables une par une, en commençant par les moins sensibles
    for i, result in enumerate(sorted_results):
        var_name = result['variable']
        var_value = result['original_value']
        impact_score = result['impact_score']

        print(f"\n[{i+1}/{len(sorted_results)}] Test de suppression : {var_name} (impact: {impact_score:.3f} dB)")

        # Créer une version de test en supprimant cette variable
        test_values = current_component_values.copy()

        # Stratégie de suppression selon le type de composant
        if var_name.startswith('C'):
            # Capacité : tendre vers 0 (circuit ouvert en AC)
            test_values[var_name] = 1e-15
        elif var_name.startswith('L'):
            # Inductance : tendre vers l'infini (circuit ouvert en AC)
            test_values[var_name] = 1e6
        elif var_name.startswith('R'):
            # Résistance : tendre vers l'infini (circuit ouvert)
            test_values[var_name] = 1e12
        else:
            # Autres variables (gm, ro, etc.) : mettre à 0 ou valeur neutre
            if 'gm' in var_name:
                test_values[var_name] = 0  # Pas de transconductance
            elif 'ro' in var_name:
                test_values[var_name] = 1e12  # Résistance infinie
            else:
                test_values[var_name] = 0

        # Calculer la réponse avec cette modification
        mag_test, phase_test = calculate_bode_full_range(expression_str, test_values, test_frequencies)

        if mag_test is not None:
            # Calculer l'erreur maximale
            mag_error = np.max(np.abs(mag_test - mag_ref))
            phase_error = np.max(np.abs(phase_test - phase_ref))

            print(f"   Erreur magnitude : {mag_error:.3f} dB")
            print(f"   Erreur phase : {phase_error:.3f}°")

            # Si l'erreur est acceptable, marquer cette variable pour suppression
            if mag_error <= max_error_db:
                variables_to_remove.append((var_name, var_value, impact_score, mag_error))
                current_component_values = test_values.copy()  # Mettre à jour les valeurs courantes
                print(f"   ✓ Variable marquée pour suppression")

                # Recalculer la référence avec les nouvelles valeurs
                mag_ref, phase_ref = calculate_bode_full_range(expression_str, current_component_values, test_frequencies)
            else:
                print(f"   ✗ Variable conservée (erreur > {max_error_db} dB)")
        else:
            print(f"   ✗ Erreur de calcul")

    # Créer l'expression symbolique simplifiée en substituant les variables supprimées
    original_expr = sp.sympify(expression_str)
    simplified_expr = original_expr

    print(f"\nCréation de l'expression symbolique simplifiée...")

    for var_name, var_value, impact_score, mag_error in variables_to_remove:
        var_symbol = symbols(var_name)
        if var_symbol in simplified_expr.free_symbols:
            try:
                # Substituer par une valeur appropriée selon le type de composant
                if var_name.startswith('C'):
                    # Capacité négligeable -> 0 (circuit ouvert en AC)
                    simplified_expr = simplified_expr.subs(var_symbol, 0)
                    print(f"   Supprimé capacité {var_name} (-> 0)")
                elif var_name.startswith('L'):
                    # Inductance -> 0 (court-circuit en DC, mais ici on simplifie)
                    simplified_expr = simplified_expr.subs(var_symbol, 0)
                    print(f"   Supprimé inductance {var_name} (-> 0)")
                elif var_name.startswith('R'):
                    # Résistance très grande -> on peut souvent la négliger
                    # On essaie d'abord de la mettre à 0, sinon on la garde
                    temp_expr = simplified_expr.subs(var_symbol, 0)
                    if temp_expr != 0 and not temp_expr.has(sp.zoo) and not temp_expr.has(sp.oo):
                        simplified_expr = temp_expr
                        print(f"   Supprimé résistance {var_name} (-> 0)")
                    else:
                        print(f"   Résistance {var_name} conservée (suppression causerait division par 0)")
                        continue
                else:
                    # Autres variables (gm, ro, etc.) -> 0
                    simplified_expr = simplified_expr.subs(var_symbol, 0)
                    print(f"   Supprimé {var_name} (-> 0)")
            except Exception as e:
                print(f"   Erreur lors de la suppression de {var_name}: {e}")
                continue

    # Simplifier l'expression résultante
    try:
        simplified_expr = sp.simplify(simplified_expr)
        simplified_str = str(simplified_expr)
    except Exception as e:
        print(f"   Erreur lors de la simplification : {e}")
        simplified_str = str(simplified_expr)

    # Résultats de la simplification
    print(f"\n{'='*80}")
    print("RÉSULTATS DE LA SIMPLIFICATION SYMBOLIQUE")
    print(f"{'='*80}")

    variables_kept = len(sensitivity_results) - len(variables_to_remove)

    print(f"Variables supprimées : {len(variables_to_remove)}")
    print(f"Variables conservées : {variables_kept}")

    if variables_to_remove:
        print(f"\nVariables supprimées de l'expression symbolique :")
        print(f"{'Variable':<12} {'Valeur orig.':<15} {'Impact (dB)':<12} {'Erreur (dB)':<12}")
        print("-" * 55)
        for var_name, var_value, impact, error in variables_to_remove:
            print(f"{var_name:<12} {var_value:<15.3e} {impact:<12.3f} {error:<12.3f}")

    # Calculer la réduction de complexité
    original_vars = len(sensitivity_results)
    reduction_percent = (len(variables_to_remove) / original_vars) * 100

    print(f"\nRéduction de complexité : {reduction_percent:.1f}% ({len(variables_to_remove)}/{original_vars} variables supprimées)")

    # Comparer les longueurs d'expression
    original_length = len(expression_str)
    simplified_length = len(simplified_str)
    length_reduction = ((original_length - simplified_length) / original_length) * 100

    print(f"Longueur originale : {original_length} caractères")
    print(f"Longueur simplifiée : {simplified_length} caractères")
    print(f"Réduction de longueur : {length_reduction:.1f}%")

    # Afficher l'expression simplifiée
    print(f"\nExpression symbolique simplifiée :")
    print(f"{simplified_str}")

    return simplified_str, variables_to_remove

def plot_original_vs_simplified(original_expr, simplified_expr, component_values):
    """
    Compare les diagrammes de Bode de l'expression originale et simplifiée
    """
    print("\nGénération du diagramme de comparaison original vs simplifié...")

    # Plage de fréquences
    freq_range = np.logspace(1, 7, 1000)

    # Calculer la réponse de l'expression originale
    mag_orig, phase_orig = calculate_bode_full_range(original_expr, component_values, freq_range)

    # Calculer la réponse de l'expression simplifiée
    mag_simp, phase_simp = calculate_bode_full_range(simplified_expr, component_values, freq_range)

    if mag_orig is None or mag_simp is None:
        print("Erreur dans le calcul des réponses de Bode")
        return

    # Calculer les erreurs
    mag_error = np.abs(mag_simp - mag_orig)
    phase_error = np.abs(phase_simp - phase_orig)
    max_mag_error = np.max(mag_error)
    max_phase_error = np.max(phase_error)
    avg_mag_error = np.mean(mag_error)
    avg_phase_error = np.mean(phase_error)

    # Créer la figure avec 3 sous-graphiques
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12))

    # Graphique 1 : Magnitude
    ax1.semilogx(freq_range, mag_orig, 'b-', linewidth=3, label='Expression originale', alpha=0.8)
    ax1.semilogx(freq_range, mag_simp, 'r--', linewidth=2, label='Expression simplifiée', alpha=0.8)
    ax1.set_ylabel('Magnitude (dB)', fontsize=12, fontweight='bold')
    ax1.set_title(f'Comparaison Magnitude - Erreur max: {max_mag_error:.3f} dB, Erreur moy: {avg_mag_error:.3f} dB',
                  fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=11)

    # Graphique 2 : Phase
    ax2.semilogx(freq_range, phase_orig, 'b-', linewidth=3, label='Expression originale', alpha=0.8)
    ax2.semilogx(freq_range, phase_simp, 'r--', linewidth=2, label='Expression simplifiée', alpha=0.8)
    ax2.set_ylabel('Phase (degrés)', fontsize=12, fontweight='bold')
    ax2.set_title(f'Comparaison Phase - Erreur max: {max_phase_error:.3f}°, Erreur moy: {avg_phase_error:.3f}°',
                  fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=11)

    # Graphique 3 : Erreurs
    ax3.semilogx(freq_range, mag_error, 'g-', linewidth=2, label=f'Erreur magnitude (max: {max_mag_error:.3f} dB)')
    ax3_phase = ax3.twinx()
    ax3_phase.semilogx(freq_range, phase_error, 'm-', linewidth=2, label=f'Erreur phase (max: {max_phase_error:.3f}°)')

    ax3.set_xlabel('Fréquence (Hz)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Erreur Magnitude (dB)', fontsize=11, color='g')
    ax3_phase.set_ylabel('Erreur Phase (°)', fontsize=11, color='m')
    ax3.set_title('Erreurs entre Original et Simplifié', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)

    # Légendes pour le graphique d'erreur
    lines1, labels1 = ax3.get_legend_handles_labels()
    lines2, labels2 = ax3_phase.get_legend_handles_labels()
    ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=10)

    # Ajuster les couleurs des axes
    ax3.tick_params(axis='y', labelcolor='g')
    ax3_phase.tick_params(axis='y', labelcolor='m')

    plt.tight_layout()

    # Ajouter des informations textuelles
    info_text = f"Expressions comparées:\n"
    info_text += f"Originale: {len(original_expr)} caractères\n"
    info_text += f"Simplifiée: {len(simplified_expr)} caractères\n"
    reduction = ((len(original_expr) - len(simplified_expr)) / len(original_expr)) * 100
    info_text += f"Réduction: {reduction:.1f}%"

    ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes,
             verticalalignment='top', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.show()

    # Afficher les statistiques détaillées
    print(f"\n{'='*60}")
    print("STATISTIQUES DE COMPARAISON")
    print(f"{'='*60}")
    print(f"Erreur magnitude maximale : {max_mag_error:.3f} dB")
    print(f"Erreur magnitude moyenne : {avg_mag_error:.3f} dB")
    print(f"Erreur phase maximale : {max_phase_error:.3f}°")
    print(f"Erreur phase moyenne : {avg_phase_error:.3f}°")

    # Trouver les fréquences où l'erreur est maximale
    max_mag_freq = freq_range[np.argmax(mag_error)]
    max_phase_freq = freq_range[np.argmax(phase_error)]
    print(f"Fréquence d'erreur magnitude max : {max_mag_freq:.1e} Hz")
    print(f"Fréquence d'erreur phase max : {max_phase_freq:.1e} Hz")

def save_simplified_expression(simplified_expr, original_file_path, removed_vars):
    """Sauvegarde l'expression simplifiée dans un nouveau fichier"""
    if simplified_expr is None:
        return

    # Créer le nom du fichier de sortie
    base_name = original_file_path.replace('.txt', '')
    output_file = f"{base_name}_simplified.txt"

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Expression de transfert symbolique simplifiée\n")
            f.write(f"# Variables supprimées : {len(removed_vars)}\n")
            f.write("# Variables supprimées de l'expression :\n")
            for var_name, var_value, impact, error in removed_vars:
                f.write(f"# {var_name} (valeur originale: {var_value:.3e}, impact: {impact:.3f} dB, erreur: {error:.3f} dB)\n")
            f.write("\n")
            f.write(simplified_expr)

        print(f"\nExpression symbolique simplifiée sauvegardée dans : {output_file}")
        return output_file
    except Exception as e:
        print(f"Erreur lors de la sauvegarde : {e}")
        return None

def main():
    # Utiliser le fichier FTC.txt par défaut
    default_file = r"C:\Users\<USER>\Documents\Julia\FTC.txt"
    file_input = input(f"Fichier expression [{default_file}]: ").strip().strip('"')
    file_path = file_input if file_input else default_file

    expression = read_symbolic_expression(file_path)
    if expression is None:
        print("Impossible de lire le fichier")
        return

    variables = extract_variables(expression)
    component_values = define_component_values(variables)

    print(f"Expression analysée : {expression}")
    print(f"Variables trouvées : {list(component_values.keys())}")

    # Demander le mode d'analyse
    print("\nModes d'analyse disponibles :")
    print("1. Analyse parallèle (recommandé)")
    print("2. Analyse séquentielle")
    print("3. Comparaison des deux modes")

    mode = input("\nVotre choix (1/2/3) [défaut: 1]: ").strip()
    if not mode:
        mode = "1"

    # Demander le nombre de processus pour le mode parallèle
    if mode in ["1", "3"]:
        num_cores = mp.cpu_count()
        print(f"\nNombre de cœurs détectés : {num_cores}")
        num_proc = input(f"Nombre de processus à utiliser [défaut: {min(num_cores, 8)}]: ").strip()
        if num_proc:
            try:
                num_processes = int(num_proc)
            except:
                num_processes = min(num_cores, 8)
        else:
            num_processes = min(num_cores, 8)

    sensitivity_results = None

    if mode == "1":
        # Analyse parallèle uniquement
        sensitivity_results = analyze_variable_sensitivity_parallel(expression, component_values, num_processes)

    elif mode == "2":
        # Analyse séquentielle uniquement
        sensitivity_results = analyze_variable_sensitivity_sequential(expression, component_values)

    elif mode == "3":
        # Comparaison des deux modes
        print("\n" + "="*80)
        print("COMPARAISON DES MODES D'ANALYSE")
        print("="*80)

        # Analyse séquentielle
        print("\n1. ANALYSE SÉQUENTIELLE")
        print("-" * 40)
        seq_results = analyze_variable_sensitivity_sequential(expression, component_values)

        # Analyse parallèle
        print("\n2. ANALYSE PARALLÈLE")
        print("-" * 40)
        par_results = analyze_variable_sensitivity_parallel(expression, component_values, num_processes)

        # Utiliser les résultats parallèles pour la suite
        sensitivity_results = par_results

        # Vérifier la cohérence des résultats
        if seq_results and par_results:
            print("\n3. VÉRIFICATION DE COHÉRENCE")
            print("-" * 40)

            seq_dict = {r['variable']: r['impact_score'] for r in seq_results}
            par_dict = {r['variable']: r['impact_score'] for r in par_results}

            max_diff = 0
            for var in seq_dict:
                if var in par_dict:
                    diff = abs(seq_dict[var] - par_dict[var])
                    max_diff = max(max_diff, diff)

            print(f"Différence maximale entre les scores : {max_diff:.6f} dB")
            if max_diff < 1e-10:
                print("✓ Les résultats sont identiques")
            else:
                print("⚠ Différences détectées (probablement dues aux arrondis)")

    if sensitivity_results is None:
        print("Erreur dans l'analyse de sensibilité")
        return

    # Demander si l'utilisateur veut simplifier la fonction de transfert
    print("\n" + "="*80)
    print("OPTIONS DE SIMPLIFICATION")
    print("="*80)

    simplify = input("\nVoulez-vous simplifier la fonction de transfert ? (o/n) [défaut: o]: ").strip().lower()
    if simplify != 'n':
        # Demander le seuil d'erreur
        error_input = input("Seuil d'erreur maximum en dB [défaut: 5.0]: ").strip()
        try:
            max_error_db = float(error_input) if error_input else 5.0
        except:
            max_error_db = 5.0

        # Simplifier la fonction de transfert
        simplified_expr, removed_vars = simplify_transfer_function_symbolic(expression, component_values, sensitivity_results, max_error_db)

        if simplified_expr and removed_vars:
            # Demander si on veut afficher la comparaison Bode
            compare = input("\nAfficher la comparaison Bode original vs simplifié ? (o/n) [défaut: o]: ").strip().lower()
            if compare != 'n':
                plot_original_vs_simplified(expression, simplified_expr, component_values)

            # Demander si on veut sauvegarder
            save = input("\nSauvegarder l'expression simplifiée ? (o/n) [défaut: o]: ").strip().lower()
            if save != 'n':
                save_simplified_expression(simplified_expr, file_path, removed_vars)
        elif simplified_expr:
            print("Aucune variable n'a pu être supprimée avec le seuil d'erreur spécifié.")
        else:
            print("Erreur lors de la simplification.")

    print("\n" + "="*80)
    print("GÉNÉRATION DES GRAPHIQUES")
    print("="*80)

    # Demander à l'utilisateur quel type de graphique il veut
    print("\nOptions d'affichage de l'analyse de sensibilité :")
    print("1. Toutes les variables sur un seul Bode")
    print("2. Seulement les 10 variables les plus sensibles")
    print("3. Les deux")
    print("4. Aucun graphique")

    choice = input("\nVotre choix (1/2/3/4) [défaut: 4]: ").strip()
    if not choice:
        choice = "4"

    if choice in ["1", "3"]:
        print("\n1. Affichage de toutes les variations sur un seul diagramme de Bode...")
        plot_all_variations_single_bode(expression, component_values, sensitivity_results)

    if choice in ["2", "3"]:
        print("\n2. Affichage des variables les plus sensibles...")
        plot_top_variations_only(expression, component_values, sensitivity_results, top_n=10)

    print("\nAnalyse terminée !")

if __name__ == "__main__":
    # Protection nécessaire pour le multiprocessing sur Windows
    mp.freeze_support()
    main()
