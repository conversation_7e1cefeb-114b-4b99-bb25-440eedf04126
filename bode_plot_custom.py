#!/usr/bin/env python3
"""
Script pour tracer le diagramme de Bode d'une fonction de transfert personnalisée
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp

def parse_custom_transfer_function():
    """Parse la fonction de transfert donnée par l'utilisateur"""
    
    # Fonction de transfert donnée:
    # -3.11709091309e+36/(1.55859221291e+36+2.48050213442e+24*s+4.96100426885e+24*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+14*s^2+7.89568352087e+14*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+14*s^2+7.89568352087e+14*s^2+394784176044*s^2+789568352087*s^2+62831.8530718*s^3+125663.706144*s^3+2.48050213442e+29*s+4.96100426885e+29*s+3.94784176044e+22*s^2+7.89568352087e+22*s^2+3.94784176044e+19*s^2+7.89568352087e+19*s^2+6.28318530718e+12*s^3+1.25663706144e+13*s^3+3.94784176044e+19*s^2+7.89568352087e+19*s^2+6.28318530718e+12*s^3+1.25663706144e+13*s^3+6283185307.18*s^3+12566370614.4*s^3+1000.0*s^4+2000.0*s^4)
    
    # Numérateur
    num = -3.11709091309e+36
    
    # Coefficients du dénominateur (regroupés par puissance de s)
    # Terme constant (s^0)
    coeff_s0 = 1.55859221291e+36
    
    # Termes en s^1
    coeff_s1 = (2.48050213442e+24 + 4.96100426885e+24 + 2.48050213442e+21 + 
                4.96100426885e+21 + 2.48050213442e+21 + 4.96100426885e+21 + 
                2.48050213442e+29 + 4.96100426885e+29)
    
    # Termes en s^2
    coeff_s2 = (3.94784176044e+14 + 7.89568352087e+14 + 3.94784176044e+14 + 
                7.89568352087e+14 + 394784176044 + 789568352087 + 
                3.94784176044e+22 + 7.89568352087e+22 + 3.94784176044e+19 + 
                7.89568352087e+19 + 3.94784176044e+19 + 7.89568352087e+19)
    
    # Termes en s^3
    coeff_s3 = (62831.8530718 + 125663.706144 + 6.28318530718e+12 + 
                1.25663706144e+13 + 6.28318530718e+12 + 1.25663706144e+13 + 
                6283185307.18 + 12566370614.4)
    
    # Termes en s^4
    coeff_s4 = 1000.0 + 2000.0
    
    print("=== Fonction de transfert personnalisée ===")
    print(f"Numérateur: {num:.2e}")
    print(f"Coefficients du dénominateur:")
    print(f"  s^4: {coeff_s4:.2e}")
    print(f"  s^3: {coeff_s3:.2e}")
    print(f"  s^2: {coeff_s2:.2e}")
    print(f"  s^1: {coeff_s1:.2e}")
    print(f"  s^0: {coeff_s0:.2e}")
    
    # Coefficients pour scipy (ordre décroissant: s^4, s^3, s^2, s^1, s^0)
    den_coeffs = [coeff_s4, coeff_s3, coeff_s2, coeff_s1, coeff_s0]
    
    return num, den_coeffs

def create_transfer_function(num, den_coeffs):
    """Crée une fonction de transfert scipy"""
    
    # Le numérateur est une constante
    num_coeffs = [num]
    
    # Créer la fonction de transfert
    tf = signal.TransferFunction(num_coeffs, den_coeffs)
    
    print(f"\n=== Fonction de transfert scipy ===")
    print(f"Numérateur: {tf.num}")
    print(f"Dénominateur: {tf.den}")
    
    return tf

def plot_bode_diagram(tf):
    """Trace le diagramme de Bode"""
    
    # Gamme de fréquences (0.1 Hz à 1 THz)
    frequencies = np.logspace(-1, 12, 10000)  # 0.1 Hz à 1 THz
    omega = 2 * np.pi * frequencies
    
    print(f"\n=== Calcul de la réponse fréquentielle ===")
    print("Calcul en cours...")
    
    # Calcul de la réponse fréquentielle
    w, h = signal.freqresp(tf, omega)
    
    # Conversion en dB et phase en degrés
    magnitude_db = 20 * np.log10(np.abs(h))
    phase_deg = np.angle(h) * 180 / np.pi
    
    print("Calcul terminé.")
    
    # Création du graphique
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Graphique de magnitude
    ax1.semilogx(frequencies, magnitude_db, 'b-', linewidth=2)
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - Fonction de transfert personnalisée')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(0.1, 1e12)
    
    # Graphique de phase
    ax2.semilogx(frequencies, phase_deg, 'r-', linewidth=2)
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(0.1, 1e12)
    
    plt.tight_layout()
    plt.savefig('bode_diagram_custom.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Affichage des informations importantes
    print(f"\n=== Analyse du diagramme de Bode ===")
    
    # Trouver les indices pour différentes fréquences
    idx_1hz = np.argmin(np.abs(frequencies - 1))
    idx_1khz = np.argmin(np.abs(frequencies - 1e3))
    idx_1mhz = np.argmin(np.abs(frequencies - 1e6))
    idx_1ghz = np.argmin(np.abs(frequencies - 1e9))
    
    print(f"Gain à 1 Hz: {magnitude_db[idx_1hz]:.1f} dB")
    print(f"Gain à 1 kHz: {magnitude_db[idx_1khz]:.1f} dB")
    print(f"Gain à 1 MHz: {magnitude_db[idx_1mhz]:.1f} dB")
    print(f"Gain à 1 GHz: {magnitude_db[idx_1ghz]:.1f} dB")
    
    # Recherche des fréquences de coupure (-3dB par rapport au gain maximum)
    gain_max = np.max(magnitude_db)
    cutoff_level = gain_max - 3
    
    print(f"\nGain maximum: {gain_max:.1f} dB")
    print(f"Niveau de coupure (-3dB): {cutoff_level:.1f} dB")
    
    cutoff_freqs = []
    for i in range(1, len(magnitude_db)):
        if ((magnitude_db[i] <= cutoff_level and magnitude_db[i-1] > cutoff_level) or
            (magnitude_db[i] >= cutoff_level and magnitude_db[i-1] < cutoff_level)):
            # Interpolation linéaire pour plus de précision
            f_cutoff = frequencies[i-1] + (frequencies[i] - frequencies[i-1]) * \
                      (cutoff_level - magnitude_db[i-1]) / (magnitude_db[i] - magnitude_db[i-1])
            cutoff_freqs.append(f_cutoff)
    
    print(f"\nFréquences de coupure (-3dB) détectées:")
    for i, fc in enumerate(cutoff_freqs):
        if fc < 1e3:
            print(f"  Coupure {i+1}: {fc:.1f} Hz")
        elif fc < 1e6:
            print(f"  Coupure {i+1}: {fc/1e3:.1f} kHz")
        elif fc < 1e9:
            print(f"  Coupure {i+1}: {fc/1e6:.1f} MHz")
        else:
            print(f"  Coupure {i+1}: {fc/1e9:.2f} GHz")

def main():
    """Fonction principale"""
    
    print("=== Analyse de la fonction de transfert personnalisée ===")
    print("H(s) = -3.11709091309e+36 / (polynôme de degré 4)")
    
    # Parse la fonction de transfert
    num, den_coeffs = parse_custom_transfer_function()
    
    # Créer la fonction de transfert scipy
    tf = create_transfer_function(num, den_coeffs)
    
    # Tracer le diagramme de Bode
    plot_bode_diagram(tf)
    
    print(f"\n=== Fichier sauvegardé ===")
    print("Le diagramme de Bode a été sauvegardé sous 'bode_diagram_custom.png'")

if __name__ == "__main__":
    main()
