# Correction du problème ro_Q1 utilisant la valeur par défaut

## Problème identifié

L'utilisateur a remarqué que dans les substitutions numériques, `ro_Q1` utilisait la valeur par défaut (1000) au lieu de la valeur calculée dans la section "Transistor BJT parameters".

## Analyse du problème

### Cause racine
Le problème venait des lignes 363 et 366 dans la fonction `ac_analysis` :

```python
except: values[f"ro_{name_q}"] = '100k'  # PROBLÈME ICI
```

Quand le calcul de `ro_Q1` échouait, la valeur par défaut assignée était `'100k'` (chaîne avec 'k').

### Chaîne d'erreurs
1. **Calcul de ro_Q1 échoue** → `values["ro_Q1"] = '100k'`
2. **Dans apply_consistent_substitution** → `xcas(f"evalf(100k)")` échoue
3. **Gestion d'erreur** → Utilise la valeur par défaut `'1000'`

### Pourquoi xcas(f"evalf(100k)") échoue
- `100k` n'est pas une expression numérique valide pour xcas
- Il faut `100*1000` ou `100000` pour que xcas puisse l'évaluer

## Solution appliquée

### Modification dans ac_analysis (lignes 363 et 366)
**Avant :**
```python
except: values[f"ro_{name_q}"] = '100k'
```

**Après :**
```python
except: values[f"ro_{name_q}"] = '100000'
```

### Résultat attendu
Maintenant, quand le calcul de `ro_Q1` échoue :
1. `values["ro_Q1"] = '100000'` (valeur numérique valide)
2. `xcas(f"evalf(100000)")` réussit → `'100000.0'`
3. La substitution utilise la valeur calculée au lieu de la valeur par défaut

## Vérification

Pour vérifier que la correction fonctionne, regarder dans les "Substitutions numeriques appliquees" :

**Avant la correction :**
```
ro_Q1 = 1000 (valeur par defaut)
```

**Après la correction :**
```
ro_Q1 = 100000.0
```

Ou si le calcul réussit vraiment :
```
ro_Q1 = [valeur calculée réelle]
```

## Impact

Cette correction garantit que :
1. **Cohérence** : `ro_Q1` affiché dans "Transistor BJT parameters" correspond à celui utilisé dans les substitutions
2. **Précision** : Les calculs utilisent la vraie valeur de résistance de sortie
3. **Transparence** : L'utilisateur voit la vraie valeur utilisée dans les calculs

## Note technique

Le problème était spécifique aux valeurs par défaut contenant des suffixes ('k', 'M', etc.) qui ne sont pas directement évaluables par xcas. La fonction `parse_value` existe pour convertir ces suffixes, mais n'était pas utilisée dans les cas d'exception.

Cette correction utilise directement la valeur numérique (100000) au lieu du suffixe ('100k') pour éviter les problèmes d'évaluation.
