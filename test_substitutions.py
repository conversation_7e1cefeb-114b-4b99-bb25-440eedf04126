#!/usr/bin/env python3
"""
Test script pour vérifier l'affichage des substitutions numériques
"""

# Simuler les fonctions nécessaires pour le test
def xcas(cmd):
    """Simulation de xcas pour les tests"""
    # Simuler quelques substitutions simples
    if "subst" in cmd and "Fc1_X1" in cmd:
        return cmd.replace("Fc1_X1", "100000000")
    elif "subst" in cmd and "Fc2_X1" in cmd:
        return cmd.replace("Fc2_X1", "1000000000")
    elif "subst" in cmd and "beta_Q1" in cmd:
        return cmd.replace("beta_Q1", "100")
    elif "subst" in cmd and "ro_Q1" in cmd:
        return cmd.replace("ro_Q1", "1000")
    elif "subst" in cmd and "k" in cmd:
        return cmd.replace("k", "1000")
    elif "subst" in cmd and "pi" in cmd:
        return cmd.replace("pi", "3.14159265359")
    elif "evalf" in cmd:
        return cmd.replace("evalf(simplify(", "").replace("))", ")")
    return cmd

def file_print(message="", end='\n\n'):
    print(message, end=end)

def force_fc_substitution(expression, values, return_substitutions=False):
    """Version de test de force_fc_substitution"""
    result = expression
    substitutions_applied = []
    for var, val in values.items():
        if var.startswith('Fc') and '_' in var:
            try:
                if var in str(result):
                    result = xcas(f"subst({result},{var},{val})")
                    substitutions_applied.append(f"{var} = {val}")
            except: 
                continue
    if return_substitutions:
        return result, substitutions_applied
    return result

def test_plot_bode_substitutions():
    """Test de la fonction apply_consistent_substitution modifiée"""
    
    # Fonction de substitution cohérente (copiée de plot_bode)
    def apply_consistent_substitution(expression, values):
        """Applique toutes les substitutions de manière cohérente"""

        result = expression
        substitutions_applied = []

        # 1. Appliquer force_fc_substitution
        result, fc_substitutions = force_fc_substitution(result, values, return_substitutions=True)
        substitutions_applied.extend(fc_substitutions)

        # 2. Variables prioritaires
        priority_vars = [k for k in values.keys() if k.startswith('ro_') or k.startswith('beta_') or k.startswith('Ic_ac_') or k.startswith('Av_') or k.startswith('Fc1_') or k.startswith('Fc2_') or k.startswith('Fc3_') or k.startswith('Fc4_')]

        for var in priority_vars:
            comp_value = values.get(var, '1000')
            try:
                comp_value_num = xcas(f"evalf({comp_value})")
                if var in str(result):
                    result = xcas(f"subst({result},{var},{comp_value_num})")
                    substitutions_applied.append(f"{var} = {comp_value_num}")
            except:
                if var.startswith('ro_'): default_val = '1000'
                elif var.startswith('beta_'): default_val = '100'
                elif var.startswith('Ic_ac_'): default_val = '0.001'
                elif var.startswith('Av_'): default_val = '100000'
                elif var.startswith('Fc1_'): default_val = '100000000'
                elif var.startswith('Fc2_'): default_val = '1000000000'
                elif var.startswith('Fc3_'): default_val = '10000000000'
                elif var.startswith('Fc4_'): default_val = '100000000000'
                else: default_val = '1000'
                if var in str(result):
                    result = xcas(f"subst({result},{var},{default_val})")
                    substitutions_applied.append(f"{var} = {default_val} (valeur par defaut)")

        # 3. Identifier les résistances de compensation BJT
        bjt_compensation_resistors = set()
        for var, val in values.items():
            if var.startswith('comp_BJT_res_') and val == 'true':
                resistor_name = var.replace('comp_BJT_res_', '')
                bjt_compensation_resistors.add(resistor_name)

        # 4. Substitution des autres composants
        for var, val in values.items():
            if var not in priority_vars and var != 'k' and not var.startswith('comp_BJT_'):
                if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                    try:
                        comp_value_num = xcas(f"evalf({val})")
                        if var in str(result):
                            result = xcas(f"subst({result},{var},{comp_value_num})")
                            substitutions_applied.append(f"{var} = {comp_value_num}")
                    except:
                        continue

        # 5. Substitutions finales
        if 'k' in str(result):
            result = xcas(f"subst({result},k,1000)")
            substitutions_applied.append("k = 1000")
        if 'pi' in str(result):
            result = xcas(f"subst({result},pi,3.14159265359)")
            substitutions_applied.append("pi = 3.14159265359")
        result = xcas(f"evalf(simplify({result}))")

        return result, substitutions_applied

    # Test avec une fonction de transfert exemple
    transfer_function = "100000 / ((1 + s / (2 * pi * Fc1_X1)) * (1 + s / (2 * pi * Fc2_X1)) * (1 + s / (2 * pi * Fc3_X1)) * (1 + s / (2 * pi * Fc4_X1))) * beta_Q1 / (ro_Q1 + R1)"
    
    values = {
        'Fc1_X1': '100000000',
        'Fc2_X1': '1000000000', 
        'Fc3_X1': '10000000000',
        'Fc4_X1': '100000000000',
        'beta_Q1': '100',
        'ro_Q1': '1000',
        'R1': '1000',
        'Av_X1': '100000'
    }
    
    print("=== TEST DES SUBSTITUTIONS NUMERIQUES ===")
    print(f"Fonction de transfert originale:")
    print(f"  {transfer_function}")
    print()
    
    # Appliquer les substitutions cohérentes
    tf_final, substitutions_list = apply_consistent_substitution(transfer_function, values)
    
    # Afficher la fonction de transfert finale
    print(f"Fonction de transfert finale: {tf_final}")
    print()
    
    # Afficher les substitutions appliquées
    if substitutions_list:
        print("Substitutions numeriques appliquees:")
        for substitution in substitutions_list:
            print(f"  {substitution}")
    else:
        print("Aucune substitution numerique appliquee")
    
    print("\n=== TEST REUSSI ===")
    print("Les substitutions sont maintenant affichees lors des print des expressions de fonction de transfert!")

if __name__ == '__main__':
    test_plot_bode_substitutions()
