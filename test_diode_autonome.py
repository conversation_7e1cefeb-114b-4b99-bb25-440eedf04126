#!/usr/bin/env python3
"""
Test autonome pour vérifier l'analyse avancée des diodes
Sans dépendance externe, simule le comportement attendu
"""

def parse_value(value_str):
    """Fonction de parsing simplifiée"""
    value_str = str(value_str).strip()
    if value_str[-1].lower() in 'kmunpfg' or value_str[-1].upper() in 'MG':
        multiplier = {'k': 1e3, 'm': 1e-3, 'u': 1e-6, 'n': 1e-9, 'p': 1e-12, 'f': 1e-15, 'M': 1e6, 'G': 1e9, 'g': 1e9}[value_str[-1]]
        return str(float(value_str[:-1]) * multiplier)
    try: 
        return str(float(value_str))
    except: 
        return value_str

def analyser_diode_avancee(netlist_lines, dc_voltages, dc_currents):
    """
    Simulation de l'analyse avancée des diodes
    """
    print("=== Test autonome de l'analyse avancée des diodes ===")
    
    # Parser la netlist
    netlist = [line.split() for line in netlist_lines]
    values = {}
    
    # Traitement des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type == 'D':
            values[f"Vth_{comp_name}"] = parse_value(comp[3]) if len(comp) > 3 else "0.7"
            values[f"Vt_{comp_name}"] = "0.026"
            values[f"rd_{comp_name}"] = "10"  # Sera recalculé
    
    # Analyse des diodes
    diode_components = [comp for comp in netlist if comp[0][0].upper() == 'D']
    diode_states = {}
    
    print(f"Nombre de diodes trouvées: {len(diode_components)}")
    print()
    
    for comp in diode_components:
        name_d = comp[0]
        n_anode, n_cathode = comp[1], comp[2]
        
        print(f"=== Analyse de la diode {name_d} ===")
        print(f"Connexions: anode={n_anode}, cathode={n_cathode}")
        
        # Récupérer les valeurs DC
        Id_dc_val = dc_currents.get(name_d, '0')
        Vth_val = values.get(f"Vth_{name_d}", '0.7')
        Vt_val = values.get(f"Vt_{name_d}", '0.026')
        
        # Calculer la tension DC aux bornes de la diode
        try:
            V_anode_dc = float(dc_voltages.get(n_anode, '0')) if n_anode != '0' else 0.0
            V_cathode_dc = float(dc_voltages.get(n_cathode, '0')) if n_cathode != '0' else 0.0
            V_diode_dc = V_anode_dc - V_cathode_dc
        except:
            V_diode_dc = 0.0
            
        try:
            Id_dc_numeric = abs(float(Id_dc_val))
            Vt_numeric = float(Vt_val)
            Vth_numeric = float(Vth_val)
            
            print(f"Paramètres:")
            print(f"  Vth = {Vth_numeric} V")
            print(f"  Vt = {Vt_numeric} V")
            print(f"  V_dc aux bornes = {V_diode_dc:.3f} V")
            print(f"  Id_dc = {Id_dc_numeric:.6f} A")
            
            # Déterminer l'état de la diode
            seuil_conduction = 1e-9  # 1 nA
            if Id_dc_numeric > seuil_conduction and V_diode_dc > 0.5 * Vth_numeric:
                # Diode en conduction
                etat = "CONDUCTRICE"
                rd_calculated = Vt_numeric / Id_dc_numeric
                
                print(f"État: {etat}")
                print(f"  Critère courant: {Id_dc_numeric:.2e} A > {seuil_conduction:.2e} A ✓")
                print(f"  Critère tension: {V_diode_dc:.3f} V > {0.5 * Vth_numeric:.3f} V ✓")
                print(f"  rd calculée = {Vt_numeric} / {Id_dc_numeric:.6f} = {rd_calculated:.3f} Ω")
                
                # Estimer l'amplitude AC (simulation simple)
                amplitude_ac_estimee = 0.03  # Estimation pour le test
                limite_petit_signal = 0.1 * Vt_numeric  # 2.6 mV
                
                print(f"Analyse AC:")
                print(f"  Amplitude AC estimée: {amplitude_ac_estimee:.6f} V")
                print(f"  Limite petit signal: {limite_petit_signal:.6f} V")
                
                if amplitude_ac_estimee < limite_petit_signal:
                    regime = "PETIT SIGNAL"
                    fiabilite = "FIABLE"
                    print(f"  Régime: {regime} ✓")
                    print(f"  Analyse AC: {fiabilite}")
                else:
                    regime = "GRAND SIGNAL"
                    fiabilite = "APPROXIMATIVE"
                    print(f"  Régime: {regime} ⚠️")
                    print(f"  Analyse AC: {fiabilite}")
                    print(f"  ATTENTION: Résultats AC approximatifs")
                
                values[f"rd_{name_d}"] = str(rd_calculated)
                
            else:
                # Diode bloquée
                etat = "BLOQUEE"
                regime = "BLOQUE"
                print(f"État: {etat}")
                if Id_dc_numeric <= seuil_conduction:
                    print(f"  Critère courant: {Id_dc_numeric:.2e} A ≤ {seuil_conduction:.2e} A")
                if V_diode_dc <= 0.5 * Vth_numeric:
                    print(f"  Critère tension: {V_diode_dc:.3f} V ≤ {0.5 * Vth_numeric:.3f} V")
                print(f"  rd = 1TΩ (circuit ouvert en AC)")
                values[f"rd_{name_d}"] = "1e12"
                fiabilite = "CIRCUIT OUVERT"
            
            # Stocker l'état
            diode_states[name_d] = {
                'etat': etat,
                'regime': regime,
                'V_dc': V_diode_dc,
                'Id_dc': Id_dc_numeric,
                'rd': values[f"rd_{name_d}"],
                'fiabilite': fiabilite
            }
            
        except Exception as e:
            print(f"Erreur lors de l'analyse: {e}")
            values[f"rd_{name_d}"] = "10"
            diode_states[name_d] = {'etat': 'ERREUR', 'regime': 'ERREUR'}
        
        print()
    
    # Résumé
    print("=== Résumé de l'analyse ===")
    conductrices = [d for d, s in diode_states.items() if s['etat'] == 'CONDUCTRICE']
    bloquees = [d for d, s in diode_states.items() if s['etat'] == 'BLOQUEE']
    grand_signal = [d for d, s in diode_states.items() if s.get('regime') == 'GRAND SIGNAL']
    
    if conductrices:
        print(f"Diodes conductrices: {', '.join(conductrices)}")
    if bloquees:
        print(f"Diodes bloquées: {', '.join(bloquees)}")
    
    if grand_signal:
        print(f"⚠️  Diodes en régime grand signal: {', '.join(grand_signal)}")
        print("L'analyse AC linéaire peut donner des résultats approximatifs")
    else:
        print("✓ Toutes les diodes en régime petit signal - analyse AC fiable")
    
    return diode_states, values

def test_circuit_simple():
    """Test avec le circuit simple V1-R1-D1"""
    print("Circuit test: V1(5V DC + 3V AC) → R1(100Ω) → D1(2.2V) → GND")
    print()
    
    netlist_lines = [
        "V1 1 0 DC 5 AC 3",
        "R1 1 2 100", 
        "D1 2 0 2.2"
    ]
    
    # Résultats DC simulés (calculés manuellement)
    dc_voltages = {
        '1': '5.0',
        '2': '2.2'
    }
    
    dc_currents = {
        'D1': '0.028'  # (5-2.2)/100 = 0.028 A
    }
    
    diode_states, values = analyser_diode_avancee(netlist_lines, dc_voltages, dc_currents)
    
    print("\n=== Vérification des résultats ===")
    d1_state = diode_states.get('D1', {})
    print(f"D1 état: {d1_state.get('etat', 'INCONNU')}")
    print(f"D1 régime: {d1_state.get('regime', 'INCONNU')}")
    print(f"D1 rd: {values.get('rd_D1', 'INCONNU')} Ω")
    
    # Vérification attendue
    expected_rd = 0.026 / 0.028  # Vt / Id_dc
    print(f"rd attendue: {expected_rd:.3f} Ω")
    
    if abs(float(values.get('rd_D1', '0')) - expected_rd) < 0.01:
        print("✓ Calcul de rd correct")
    else:
        print("✗ Erreur dans le calcul de rd")

if __name__ == "__main__":
    test_circuit_simple()
