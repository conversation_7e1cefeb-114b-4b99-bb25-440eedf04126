#!/usr/bin/env python3
"""
Test de la fonctionnalité d'ajout intelligent de termes dominants
"""

import sys
import os
import numpy as np
import sympy as sp

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bode_H1_final import *

def test_intelligent_add():
    """Test de l'ajout intelligent de termes"""
    
    print("=== Test de l'ajout intelligent de termes ===\n")
    
    # Expression de test complexe
    test_expr = "gm_Q1*R2/(R1*C1*s + 1) + 1/(R3*C2*s + R4*C3*s + 1)"
    print(f"Expression de test: {test_expr}")
    
    # Variables et valeurs
    variables = extract_variables_from_expression(test_expr)
    print(f"Variables: {variables}")
    
    component_values = {
        'gm_Q1': 0.1, 'R1': 1000.0, 'C1': 1e-6, 'R2': 2000.0,
        'R3': 1500.0, 'C2': 2e-6, 'R4': 500.0, 'C3': 5e-7
    }
    
    print(f"Valeurs des composants: {component_values}")
    
    # Test de validation d'expression
    print(f"\n=== Test de validation d'expression ===")
    
    # Créer une instance temporaire pour tester la validation
    class TestValidator:
        def validate_expression_coherence(self, expr):
            """Valide la cohérence mathématique d'une expression"""
            try:
                # Test 1: L'expression peut-elle être évaluée ?
                test_values = {var: 1.0 for var in extract_variables_from_expression(str(expr))}
                test_values['s'] = 1j * 2 * np.pi * 1000  # Test à 1kHz
                
                # Substituer et évaluer
                test_expr = expr
                for var, val in test_values.items():
                    if var != 's':
                        test_expr = test_expr.subs(sp.symbols(var), val)
                
                result = complex(test_expr.subs(sp.symbols('s'), test_values['s']))
                
                # Test 2: Le résultat est-il fini et raisonnable ?
                if not (np.isfinite(result.real) and np.isfinite(result.imag)):
                    return False
                
                # Test 3: L'amplitude n'est-elle pas aberrante ?
                magnitude = abs(result)
                if magnitude > 1e10 or magnitude < 1e-10:
                    return False
                
                return True
                
            except Exception as e:
                print(f"  Erreur validation: {e}")
                return False
    
    validator = TestValidator()
    
    # Test avec expression valide
    valid_expr = sp.sympify("1/(R1*C1*s + 1)")
    is_valid = validator.validate_expression_coherence(valid_expr)
    print(f"Expression valide: {valid_expr} -> {is_valid}")
    
    # Test avec expression problématique
    try:
        problematic_expr = sp.sympify("1/(0*s + 0)")  # Division par zéro potentielle
        is_valid = validator.validate_expression_coherence(problematic_expr)
        print(f"Expression problématique: {problematic_expr} -> {is_valid}")
    except:
        print("Expression problématique: erreur de parsing")
    
    # Test de reconstruction d'expression
    print(f"\n=== Test de reconstruction d'expression ===")
    
    original = sp.sympify("1/(R1*C1*s + 1)")
    term_to_add = sp.sympify("R2*C2*s")
    
    print(f"Expression originale: {original}")
    print(f"Terme à ajouter au dénominateur: {term_to_add}")
    
    # Reconstruction manuelle
    num, den = original.as_numer_denom()
    reconstructed = num / (den + term_to_add)
    simplified = sp.simplify(reconstructed)
    
    print(f"Expression reconstruite: {simplified}")
    
    # Test de validation
    is_coherent = validator.validate_expression_coherence(simplified)
    print(f"Cohérence: {is_coherent}")
    
    # Test d'évaluation de précision
    print(f"\n=== Test d'évaluation de précision ===")
    
    test_frequencies = np.array([10, 100, 1000, 10000])
    
    # Expression de référence
    ref_expr = "1/(R1*C1*s + 1) + R2/(R3*C2*s + 1)"
    mag_ref, phase_ref = calculate_bode_at_frequencies(ref_expr, component_values, test_frequencies)
    
    # Expression simplifiée
    simple_expr = "1/(R1*C1*s + 1)"
    mag_simple, phase_simple = calculate_bode_at_frequencies(simple_expr, component_values, test_frequencies)
    
    if mag_ref is not None and mag_simple is not None:
        error = np.max(np.abs(mag_simple - mag_ref))
        print(f"Erreur entre référence et simplifiée: {error:.3f} dB")
        
        print(f"Fréquences (Hz): {test_frequencies}")
        print(f"Magnitude ref (dB): {mag_ref}")
        print(f"Magnitude simple (dB): {mag_simple}")
        print(f"Différences (dB): {mag_simple - mag_ref}")
    
    print(f"\n=== Test terminé ===")
    
    return True

def test_sympy_operations():
    """Test des opérations SymPy avancées"""
    print(f"\n=== Test des opérations SymPy avancées ===")
    
    # Test de manipulation d'expressions complexes
    expr_str = "gm_Q1*R2/(R1*C1*s + 1) + 1/(R3*C2*s + R4*C3*s + 1)"
    expr = sp.sympify(expr_str)
    
    print(f"Expression: {expr}")
    
    # Séparer numérateur et dénominateur
    num = sp.numer(expr)
    den = sp.denom(expr)
    
    print(f"Numérateur: {num}")
    print(f"Dénominateur: {den}")
    
    # Analyser les termes du numérateur
    if num.is_Add:
        num_terms = num.args
        print(f"Termes du numérateur: {len(num_terms)}")
        for i, term in enumerate(num_terms):
            print(f"  Terme {i+1}: {term}")
            print(f"    Variables: {term.free_symbols}")
    
    # Analyser les termes du dénominateur
    if den.is_Add:
        den_terms = den.args
        print(f"Termes du dénominateur: {len(den_terms)}")
        for i, term in enumerate(den_terms):
            print(f"  Terme {i+1}: {term}")
            print(f"    Variables: {term.free_symbols}")
    
    return True

if __name__ == "__main__":
    try:
        success1 = test_intelligent_add()
        success2 = test_sympy_operations()
        
        if success1 and success2:
            print(f"\n✓ Tous les tests sont passés avec succès!")
            print(f"\nLa nouvelle fonctionnalité d'ajout intelligent devrait:")
            print(f"- Évaluer la précision avant d'ajouter un terme")
            print(f"- Valider la cohérence mathématique")
            print(f"- Sélectionner le terme le plus bénéfique")
            print(f"- Éviter les dégradations de précision")
        else:
            print(f"\n✗ Certains tests ont échoué")
            
    except Exception as e:
        print(f"Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
