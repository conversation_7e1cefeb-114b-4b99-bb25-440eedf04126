#!/usr/bin/env python3
"""
Test de la nouvelle approche d'ajout équilibré de termes
"""

import sys
import os
import numpy as np
import sympy as sp

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bode_H1_final import *

def test_balanced_expression_creation():
    """Test de création d'expressions équilibrées"""
    
    print("=== Test de création d'expressions équilibrées ===\n")
    
    # Expression simplifiée de base
    simplified_expr = sp.sympify("1/(R1*C1*s + 1)")
    print(f"Expression simplifiée: {simplified_expr}")
    
    # Terme à ajouter
    term_to_add = sp.sympify("R2*C2*s")
    print(f"Terme à ajouter: {term_to_add}")
    
    # Test d'ajout avec facteur d'atténuation
    scaling_factors = [0.01, 0.1, 0.5, 1.0]
    
    for factor in scaling_factors:
        print(f"\nFacteur d'atténuation: {factor}")
        
        # Ajout au dénominateur avec atténuation
        num, den = simplified_expr.as_numer_denom()
        balanced_expr = num / (den + factor * term_to_add)
        simplified_balanced = sp.simplify(balanced_expr)
        
        print(f"  Expression équilibrée: {simplified_balanced}")
        
        # Test d'évaluation numérique
        test_values = {
            'R1': 1000, 'C1': 1e-6, 'R2': 2000, 'C2': 2e-6,
            's': 1j * 2 * np.pi * 1000  # 1 kHz
        }
        
        try:
            result = complex(simplified_balanced.subs(test_values))
            magnitude = abs(result)
            phase = np.angle(result, deg=True)
            
            print(f"  À 1kHz: |H| = {magnitude:.3e}, ∠H = {phase:.1f}°")
            
            # Vérifier la cohérence
            is_coherent = np.isfinite(magnitude) and 1e-10 < magnitude < 1e10
            print(f"  Cohérent: {'✓' if is_coherent else '✗'}")
            
        except Exception as e:
            print(f"  Erreur évaluation: {e}")

def test_scaling_factor_impact():
    """Test de l'impact du facteur d'atténuation sur la précision"""
    
    print(f"\n=== Test de l'impact du facteur d'atténuation ===\n")
    
    # Expression originale complexe
    original_str = "gm_Q1*R2/(R1*C1*s + 1) + 1/(R3*C2*s + R4*C3*s + 1)"
    original_expr = sp.sympify(original_str)
    
    # Expression simplifiée
    simplified_str = "gm_Q1*R2/(R1*C1*s + 1)"
    simplified_expr = sp.sympify(simplified_str)
    
    # Terme à ajouter (du dénominateur de la deuxième fraction)
    term_str = "R4*C3*s"
    term = sp.sympify(term_str)
    
    print(f"Expression originale: {original_str}")
    print(f"Expression simplifiée: {simplified_str}")
    print(f"Terme à ajouter: {term_str}")
    
    # Valeurs des composants
    component_values = {
        'gm_Q1': 0.1, 'R1': 1000, 'C1': 1e-6, 'R2': 2000,
        'R3': 1500, 'C2': 2e-6, 'R4': 500, 'C3': 5e-7
    }
    
    # Fréquences de test
    test_frequencies = np.array([10, 100, 1000, 10000, 100000])
    
    # Calculer la réponse de référence
    mag_ref, _ = calculate_bode_at_frequencies(original_str, component_values, test_frequencies)
    mag_simplified, _ = calculate_bode_at_frequencies(simplified_str, component_values, test_frequencies)
    
    if mag_ref is not None and mag_simplified is not None:
        error_simplified = np.max(np.abs(mag_simplified - mag_ref))
        print(f"\nErreur expression simplifiée: {error_simplified:.3f} dB")
        
        # Tester différents facteurs d'atténuation
        factors = [0.01, 0.05, 0.1, 0.2, 0.5, 1.0]
        
        print(f"\nTest des facteurs d'atténuation:")
        print(f"{'Facteur':<8} {'Erreur (dB)':<12} {'Amélioration':<12} {'Status'}")
        print("-" * 45)
        
        for factor in factors:
            # Créer l'expression avec atténuation
            num, den = simplified_expr.as_numer_denom()
            balanced_expr = num / (den + factor * term)
            balanced_str = str(sp.simplify(balanced_expr))
            
            # Évaluer
            mag_balanced, _ = calculate_bode_at_frequencies(balanced_str, component_values, test_frequencies)
            
            if mag_balanced is not None:
                error_balanced = np.max(np.abs(mag_balanced - mag_ref))
                improvement = error_simplified - error_balanced
                
                status = "✓" if improvement > 0 else "✗"
                print(f"{factor:<8.2f} {error_balanced:<12.3f} {improvement:<12.3f} {status}")
            else:
                print(f"{factor:<8.2f} {'ERREUR':<12} {'N/A':<12} ✗")

def test_variable_term_extraction():
    """Test d'extraction de termes contenant une variable"""
    
    print(f"\n=== Test d'extraction de termes par variable ===\n")
    
    # Expression complexe
    expr_str = "gm_Q1*R2/(R1*C1*s + 1) + R3/(R4*C2*s + R5*C3*s + 1)"
    expr = sp.sympify(expr_str)
    
    print(f"Expression: {expr}")
    
    # Variables à tester
    variables = ['R1', 'R2', 'R3', 'C1', 'C2', 'gm_Q1']
    
    for variable in variables:
        var_symbol = sp.symbols(variable)
        terms_with_var = []
        
        # Méthode simple d'extraction
        if expr.is_Add:
            for term in expr.args:
                if var_symbol in term.free_symbols:
                    terms_with_var.append(term)
        elif var_symbol in expr.free_symbols:
            terms_with_var.append(expr)
        
        print(f"\nVariable {variable}:")
        if terms_with_var:
            for i, term in enumerate(terms_with_var):
                print(f"  Terme {i+1}: {term}")
        else:
            print(f"  Aucun terme trouvé")

if __name__ == "__main__":
    try:
        test_balanced_expression_creation()
        test_scaling_factor_impact()
        test_variable_term_extraction()
        
        print(f"\n=== Résumé de la nouvelle approche ===")
        print("✓ Facteur d'atténuation: évite la dominance des termes ajoutés")
        print("✓ Reconstruction équilibrée: préserve la structure de l'expression")
        print("✓ Test de cohérence: validation avant acceptation")
        print("✓ Optimisation progressive: test de plusieurs facteurs")
        print("\nCette approche devrait éviter les dégradations massives de précision.")
        
    except Exception as e:
        print(f"Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()
