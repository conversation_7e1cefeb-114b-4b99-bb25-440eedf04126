# Modifications pour l'affichage des substitutions numériques

## Objectif
Afficher lors des print des expressions de fonction de transfert, quelle valeur numérique le code utilise pour quel paramètre symbolique qu'il substitue, pour tous les paramètres substitués.

## Modifications apportées

### 1. Fonction `force_fc_substitution` (ligne 1284)
**Avant :**
```python
def force_fc_substitution(expression, values):
    result = expression
    for var, val in values.items():
        if var.startswith('Fc') and '_' in var:
            try:
                if var in str(result): result = xcas(f"subst({result},{var},{val})")
            except: continue
    return result
```

**Après :**
```python
def force_fc_substitution(expression, values, return_substitutions=False):
    result = expression
    substitutions_applied = []
    for var, val in values.items():
        if var.startswith('Fc') and '_' in var:
            try:
                if var in str(result): 
                    result = xcas(f"subst({result},{var},{val})")
                    substitutions_applied.append(f"{var} = {val}")
            except: continue
    if return_substitutions:
        return result, substitutions_applied
    return result
```

### 2. Fonction `apply_consistent_substitution` dans `plot_bode` (ligne 570)
**Modifications principales :**
- Ajout du tracking des substitutions appliquées
- Utilisation de la nouvelle version de `force_fc_substitution`
- Collecte de toutes les substitutions dans une liste
- Retour de la liste des substitutions avec le résultat

**Nouvelles fonctionnalités :**
- Affichage des substitutions Fc
- Affichage des substitutions de variables prioritaires (ro_, beta_, Ic_ac_, Av_, Fc1-4_)
- Affichage des substitutions de composants
- Affichage des substitutions finales (k, pi)

### 3. Affichage dans `plot_bode` (ligne 648)
**Ajout :**
```python
# Afficher les substitutions appliquées
if substitutions_list:
    file_print("Substitutions numeriques appliquees:")
    for substitution in substitutions_list:
        file_print(f"  {substitution}")
else:
    file_print("Aucune substitution numerique appliquee")
```

### 4. Affichage des fonctions de transfert (ligne 1223)
**Avant :**
```python
transfer_function_with_fc_values = force_fc_substitution(transfer_function, values)
file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function_with_fc_values}")
```

**Après :**
```python
transfer_function_with_fc_values, fc_substitutions = force_fc_substitution(transfer_function, values, return_substitutions=True)
file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function_with_fc_values}")
if fc_substitutions:
    file_print("Substitutions Fc appliquees:")
    for substitution in fc_substitutions:
        file_print(f"  {substitution}")
```

### 5. Analyse AC symbolique (ligne 1144)
**Ajout du tracking des substitutions :**
- Collecte de toutes les substitutions dans `all_substitutions`
- Affichage groupé à la fin de la section AC Analysis (Symbolic)

## Résultat
Maintenant, lors de l'affichage des fonctions de transfert, le programme affiche :

1. **La fonction de transfert finale** avec toutes les substitutions appliquées
2. **La liste détaillée des substitutions** sous la forme :
   ```
   Substitutions numeriques appliquees:
     Fc1_X1 = 100000000
     Fc2_X1 = 1000000000
     beta_Q1 = 100
     ro_Q1 = 1000
     R1 = 1000
     k = 1000
     pi = 3.14159265359
   ```

## Compatibilité
- Les modifications sont rétrocompatibles
- Le paramètre `return_substitutions=False` par défaut dans `force_fc_substitution` maintient le comportement existant
- Toutes les fonctionnalités existantes sont préservées

## Test
Un script de test `test_substitutions.py` a été créé pour vérifier le bon fonctionnement des modifications.
