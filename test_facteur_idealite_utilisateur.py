#!/usr/bin/env python3
"""
Test pour vérifier la syntaxe étendue avec facteur d'idéalité utilisateur
"""

def test_syntaxe_etendue():
    print("=== Test de la syntaxe étendue pour les diodes ===")
    print()
    
    print("Nouvelle syntaxe:")
    print("Dnom anode cathode tension_seuil [facteur_idealite]")
    print()
    
    print("Exemples:")
    examples = [
        ("D1 1 2 0.7", "Diode avec n = 10 (défaut)"),
        ("D2 2 3 0.7 1", "Diode idéale (n = 1)"),
        ("D3 3 4 0.7 2", "Diode silicium (n = 2)"),
        ("D4 4 0 2.2 5", "Diode Zener (n = 5)"),
        ("D5 5 0 1.4 15", "LED rouge (n = 15)")
    ]
    
    for syntax, description in examples:
        print(f"  {syntax:<20} # {description}")
    
    print()

def test_calcul_rd_avec_n_utilisateur():
    print("=== Test du calcul rd avec différents facteurs n ===")
    print()
    
    # Paramètres fixes
    VT = 0.026  # V
    Id_dc = 0.019  # A (exemple)
    V1_ac = 3.0  # V
    R1 = R2 = 100.0  # Ω
    
    print(f"Paramètres du circuit:")
    print(f"  VT = {VT} V")
    print(f"  Id_dc = {Id_dc} A")
    print(f"  V1_ac = {V1_ac} V")
    print(f"  R1 = R2 = {R1} Ω")
    print()
    
    # Test avec différentes valeurs de n
    n_values = [1, 2, 5, 10, 15, 20]
    
    print("n (utilisateur) | rd (Ω)   | V3_ac (V) | Atténuation | Type")
    print("----------------|----------|-----------|-------------|------")
    
    for n in n_values:
        # Calcul de rd
        rd = (n * VT) / Id_dc
        
        # Calcul AC
        V3_ac = V1_ac * R2 / (R1 + rd + R2)
        attenuation = V3_ac / V1_ac
        
        # Type de diode
        if n == 1:
            diode_type = "Idéale"
        elif n <= 2:
            diode_type = "Schottky/Si"
        elif n <= 5:
            diode_type = "Réelle"
        elif n <= 10:
            diode_type = "Défaut"
        else:
            diode_type = "LED/Zener"
        
        print(f"{n:14d} | {rd:7.3f} | {V3_ac:8.6f} | {attenuation:10.3f} | {diode_type}")
    
    print()

def test_exemples_circuits():
    print("=== Exemples de circuits avec facteurs personnalisés ===")
    print()
    
    circuits = [
        {
            "nom": "Redresseur avec diode idéale",
            "netlist": [
                "V1 1 0 DC 0 AC 5",
                "D1 1 2 0.7 1",  # n = 1 (idéale)
                "R1 2 0 1k"
            ],
            "description": "Diode idéale pour redressement optimal"
        },
        {
            "nom": "Écrêteur avec diodes Zener",
            "netlist": [
                "V1 1 0 DC 0 AC 10",
                "R1 1 2 1k",
                "D1 2 0 3.3 5",  # n = 5 (Zener)
                "D2 0 2 3.3 5"   # n = 5 (Zener)
            ],
            "description": "Diodes Zener avec facteur d'idéalité 5"
        },
        {
            "nom": "Indicateur LED",
            "netlist": [
                "V1 1 0 DC 5",
                "R1 1 2 330",
                "D1 2 0 1.8 15"  # n = 15 (LED)
            ],
            "description": "LED avec facteur d'idéalité élevé"
        },
        {
            "nom": "Circuit mixte",
            "netlist": [
                "V1 1 0 DC 12 AC 2",
                "R1 1 2 1k",
                "D1 2 3 0.7 2",    # Diode silicium
                "R2 3 4 2k",
                "D2 4 0 5.1 8"     # Diode Zener
            ],
            "description": "Mélange de diodes avec différents facteurs"
        }
    ]
    
    for circuit in circuits:
        print(f"**{circuit['nom']}**")
        print(f"Description: {circuit['description']}")
        print("Netlist:")
        for line in circuit['netlist']:
            print(f"  {line}")
        print()

def test_impact_facteur_n():
    print("=== Impact du facteur d'idéalité sur l'atténuation ===")
    print()
    
    # Circuit: V1(3V AC) → R1(100Ω) → D1(2.2V, n) → R2(100Ω) → GND
    print("Circuit: V1(3V AC) → R1(100Ω) → D1(2.2V, n) → R2(100Ω) → GND")
    print()
    
    VT = 0.026
    Id_dc = 0.019  # Estimation
    V1_ac = 3.0
    R1 = R2 = 100.0
    
    print("Facteur n | rd (Ω)  | V3_ac (V) | Réduction vs n=1")
    print("----------|---------|-----------|------------------")
    
    rd_n1 = None
    V3_ac_n1 = None
    
    for n in [1, 2, 5, 10, 15, 20]:
        rd = (n * VT) / Id_dc
        V3_ac = V1_ac * R2 / (R1 + rd + R2)
        
        if n == 1:
            rd_n1 = rd
            V3_ac_n1 = V3_ac
            reduction = 0.0
        else:
            reduction = ((V3_ac_n1 - V3_ac) / V3_ac_n1) * 100
        
        print(f"{n:8d} | {rd:6.3f} | {V3_ac:8.6f} | {reduction:15.1f}%")
    
    print()
    print("Observation: Plus n est élevé, plus l'atténuation est importante")
    print("Facteur par défaut n = 10 donne un bon compromis réaliste")

if __name__ == "__main__":
    test_syntaxe_etendue()
    test_calcul_rd_avec_n_utilisateur()
    test_exemples_circuits()
    test_impact_facteur_n()
