(C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R2*R3*R5*s^2+C2*R2*R3*R4*R5*s+C3*L1*R3*R4*R5*s^2+L1*R3*R5*s+R3*R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)
