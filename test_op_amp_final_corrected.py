#!/usr/bin/env python3

# Test final corrigé de l'amplificateur opérationnel avec réponse fréquentielle

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

print("=== Test final corrigé de l'amplificateur opérationnel ===")
print("Configuration: Amplificateur inverseur")
print("Gain théorique = -R2/R1 = -2k/1k = -2")
print("fc1 = 500MHz (par défaut), fc2 = 10GHz (par défaut)")
print()

# Netlist simple avec amplificateur inverseur
netlist_final = """V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 2 0 3
R2 2 3 2k"""

print("Netlist:")
print(netlist_final)
print()

try:
    solve_circuit(netlist_final, 
                 frequency_hz=1000.0,     # Test à 1kHz (bien en dessous de fc1)
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='3', 
                 freq_min=1000.0,          # 1kHz à 100GHz pour voir toute la réponse
                 freq_max=100000000000.0, 
                 laplace_domain=True)
    
    print("✓ Test terminé avec succès!")
    print()
    print("Vérifications attendues dans le diagramme de Bode :")
    print("1. Gain constant ≈ -2 (≈ 6dB) jusqu'à 500MHz")
    print("2. Décroissance -20dB/décade entre 500MHz et 10GHz")
    print("3. Décroissance -40dB/décade au-delà de 10GHz")
    print("4. Pas de pic de résonance anormal")
    print()
    print("Dans le fichier de résultats, vous devriez voir :")
    print("- fc1_OP1 = 500000000 (500MHz)")
    print("- fc2_OP1 = 10000000000 (10GHz)")
    print("- gain_OP1 = 1000000 (1e6)")
    print("- La fonction de transfert contient ces paramètres")
    
except Exception as e:
    print(f"✗ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
