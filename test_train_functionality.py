#!/usr/bin/env python3
"""
Script de test pour la fonctionnalité Train des boutons de variables
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from bode_H1_final import BodeAnalyzer, extract_variables_from_expression, read_transfer_function, get_user_input

def create_test_transfer_function():
    """Crée une fonction de transfert de test simple"""
    test_function = """
(R1*C1*s + 1) / ((R2*C2*s + 1) * (R3*C3*s + 1))
"""
    
    # Sauvegarder dans un fichier temporaire
    with open("FTC_test.txt", "w") as f:
        f.write(test_function.strip())
    
    return test_function.strip()

def main():
    """Fonction principale de test"""
    app = QApplication(sys.argv)
    
    try:
        # Créer une fonction de transfert de test
        transfer_function = create_test_transfer_function()
        print("Fonction de transfert de test créée:")
        print(transfer_function)
        
        # Extraire les variables
        variables = extract_variables_from_expression(transfer_function)
        print(f"Variables détectées: {variables}")
        
        # Valeurs de test
        values = {
            'R1': 1000.0,
            'R2': 10000.0, 
            'R3': 5000.0,
            'C1': 1e-6,
            'C2': 1e-7,
            'C3': 2e-7
        }
        
        # Plages de test
        slider_ranges = {
            'R1': (100.0, 100000.0),
            'R2': (1000.0, 100000.0),
            'R3': (500.0, 50000.0),
            'C1': (1e-9, 1e-4),
            'C2': (1e-9, 1e-4),
            'C3': (1e-9, 1e-4)
        }
        
        # Paramètres de fréquence
        freq_params = {
            'f_min': 1.0,
            'f_max': 1e6,
            'num_points': 500
        }
        
        print(f"Valeurs des composants: {values}")
        print(f"Plages des sliders: {slider_ranges}")
        
        # Créer l'analyseur de Bode
        analyzer = BodeAnalyzer(transfer_function, variables, values, freq_params, slider_ranges)
        analyzer.show()
        
        # Message d'information
        QMessageBox.information(None, "Test Train Functionality - Merge Intelligent",
                              "Analyseur de Bode lancé avec la fonctionnalité Train (Merge Intelligent).\n\n"
                              "NOUVEAU: Merge intelligent d'expressions!\n\n"
                              "Pour tester les boutons Train:\n"
                              "1. Cliquez sur 'Analyse de sensibilité'\n"
                              "2. Simplifiez la fonction de transfert\n"
                              "3. Affichez le Bode simplifié\n"
                              "4. Bougez un slider pour trouver une zone de divergence\n"
                              "5. Cliquez 'Train' pour cette variable à cette position\n"
                              "6. Le système va merger intelligemment les expressions\n\n"
                              "PRINCIPE: Combine l'expression globale (bonne partout)\n"
                              "avec une optimisation locale (bonne à la position actuelle)\n"
                              "pour créer une expression hybride performante!")
        
        # Lancer l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        QMessageBox.critical(None, "Erreur", f"Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        # Nettoyer le fichier de test
        if os.path.exists("FTC_test.txt"):
            os.remove("FTC_test.txt")

if __name__ == "__main__":
    main()
