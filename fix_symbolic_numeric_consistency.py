#!/usr/bin/env python3
"""
Script pour corriger automatiquement la cohérence entre les fonctions de transfert
symboliques et numériques dans test.py
"""
import re
import shutil

def fix_plot_bode_function():
    """Corrige la fonction plot_bode pour assurer la cohérence symbolique/numérique"""
    
    # Lire le fichier test.py
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Nouvelle fonction plot_bode corrigée
    new_plot_bode = '''def plot_bode(transfer_function, values, freq_range, **_):
    tf_symbolic = transfer_function
    tf_numeric = transfer_function
    
    # Appliquer force_fc_substitution comme pour la fonction symbolique
    tf_symbolic_with_fc = force_fc_substitution(tf_symbolic, values)
    tf_numeric_with_fc = force_fc_substitution(tf_numeric, values)
    
    # Afficher la fonction symbolique avec les substitutions Fc
    file_print(f"Fonction de transfert symbolique: {tf_symbolic_with_fc}")
    
    # Variables prioritaires incluant les paramètres des amplificateurs opérationnels
    priority_vars = [k for k in values.keys() if k.startswith('ro_') or k.startswith('beta_') or k.startswith('Ic_ac_') or k.startswith('Av_') or k.startswith('Fc1_') or k.startswith('Fc2_') or k.startswith('Fc3_') or k.startswith('Fc4_')]
    
    # Substitution prioritaire identique pour symbolique et numérique
    for var in priority_vars:
        comp_value = values.get(var, '1000')
        try:
            comp_value_num = xcas(f"evalf({comp_value})")
            float(comp_value_num)
            tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},{var},{comp_value_num})")
        except:
            if var.startswith('ro_'): default_val = '1000'
            elif var.startswith('beta_'): default_val = '100'
            elif var.startswith('Ic_ac_'): default_val = '0.001'
            elif var.startswith('Av_'): default_val = '100000'
            elif var.startswith('Fc1_'): default_val = '100000000'  # 100MHz
            elif var.startswith('Fc2_'): default_val = '1000000000'  # 1GHz
            elif var.startswith('Fc3_'): default_val = '10000000000'  # 10GHz
            elif var.startswith('Fc4_'): default_val = '100000000000'  # 100GHz
            else: default_val = '1000'
            tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},{var},{default_val})")
    
    # Substitution des autres composants avec la même logique que substitute_values
    # Identifier les résistances de compensation BJT
    bjt_compensation_resistors = set()
    for var, val in values.items():
        if var.startswith('comp_BJT_res_') and val == 'true':
            resistor_name = var.replace('comp_BJT_res_', '')
            bjt_compensation_resistors.add(resistor_name)
    
    # Substitution manuelle identique à verify_symbolic_numeric_consistency
    for var, val in values.items():
        if var not in priority_vars and var != 'k' and not var.startswith('comp_BJT_'):
            # Forcer la substitution numérique pour les résistances de compensation BJT
            if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                try:
                    comp_value_num = xcas(f"evalf({val})")
                    float(comp_value_num)
                    if var in str(tf_numeric_with_fc):
                        tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},{var},{comp_value_num})")
                except: 
                    continue
    
    # Substitutions finales
    tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},k,1000)")
    tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},pi,3.14159265359)")
    tf_numeric_final = xcas(f"evalf(simplify({tf_numeric_with_fc}))")
    
    file_print(f"Fonction de transfert numérique: {tf_numeric_final}")
    
    # Vérification de cohérence
    try:
        consistency_check = verify_symbolic_numeric_consistency(tf_symbolic_with_fc, tf_numeric_final, values)
        if consistency_check[0]:
            file_print("✓ Cohérence symbolique/numérique vérifiée")
        else:
            file_print(f"⚠ Différence détectée: {consistency_check[3]}")
            file_print(f"  Symbolique évalué: {consistency_check[1]}")
            file_print(f"  Numérique: {consistency_check[2]}")
    except Exception as e:
        file_print(f"Erreur lors de la vérification de cohérence: {e}")
    
    try:
        num_coeffs, den_coeffs = parse_transfer_function(tf_numeric_final)
        if all(c == 0 for c in num_coeffs) or all(c == 0 for c in den_coeffs):
            file_print("Erreur: Coefficients nuls")
            return
        system = signal.TransferFunction(num_coeffs, den_coeffs)
        freqs = np.logspace(np.log10(freq_range[0]), np.log10(freq_range[1]), 1000)
        w = 2 * np.pi * freqs
        w, h = signal.freqresp(system, w)
        freqs = w / (2 * np.pi)
        magnitude_db = 20 * np.log10(np.abs(h))
        phase_deg = np.angle(h) * 180 / np.pi
        _, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        ax1.semilogx(freqs, magnitude_db)
        ax1.set_ylabel('Magnitude (dB)')
        ax1.set_xlabel('Fréquence (Hz)')
        ax1.grid(True, which="both", ls="-", alpha=0.3)
        ax1.set_title('Diagramme de Bode')
        ax2.semilogx(freqs, phase_deg)
        ax2.set_xlabel('Fréquence (Hz)')
        ax2.set_ylabel('Phase (degrés)')
        ax2.grid(True, which="both", ls="-", alpha=0.3)
        plt.tight_layout()
        plt.show()
    except Exception as e:
        file_print(f"Erreur lors du tracé du diagramme de Bode: {e}")
        import traceback
        traceback.print_exc()'''
    
    # Trouver et remplacer la fonction plot_bode existante
    
    # Pattern pour trouver la fonction plot_bode complète
    pattern = r'def plot_bode\(transfer_function, values, freq_range, \*\*_\):.*?(?=\ndef |\nif __name__|$)'
    
    match = re.search(pattern, content, re.DOTALL)
    if match:
        # Remplacer la fonction
        new_content = content[:match.start()] + new_plot_bode + content[match.end():]
        
        # Sauvegarder le fichier modifié
        with open('test.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✓ Fonction plot_bode corrigée avec succès")
        return True
    else:
        print("✗ Impossible de trouver la fonction plot_bode")
        return False

def add_symbolic_display_in_transfer_function():
    """Ajoute l'affichage de la fonction symbolique dans calculate_transfer_function"""
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher la ligne où la fonction de transfert est affichée
    pattern = r'(file_print\(f"H\{tf_number\}\(s\) = V\{out_node\}/V\{in_node\} = \{transfer_function_with_fc_values\}"\))'
    
    replacement = r'''\1
                                # Afficher aussi la fonction symbolique pure (sans substitution Fc)
                                file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function}")'''
    
    new_content = re.sub(pattern, replacement, content)
    
    if new_content != content:
        with open('test.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        print("✓ Affichage de la fonction symbolique ajouté")
        return True
    else:
        print("✗ Impossible de modifier l'affichage de la fonction symbolique")
        return False

def main():
    """Fonction principale pour appliquer toutes les corrections"""
    
    print("=== Correction de la cohérence symbolique/numérique ===")
    print()
    
    # Faire une sauvegarde
    shutil.copy('test.py', 'test.py.backup')
    print("✓ Sauvegarde créée: test.py.backup")
    
    # Appliquer les corrections
    success1 = fix_plot_bode_function()
    success2 = add_symbolic_display_in_transfer_function()
    
    if success1 and success2:
        print()
        print("✓ Toutes les corrections appliquées avec succès!")
        print()
        print("Changements effectués:")
        print("1. Fonction plot_bode corrigée pour utiliser la même logique de substitution")
        print("2. Ajout de vérification de cohérence automatique")
        print("3. Affichage des fonctions symbolique et numérique")
        print("4. Utilisation de force_fc_substitution pour les deux versions")
        print()
        print("Les fonctions de transfert symboliques et numériques devraient maintenant être cohérentes.")
    else:
        print()
        print("✗ Certaines corrections ont échoué")
        print("Restauration de la sauvegarde...")
        shutil.copy('test.py.backup', 'test.py')

if __name__ == "__main__":
    main()
