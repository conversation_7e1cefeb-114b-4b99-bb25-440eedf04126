#!/usr/bin/env python3
"""
Démonstration de la fonctionnalité de sélection de courbe pour le curseur
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from bode_H1_final import *
    print("✓ Import du module bode_H1_final réussi")
except ImportError as e:
    print(f"✗ Erreur d'import: {e}")
    sys.exit(1)

def create_demo():
    """Crée une démonstration avec les trois types de courbes"""
    
    # Créer une application Qt
    app = QApplication(sys.argv)
    
    # Définir l'icône de l'application
    set_application_icon(app)
    
    # Créer une expression de test simple (filtre passe-bas RC)
    transfer_function = "1/(1+s*R1*C1)"
    variables = ['R1', 'C1']
    values = {'R1': 1000.0, 'C1': 1e-6}  # fc = 1/(2*pi*RC) ≈ 159 Hz
    frequency_params = {'f_min': 1.0, 'f_max': 1e6, 'num_points': 500}
    slider_ranges = {'R1': (100.0, 10000.0), 'C1': (1e-9, 1e-3)}
    
    # Créer l'analyseur de Bode
    analyzer = BodeAnalyzer(transfer_function, variables, values, frequency_params, slider_ranges)
    analyzer.setWindowTitle("Démonstration - Sélection de courbe pour le curseur")
    analyzer.resize(1200, 800)
    
    print("\n=== Instructions ===")
    print("1. L'expression originale (bleue) est affichée par défaut")
    print("2. Utilisez 'Analyse de sensibilité' puis 'Simplifier la fonction de transfert' pour ajouter une courbe simplifiée (rouge, tirets)")
    print("3. Utilisez 'Ajustement manuel' pour ajouter une courbe manuelle (verte, pointillés)")
    print("4. Utilisez les boutons radio en haut pour choisir quelle courbe suit le curseur")
    print("5. Bougez la souris sur les graphiques pour voir le curseur suivre la courbe sélectionnée")
    print("6. Toutes les expressions apparaissent dans la légende")
    print("\nFermez la fenêtre pour terminer la démonstration.")
    
    # Afficher la fenêtre
    analyzer.show()
    
    # Ajouter automatiquement des courbes pour la démonstration
    def add_demo_curves():
        """Ajoute automatiquement des courbes pour la démonstration"""
        try:
            # Expression simplifiée (avec des valeurs légèrement différentes)
            simplified_expr = "1/(1+s*1000*1e-6)"  # Même que l'original mais simplifié
            analyzer.add_simplified_curves(simplified_expr, values)
            print("✓ Courbe simplifiée ajoutée automatiquement")
            
            # Expression manuelle (avec un gain réduit et une fréquence de coupure différente)
            manual_expr = "0.8/(1+s*1500*1e-6)"  # Gain 0.8 et fc ≈ 106 Hz
            analyzer.add_manual_curves(manual_expr, values)
            print("✓ Courbe manuelle ajoutée automatiquement")
            
            print("\n✓ Toutes les courbes sont maintenant disponibles!")
            print("✓ Vous pouvez maintenant tester les boutons radio pour changer le suivi du curseur")
            
        except Exception as e:
            print(f"Erreur lors de l'ajout des courbes de démonstration: {e}")
    
    # Ajouter les courbes après un court délai pour que l'interface soit prête
    from PyQt5.QtCore import QTimer
    QTimer.singleShot(1000, add_demo_curves)  # Délai de 1 seconde
    
    # Lancer l'application
    sys.exit(app.exec_())

if __name__ == "__main__":
    print("=== Démonstration de la sélection de courbe pour le curseur ===")
    create_demo()
