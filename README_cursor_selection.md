# Fonctionnalité de Sélection de Courbe pour le Curseur

## Nouvelle Fonctionnalité Ajoutée

Cette mise à jour ajoute la possibilité de choisir quelle courbe le curseur suit lors du survol des diagrammes de Bode.

### Fonctionnalités

#### 1. Boutons de Sélection de Courbe
Trois boutons radio sont maintenant disponibles en haut des diagrammes de Bode :

- **Originale** : Suit l'expression de transfert originale (courbe bleue)
- **Simplifiée** : Suit l'expression simplifiée (courbe rouge en tirets)
- **Manuelle** : Suit l'expression d'ajustement manuel (courbe verte en pointillés)

#### 2. Comportement des Boutons
- **État initial** : Le bouton "Originale" est sélectionné par défaut
- **Activation automatique** : Les boutons "Simplifiée" et "Manuelle" s'activent automatiquement quand les courbes correspondantes sont ajoutées
- **Désactivation automatique** : Les boutons se désactivent et reviennent au mode "Originale" quand les courbes sont supprimées avec le bouton "Clear"

#### 3. Légendes Améliorées
- Toutes les expressions (originale, simplifiée, manuelle) apparaissent maintenant dans la légende
- La légende est créée dès l'affichage de l'expression originale
- Chaque type d'expression a sa propre couleur et style distinctifs

### Utilisation

1. **Démarrage** : L'application démarre avec le curseur suivant l'expression originale
2. **Ajout de courbes** : 
   - Utilisez "Analyse de sensibilité" → "Simplifier la fonction de transfert" pour ajouter une courbe simplifiée
   - Utilisez "Ajustement manuel" pour ajouter une courbe manuelle
3. **Sélection de courbe** : Cliquez sur les boutons radio pour choisir quelle courbe le curseur doit suivre
4. **Suivi du curseur** : Bougez la souris sur les diagrammes pour voir les valeurs de la courbe sélectionnée

### Styles des Courbes

- **Expression originale** : Ligne bleue continue (largeur 2)
- **Expression simplifiée** : Ligne rouge en tirets (largeur 3)
- **Expression manuelle** : Ligne verte en pointillés (largeur 3)

### Fichiers de Test et Démonstration

- `test_cursor_selection.py` : Tests automatisés de la fonctionnalité
- `test_legends.py` : Tests spécifiques pour les légendes
- `demo_cursor_selection.py` : Démonstration interactive avec courbes pré-ajoutées

### Modifications Techniques

#### Nouvelles Variables
- `cursor_tracking_mode` : Mode de suivi actuel ("original", "simplified", "manual")
- `cursor_button_group` : Groupe de boutons radio
- `cursor_original_btn`, `cursor_simplified_btn`, `cursor_manual_btn` : Boutons radio individuels

#### Nouvelles Méthodes
- `set_cursor_tracking_mode(mode)` : Change le mode de suivi du curseur
- Modification de `get_interpolated_values()` : Utilise l'expression sélectionnée selon le mode

#### Modifications des Méthodes Existantes
- `add_simplified_curves()` : Active le bouton "Simplifiée"
- `add_manual_curves()` : Active le bouton "Manuelle"
- `clear_all_additional_curves()` : Désactive les boutons et revient au mode "Originale"

### Compatibilité

Cette mise à jour est entièrement rétrocompatible. Les utilisateurs existants verront simplement les nouveaux boutons radio et pourront continuer à utiliser l'application comme avant, avec le curseur suivant l'expression originale par défaut.

### Avantages

1. **Flexibilité** : Possibilité de comparer précisément les valeurs entre différentes expressions
2. **Clarté** : Légendes complètes pour identifier facilement chaque courbe
3. **Facilité d'utilisation** : Interface intuitive avec activation/désactivation automatique des boutons
4. **Cohérence visuelle** : Styles de courbes distinctifs et cohérents
