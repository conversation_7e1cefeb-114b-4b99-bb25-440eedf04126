#!/usr/bin/env python3
"""
Script pour corriger tous les problèmes d'encodage dans test.py
"""
import re

def fix_all_unicode_chars():
    """Corrige tous les caractères Unicode problématiques dans test.py"""
    
    print("=== Correction de tous les caractères Unicode ===")
    
    # Lire le fichier
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Dictionnaire de remplacement
    replacements = {
        '✓': 'OK',
        '⚠': 'ATTENTION',
        '❌': 'ERREUR',
        '🎉': 'SUCCES',
        'é': 'e',
        'è': 'e',
        'à': 'a',
        'ç': 'c',
        'ù': 'u',
        'ô': 'o',
        'î': 'i',
        'ê': 'e',
        'â': 'a',
        'û': 'u',
        'ï': 'i',
        'ë': 'e',
        'ü': 'u',
        'ö': 'o',
        'ä': 'a',
        'Ω': 'Ohm',
        '°': 'deg',
        'µ': 'u',
        '²': '2',
        '³': '3'
    }
    
    # Appliquer les remplacements
    original_content = content
    for unicode_char, replacement in replacements.items():
        content = content.replace(unicode_char, replacement)
    
    # Compter les changements
    changes_made = sum(1 for old, new in zip(original_content, content) if old != new)
    
    if changes_made > 0:
        # Sauvegarder le fichier corrigé
        with open('test.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ {changes_made} caractères Unicode remplacés")
        
        # Afficher quelques exemples de remplacements
        print("\nExemples de remplacements effectués:")
        for unicode_char, replacement in replacements.items():
            if unicode_char in original_content:
                count = original_content.count(unicode_char)
                print(f"  '{unicode_char}' -> '{replacement}' ({count} occurrences)")
        
        return True
    else:
        print("✓ Aucun caractère Unicode problématique trouvé")
        return True

def verify_fixes():
    """Vérifie que toutes les corrections ont été appliquées"""
    
    print("\n=== Vérification des corrections ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Caractères à vérifier
    problematic_chars = ['✓', '⚠', '❌', '🎉', 'é', 'è', 'à', 'ç', 'ù', 'ô', 'î', 'ê', 'â', 'û', 'ï', 'ë', 'ü', 'ö', 'ä', 'Ω', '°', 'µ']
    
    found_chars = []
    for char in problematic_chars:
        if char in content:
            found_chars.append(char)
    
    if found_chars:
        print(f"⚠ Caractères Unicode restants: {found_chars}")
        return False
    else:
        print("✓ Tous les caractères Unicode problématiques ont été corrigés")
        return True

def main():
    """Fonction principale"""
    
    print("Correction automatique de l'encodage dans test.py")
    print("=" * 55)
    
    # Faire une sauvegarde
    import shutil
    shutil.copy('test.py', 'test.py.encoding_backup')
    print("✓ Sauvegarde créée: test.py.encoding_backup")
    
    # Appliquer les corrections
    success = fix_all_unicode_chars()
    
    if success:
        # Vérifier les corrections
        verification_ok = verify_fixes()
        
        if verification_ok:
            print("\n🎉 SUCCÈS: Tous les problèmes d'encodage ont été corrigés!")
            print("\nLe fichier test.py peut maintenant être utilisé sans problème d'encodage.")
        else:
            print("\n⚠ Certains caractères Unicode n'ont pas été corrigés")
    else:
        print("\n❌ Erreur lors de la correction")

if __name__ == "__main__":
    main()
