#!/usr/bin/env python3
"""
Script de diagnostic pour analyser les différences entre les fonctions 
de transfert symbolique et numérique
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re

def extract_and_compare():
    """Compare les expressions symbolique et numérique"""
    
    with open("vue.txt", 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire les expressions
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert numérique|$)', content, re.DOTALL)
    h1_numeric_match = re.search(r'Fonction de transfert numérique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    
    if not h1_symbolic_match or not h1_numeric_match:
        print("Erreur: Expressions non trouvées")
        return
    
    h1_symbolic = h1_symbolic_match.group(1).strip()
    h1_numeric = h1_numeric_match.group(1).strip()
    
    print("=== DIAGNOSTIC DES FONCTIONS DE TRANSFERT ===\n")
    
    # Analyser les structures
    print("1. LONGUEURS DES EXPRESSIONS:")
    print(f"   Symbolique: {len(h1_symbolic)} caractères")
    print(f"   Numérique:  {len(h1_numeric)} caractères")
    
    # Analyser les termes dominants
    print("\n2. ANALYSE DES TERMES:")
    
    # Extraire quelques termes pour comparaison
    print("   Premiers 200 caractères de l'expression symbolique:")
    print(f"   {h1_symbolic[:200]}...")
    
    print("\n   Premiers 200 caractères de l'expression numérique:")
    print(f"   {h1_numeric[:200]}...")
    
    # Compter les occurrences de 's'
    s_count_sym = h1_symbolic.count('s^')
    s_count_num = h1_numeric.count('s^')
    
    print(f"\n3. NOMBRE DE TERMES EN s^:")
    print(f"   Symbolique: {s_count_sym} termes")
    print(f"   Numérique:  {s_count_num} termes")
    
    # Analyser les ordres de grandeur
    print("\n4. ORDRES DE GRANDEUR:")

    # Chercher les coefficients numériques
    
    # Dans l'expression symbolique
    sym_numbers = re.findall(r'(\d+(?:\.\d+)?(?:e[+-]?\d+)?)', h1_symbolic)
    if sym_numbers:
        sym_values = [float(x) for x in sym_numbers[:10]]  # Premiers 10
        print(f"   Symbolique (premiers coefficients): {[f'{x:.2e}' for x in sym_values]}")
    
    # Dans l'expression numérique
    num_numbers = re.findall(r'(\d+(?:\.\d+)?(?:e[+-]?\d+)?)', h1_numeric)
    if num_numbers:
        num_values = [float(x) for x in num_numbers[:10]]  # Premiers 10
        print(f"   Numérique (premiers coefficients):  {[f'{x:.2e}' for x in num_values]}")
    
    return h1_symbolic, h1_numeric

def analyze_frequency_response():
    """Analyse la réponse fréquentielle pour identifier les différences"""
    
    print("\n=== ANALYSE DE LA RÉPONSE FRÉQUENTIELLE ===\n")
    
    # Créer des fonctions de transfert simples pour test
    # Fonction de test 1: passe-bas simple
    num1 = [1]
    den1 = [1e-6, 1]  # fc ≈ 159 kHz
    tf1 = signal.TransferFunction(num1, den1)
    
    # Fonction de test 2: même fonction avec coefficients légèrement différents
    num2 = [0.95]  # Gain légèrement différent
    den2 = [1.1e-6, 1]  # Fréquence légèrement différente
    tf2 = signal.TransferFunction(num2, den2)
    
    # Calculer les réponses
    frequencies = np.logspace(3, 8, 1000)  # 1 kHz à 100 MHz
    omega = 2 * np.pi * frequencies
    
    _, h1 = signal.freqresp(tf1, omega)
    _, h2 = signal.freqresp(tf2, omega)
    
    mag1_db = 20 * np.log10(np.abs(h1))
    mag2_db = 20 * np.log10(np.abs(h2))
    
    # Calculer les différences
    diff_db = mag1_db - mag2_db
    
    print(f"Exemple de différences typiques entre deux fonctions similaires:")
    print(f"Gain DC fonction 1: {mag1_db[0]:.1f} dB")
    print(f"Gain DC fonction 2: {mag2_db[0]:.1f} dB")
    print(f"Différence DC: {diff_db[0]:.1f} dB")
    print(f"Différence max: {np.max(np.abs(diff_db)):.1f} dB")
    print(f"Différence RMS: {np.sqrt(np.mean(diff_db**2)):.1f} dB")

def check_parameter_substitution():
    """Vérifie si la substitution des paramètres est correcte"""
    
    print("\n=== VÉRIFICATION DE LA SUBSTITUTION DES PARAMÈTRES ===\n")
    
    # Paramètres extraits
    params = {
        'R1': 100.0,
        'R2': 10000.0,
        'R3': 10000.0,
        'R4': 1000.0,
        'R5': 1000.0,
        'R6': 800.0,
        'C1': 1e-7,
        'C2': 1e-5,
        'C3': 1e-7,
        'Vbe_on_Q1': 0.7,
        'beta_Q1': 100.0,
        'Cbc_Q1': 2e-12,
        'ro_Q1': 100000.0,
        'rpi_Q1': 833.333333333,
        'Cbe_Q1': 1e-11,
        'gm_Q1': 0.12
    }
    
    print("Paramètres utilisés pour la substitution:")
    for param, value in params.items():
        if value < 1e-6:
            print(f"   {param} = {value:.2e}")
        elif value > 1e6:
            print(f"   {param} = {value:.2e}")
        else:
            print(f"   {param} = {value}")
    
    # Calculer quelques constantes de temps caractéristiques
    print(f"\nConstantes de temps caractéristiques:")
    print(f"   τ1 = R2*C1 = {params['R2']*params['C1']:.2e} s (fc ≈ {1/(2*np.pi*params['R2']*params['C1']):.0f} Hz)")
    print(f"   τ2 = R5*C2 = {params['R5']*params['C2']:.2e} s (fc ≈ {1/(2*np.pi*params['R5']*params['C2']):.0f} Hz)")
    print(f"   τ3 = R6*C3 = {params['R6']*params['C3']:.2e} s (fc ≈ {1/(2*np.pi*params['R6']*params['C3']):.0f} Hz)")
    
    # Gain approximatif
    gain_approx = params['R6'] / (params['R1'] + params['R2'])
    print(f"\nGain approximatif: {gain_approx:.3f} ({20*np.log10(gain_approx):.1f} dB)")

def recommendations():
    """Donne des recommandations pour améliorer la cohérence"""
    
    print("\n=== RECOMMANDATIONS ===\n")
    
    print("1. CAUSES POSSIBLES DES DIFFÉRENCES:")
    print("   • Précision numérique lors des calculs symboliques")
    print("   • Simplifications différentes dans les expressions")
    print("   • Ordre des opérations différent")
    print("   • Approximations dans l'expression numérique")
    
    print("\n2. POUR AMÉLIORER LA COHÉRENCE:")
    print("   • Vérifier que les mêmes paramètres sont utilisés")
    print("   • Utiliser une précision numérique plus élevée")
    print("   • Comparer les expressions terme par terme")
    print("   • Vérifier les simplifications symboliques")
    
    print("\n3. DIFFÉRENCES ACCEPTABLES:")
    print("   • Différences < 1 dB : Excellente cohérence")
    print("   • Différences 1-5 dB : Bonne cohérence")
    print("   • Différences 5-20 dB : Cohérence acceptable")
    print("   • Différences > 20 dB : Problème à investiguer")
    
    print(f"\n4. VOTRE CAS:")
    print(f"   • Différence observée: ~16.7 dB")
    print(f"   • Statut: Cohérence acceptable mais à améliorer")
    print(f"   • Action: Vérifier la substitution des paramètres")

def main():
    """Fonction principale de diagnostic"""
    
    print("DIAGNOSTIC DES DIAGRAMMES DE BODE")
    print("=" * 50)
    
    # Analyser les expressions
    h1_symbolic, h1_numeric = extract_and_compare()
    
    # Analyser la réponse fréquentielle
    analyze_frequency_response()
    
    # Vérifier les paramètres
    check_parameter_substitution()
    
    # Donner des recommandations
    recommendations()
    
    print("\n" + "=" * 50)
    print("FIN DU DIAGNOSTIC")

if __name__ == "__main__":
    main()
