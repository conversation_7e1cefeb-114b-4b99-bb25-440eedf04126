# Implémentation de la résistance dynamique physique

## 🎯 Formule physique implémentée

```
rd = n * VT / I
```

Où :
- **n** = facteur d'idéalité (1.0 par défaut, entre 1 et 2)
- **VT** = tension thermique (0.026 V à 25°C)
- **I** = courant DC traversant la diode

## 🔧 Paramètres ajoutés

### Nouveaux paramètres symboliques
- `n_Dnom`: Facteur d'idéalité (défaut: 1.0)
- `VT_Dnom`: Tension thermique (défaut: 0.026 V)
- `rd_Dnom`: Résistance dynamique (calculée automatiquement)

### Paramètres existants
- `Vth_Dnom`: Tension de seuil (défini par l'utilisateur)

## 📊 Calcul automatique

### 1. Analyse DC
- Calcul du courant `Id_dc` de la diode
- Application de la contrainte `V_anode - V_cathode = Vth`

### 2. Calcul de rd
```python
Id_dc_numeric = float(courant_dc_diode)
n_numeric = 1.0  # Facteur d'idéalité
VT_numeric = 0.026  # Tension thermique

if Id_dc_numeric > 1e-12:  # Diode en conduction
    rd_calculated = (n_numeric * VT_numeric) / Id_dc_numeric
else:  # Diode bloquée
    rd_calculated = 1e6  # Résistance très élevée
```

### 3. Analyse AC
- Utilisation de la `rd` calculée au lieu de la valeur fixe
- Comportement AC réaliste basé sur le point de fonctionnement DC

## 📈 Exemple de calcul

### Circuit test
```
V1 1 0 DC 5 AC 3
R1 1 2 100
D1 2 3 2.2
R2 3 0 100
```

### Calcul DC
- **V1 = 5.0 V** (source)
- **V2 = 3.1 V** (calculé par diviseur avec contrainte diode)
- **V3 = 0.9 V** (V2 - Vth = 3.1 - 2.2)
- **Id_dc = 0.019 A** (19 mA)

### Calcul de rd
```
rd = n * VT / Id_dc = 1.0 * 0.026 / 0.019 = 1.37 Ω
```

### Analyse AC
```
R_total = R1 + rd + R2 = 100 + 1.37 + 100 = 201.37 Ω
V3_ac = V1_ac * R2 / R_total = 3 * 100 / 201.37 = 1.49 V
```

## 🔄 Comparaison avec l'ancienne méthode

| Méthode | rd (Ω) | V3_ac (V) | Atténuation |
|---------|--------|-----------|-------------|
| **Ancienne** (fixe) | 10.0 | 1.43 | 0.48 |
| **Nouvelle** (physique) | 1.37 | 1.49 | 0.50 |

## 💡 Avantages de l'implémentation physique

### ✅ Réalisme physique
- **Basé sur la vraie physique** des diodes
- **Dépend du point de fonctionnement** DC réel
- **Formule standard** de l'électronique

### ✅ Comportement adaptatif
- **Courant élevé** → rd faible → moins d'atténuation AC
- **Courant faible** → rd élevée → plus d'atténuation AC
- **Diode bloquée** → rd très élevée → circuit ouvert

### ✅ Paramètres physiques
- **Facteur d'idéalité n** : Ajustable selon le type de diode
- **Tension thermique VT** : Dépend de la température
- **Calcul automatique** : Pas d'intervention utilisateur

## 🎛️ Contrôle de l'atténuation

### Pour plus d'atténuation
1. **Réduire le courant DC** (augmenter les résistances série)
2. **Augmenter n** (diode moins idéale)
3. **Réduire VT** (température plus basse)

### Pour moins d'atténuation
1. **Augmenter le courant DC** (réduire les résistances série)
2. **Réduire n** (diode plus idéale)
3. **Augmenter VT** (température plus élevée)

## 📝 Messages d'information

Le simulateur affiche maintenant :
```
Diode D1: Id_dc = 0.019000 A, rd = 1.368 Ohm (n*VT/I)

===== Diode D1 parameters =====
  Vth_D1 (Tension de seuil) = 2.2 V
  n_D1 (Facteur d'idealite) = 1.0
  VT_D1 (Tension thermique) = 0.026 V
  rd_D1 (Resistance dynamique) = 1.368 Ohm
  Id_dc_D1 (Courant DC) = 0.019 A
  Formule: rd = n*VT/Id_dc = 1.0*0.026/0.019
```

## ✅ Statut

**IMPLÉMENTATION PHYSIQUE COMPLÈTE**

- ✅ Formule physique `rd = n*VT/I`
- ✅ Calcul automatique basé sur le courant DC
- ✅ Paramètres physiques (n, VT)
- ✅ Comportement adaptatif
- ✅ Messages informatifs détaillés
- ✅ Atténuation AC réaliste

La résistance dynamique est maintenant calculée de manière physiquement correcte !
