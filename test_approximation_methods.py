#!/usr/bin/env python3
"""
Script de test pour trouver la meilleure méthode d'approximation
Analyse les écarts en dB pour différents sets de variables
"""

import numpy as np
import sympy
import matplotlib.pyplot as plt
from scipy.optimize import minimize
import time

def read_transfer_function(filename):
    """Lit la fonction de transfert depuis un fichier"""
    try:
        with open(filename, 'r') as f:
            content = f.read().strip()
        return content.replace('^', '**')
    except FileNotFoundError:
        print(f"Fichier {filename} non trouvé")
        return None

def extract_variables_from_expression(expr_str):
    """Extrait les variables d'une expression"""
    import re
    variables = set()
    pattern = r'\b([A-Za-z][A-Za-z0-9_]*)\b'
    matches = re.findall(pattern, expr_str)
    
    for match in matches:
        if match not in ['s', 'j', 'pi', 'e', 'I', 'E', 'sin', 'cos', 'tan', 'log', 'exp']:
            variables.add(match)
    
    return sorted(list(variables))

def count_terms_in_expression(expr_str):
    """Compte le nombre de termes dans une expression"""
    try:
        expr = sympy.sympify(expr_str)
        expanded = sympy.expand(expr)
        if hasattr(expanded, 'as_ordered_terms'):
            return len(expanded.as_ordered_terms())
        else:
            return 1
    except:
        return expr_str.count('+') + expr_str.count('-') + 1

def generate_test_sets(variables, num_sets=20):
    """Génère des sets de test pour les variables"""
    # Définir des plages typiques pour chaque type de composant
    ranges = {}
    for var in variables:
        if var.startswith('C'):
            ranges[var] = (1e-12, 1e-3)  # 1pF à 1mF
        elif var.startswith('R'):
            ranges[var] = (1, 1e6)       # 1Ω à 1MΩ
        elif var.startswith('L'):
            ranges[var] = (1e-9, 1)      # 1nH à 1H
        elif 'gm' in var:
            ranges[var] = (0.001, 1)     # 1mS à 1S
        elif 'ro' in var or 'rpi' in var:
            ranges[var] = (100, 100000)  # 100Ω à 100kΩ
        else:
            ranges[var] = (0.1, 1000)    # Valeur par défaut
    
    test_sets = []
    for i in range(num_sets):
        test_set = {}
        for var in variables:
            min_val, max_val = ranges[var]
            if max_val / min_val > 100:  # Échelle logarithmique
                log_min = np.log10(min_val)
                log_max = np.log10(max_val)
                log_val = np.random.uniform(log_min, log_max)
                test_set[var] = 10 ** log_val
            else:  # Échelle linéaire
                test_set[var] = np.random.uniform(min_val, max_val)
        test_sets.append(test_set)
    
    return test_sets

def evaluate_transfer_function(expr_str, variables, values, frequencies):
    """Évalue la fonction de transfert pour un set de valeurs"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s
        
        expr = sympy.sympify(expr_str, locals=symbols_dict)
        
        # Créer une fonction rapide
        func_args = [s] + [symbols_dict[var] for var in variables]
        fast_func = sympy.lambdify(func_args, expr, 'numpy')
        
        responses = []
        for freq in frequencies:
            s_val = 2j * np.pi * freq
            param_values = [values[var] for var in variables]
            
            try:
                response = fast_func(s_val, *param_values)
                responses.append(complex(response))
            except:
                responses.append(0.0 + 0.0j)
        
        return np.array(responses)
        
    except Exception as e:
        print(f"Erreur évaluation: {e}")
        return np.zeros(len(frequencies), dtype=complex)

def test_approximation_method_1(original_expr, variables, target_reduction=0.6):
    """Méthode 1: Simplification symbolique basique"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s
        
        expr = sympy.sympify(original_expr, locals=symbols_dict)
        original_terms = count_terms_in_expression(str(expr))
        target_terms = max(2, int(original_terms * (1 - target_reduction)))
        
        # Simplification
        simplified = sympy.simplify(expr)
        
        # Si encore trop complexe, troncature
        if count_terms_in_expression(str(simplified)) > target_terms:
            if simplified.is_rational_function():
                num, den = sympy.fraction(simplified)
                
                # Garder les termes les plus significatifs
                if sympy.expand(num).is_Add:
                    num_terms = list(sympy.expand(num).as_ordered_terms())
                    num = sum(num_terms[:max(1, target_terms//2)])
                
                if sympy.expand(den).is_Add:
                    den_terms = list(sympy.expand(den).as_ordered_terms())
                    den = sum(den_terms[:max(1, target_terms//2)])
                
                simplified = num / den
        
        return str(simplified)
        
    except Exception as e:
        print(f"Erreur méthode 1: {e}")
        return str(sympy.simplify(sympy.sympify(original_expr)))

def test_approximation_method_2(original_expr, variables, target_reduction=0.6):
    """Méthode 2: Approximation par développement en série (basses fréquences)"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s
        
        expr = sympy.sympify(original_expr, locals=symbols_dict)
        original_terms = count_terms_in_expression(str(expr))
        target_terms = max(2, int(original_terms * (1 - target_reduction)))
        
        # Développement en série autour de s=0
        try:
            series_order = min(4, target_terms)
            series = sympy.series(expr, s, 0, n=series_order).removeO()
            if count_terms_in_expression(str(series)) <= target_terms:
                return str(series)
        except:
            pass
        
        # Fallback vers simplification
        return test_approximation_method_1(original_expr, variables, target_reduction)
        
    except Exception as e:
        print(f"Erreur méthode 2: {e}")
        return test_approximation_method_1(original_expr, variables, target_reduction)

def test_approximation_method_3(original_expr, variables, target_reduction=0.6):
    """Méthode 3: Approximation par sélection des termes dominants"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s
        
        expr = sympy.sympify(original_expr, locals=symbols_dict)
        original_terms = count_terms_in_expression(str(expr))
        target_terms = max(2, int(original_terms * (1 - target_reduction)))
        
        if expr.is_rational_function():
            num, den = sympy.fraction(expr)
            
            # Analyser les termes par degré en s
            def select_dominant_terms(poly_expr, max_terms):
                expanded = sympy.expand(poly_expr)
                if expanded.is_Add:
                    terms = list(expanded.as_ordered_terms())
                    
                    # Séparer par degré en s
                    terms_by_degree = {}
                    for term in terms:
                        degree = sympy.degree(term, s) if s in term.free_symbols else 0
                        if degree not in terms_by_degree:
                            terms_by_degree[degree] = []
                        terms_by_degree[degree].append(term)
                    
                    # Sélectionner les termes les plus importants
                    selected = []
                    degrees = sorted(terms_by_degree.keys(), reverse=True)
                    
                    # Garder au moins un terme de chaque degré important
                    for degree in degrees:
                        if len(selected) < max_terms:
                            selected.extend(terms_by_degree[degree][:1])
                    
                    # Compléter avec d'autres termes si nécessaire
                    remaining = max_terms - len(selected)
                    if remaining > 0:
                        for degree in degrees:
                            for term in terms_by_degree[degree][1:]:
                                if len(selected) < max_terms:
                                    selected.append(term)
                    
                    return sum(selected[:max_terms]) if selected else terms[0]
                return poly_expr
            
            num_approx = select_dominant_terms(num, max(1, target_terms//2))
            den_approx = select_dominant_terms(den, max(1, target_terms//2))
            
            return str(num_approx / den_approx)
        else:
            return test_approximation_method_1(original_expr, variables, target_reduction)
            
    except Exception as e:
        print(f"Erreur méthode 3: {e}")
        return test_approximation_method_1(original_expr, variables, target_reduction)

def calculate_db_error(original_responses, approx_responses):
    """Calcule l'erreur en dB entre deux réponses"""
    original_mag = np.abs(original_responses)
    approx_mag = np.abs(approx_responses)
    
    # Éviter les divisions par zéro
    original_mag = np.where(original_mag > 1e-15, original_mag, 1e-15)
    approx_mag = np.where(approx_mag > 1e-15, approx_mag, 1e-15)
    
    # Calculer en dB
    original_db = 20 * np.log10(original_mag)
    approx_db = 20 * np.log10(approx_mag)
    
    # Erreur RMS en dB
    error_db = np.sqrt(np.mean((original_db - approx_db)**2))
    
    # Erreur maximale en dB
    max_error_db = np.max(np.abs(original_db - approx_db))
    
    return error_db, max_error_db

def main():
    print("=== Test des méthodes d'approximation ===")
    
    # Lire la fonction de transfert
    transfer_function = read_transfer_function("FTC.txt")
    if not transfer_function:
        return
    
    print(f"Fonction de transfert lue: {len(transfer_function)} caractères")
    print(f"Début: {transfer_function[:100]}...")
    
    # Extraire les variables
    variables = extract_variables_from_expression(transfer_function)
    print(f"Variables: {variables}")
    
    # Compter les termes originaux
    original_terms = count_terms_in_expression(transfer_function)
    print(f"Termes originaux: {original_terms}")
    
    # Générer des sets de test
    test_sets = generate_test_sets(variables, num_sets=10)
    print(f"Générés {len(test_sets)} sets de test")
    
    # Fréquences de test
    frequencies = np.logspace(-1, 8, 50)  # 0.1 Hz à 100 MHz
    
    # Tester les méthodes
    methods = [
        ("Simplification basique", test_approximation_method_1),
        ("Développement série BF", test_approximation_method_2),
        ("Sélection dominants", test_approximation_method_3)
    ]
    
    results = {}
    
    for method_name, method_func in methods:
        print(f"\n=== Test de la méthode: {method_name} ===")
        
        # Tester avec différents niveaux de réduction
        for reduction in [0.5, 0.6, 0.7, 0.8]:
            print(f"\nRéduction: {reduction*100:.0f}%")
            
            try:
                # Générer l'approximation
                start_time = time.time()
                approximation = method_func(transfer_function, variables, reduction)
                approx_time = time.time() - start_time
                
                approx_terms = count_terms_in_expression(approximation)
                actual_reduction = (original_terms - approx_terms) / original_terms * 100
                
                print(f"Approximation: {approx_terms} termes (réduction: {actual_reduction:.1f}%)")
                print(f"Temps: {approx_time:.3f}s")
                print(f"Expression: {approximation[:100]}...")
                
                # Tester sur tous les sets
                errors_rms = []
                errors_max = []
                
                for i, test_set in enumerate(test_sets):
                    # Évaluer l'original
                    original_responses = evaluate_transfer_function(
                        transfer_function, variables, test_set, frequencies
                    )
                    
                    # Évaluer l'approximation
                    approx_responses = evaluate_transfer_function(
                        approximation, variables, test_set, frequencies
                    )
                    
                    # Calculer l'erreur
                    error_rms, error_max = calculate_db_error(original_responses, approx_responses)
                    errors_rms.append(error_rms)
                    errors_max.append(error_max)
                
                # Statistiques
                mean_rms_error = np.mean(errors_rms)
                mean_max_error = np.mean(errors_max)
                std_rms_error = np.std(errors_rms)
                
                print(f"Erreur RMS moyenne: {mean_rms_error:.2f} ± {std_rms_error:.2f} dB")
                print(f"Erreur max moyenne: {mean_max_error:.2f} dB")
                
                # Stocker les résultats
                key = f"{method_name}_{reduction*100:.0f}%"
                results[key] = {
                    'method': method_name,
                    'reduction': reduction,
                    'terms': approx_terms,
                    'actual_reduction': actual_reduction,
                    'time': approx_time,
                    'rms_error_mean': mean_rms_error,
                    'rms_error_std': std_rms_error,
                    'max_error_mean': mean_max_error,
                    'approximation': approximation
                }
                
            except Exception as e:
                print(f"Erreur lors du test: {e}")
    
    # Afficher le résumé
    print("\n" + "="*80)
    print("RÉSUMÉ DES RÉSULTATS")
    print("="*80)
    
    # Trier par erreur RMS
    sorted_results = sorted(results.items(), key=lambda x: x[1]['rms_error_mean'])
    
    print(f"{'Méthode':<25} {'Réd%':<5} {'Termes':<7} {'Erreur RMS':<12} {'Erreur Max':<12} {'Temps':<8}")
    print("-" * 80)
    
    for key, result in sorted_results:
        print(f"{result['method']:<25} {result['reduction']*100:>4.0f}% {result['terms']:>6} "
              f"{result['rms_error_mean']:>8.2f}±{result['rms_error_std']:>4.2f} "
              f"{result['max_error_mean']:>8.2f} dB {result['time']:>6.3f}s")
    
    # Meilleur résultat
    if sorted_results:
        best = sorted_results[0][1]
        print(f"\n🏆 MEILLEURE MÉTHODE: {best['method']}")
        print(f"   Réduction: {best['reduction']*100:.0f}% ({best['terms']} termes)")
        print(f"   Erreur RMS: {best['rms_error_mean']:.2f} ± {best['rms_error_std']:.2f} dB")
        print(f"   Erreur Max: {best['max_error_mean']:.2f} dB")
        print(f"   Expression: {best['approximation'][:150]}...")

if __name__ == "__main__":
    main()
