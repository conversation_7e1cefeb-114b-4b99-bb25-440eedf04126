--- Simulation Log - 2025-06-12 11:22:44 ---

=== Simulateur de Circuit Électrique Symbolique ===
Entrez votre netlist (terminez par une ligne vide).
Format: [Type][ID] [Nœud1] [Nœud2] ... [Valeur/Paramètre] [Type_optionnel] (Nœud 0 = masse)
Types: R, C, L, V (DC/AC), Q (BJT), M (MOSFET), X (OpAmp), D (Diode)
Ex: R1 1 0 1k; V1 1 0 DC 5 AC 1; Q1 2 1 0 100 NPN 10p 2p; D1 1 2 0.7
---
  R[ID] [Nœud+] [Nœud-] [Valeur]
  C[ID] [Nœud+] [Nœud-] [Valeur]
  L[ID] [Nœud+] [Nœud-] [Valeur]
  V[ID] [Nœud+] [Nœud-] [DC Valeur_DC] [AC Valeur_AC] (DC ou AC ou les deux)
  Q[ID] [Nœud_Collecteur] [Nœud_Base] [Nœud_Emetteur] [Beta_Valeur] [Type_BJT_optionnel, ex: PNP] [Cbe_Valeur_optionnel] [Cbc_Valeur_optionnel]
  M[ID] [Nœud_Drain] [Nœud_Gate] [Nœud_Source] [Type_MOSFET_optionnel, ex: PMOS] [Cgs_Valeur_optionnel] [Cgd_Valeur_optionnel]
  X[ID] [Nœud_In+] [Nœud_In-] [Nœud_Out] [Av_Valeur_optionnel] [Fc1_Valeur_optionnel] [Fc2_Valeur_optionnel] [Fc3_Valeur_optionnel] [Fc4_Valeur_optionnel] Amplificateur opérationnel non idéal
  D[ID] [Nœud_Anode] [Nœud_Cathode] [Von_Valeur_optionnel] (Tension de seuil assumée pour Iref=2.5mA)
Exemples (utilisant les nœuds 0, 1, 2, 3 pour la clarté):
R1 1 0 1k
C1 1 2 100n
L1 2 0 1m
V1 1 0 DC 5 AC 1
V2 2 0 AC 0.5
V3 3 0 DC 12
Q1 2 1 0 100 NPN 10p 2p
M1 4 3 0 NMOS 5p 1p
X1 5 6 7 100k 10 1M 10M 100M
D1 1 2 0.7
---
Activer les approximations symboliques? (o/n, défaut: o): Approximations activées.

Circuit: 3 nœuds actifs (max 3). R=2, C=0, L=0, V=1, Q=0, M=0, X=1, D=0

=== Calcul des Paramètres Petit Signal ===

=== Résultats DC (Console) ===

Système DC:
Inconnues x: [V_1, V_2, V_3, I_V1, I_X1_out]

Système DC:
Inconnues x: [V_1, V_2, V_3, I_V1, I_X1_out]
Équations F(x)=0:
Équations F(x)=0:
  F[0] = I_V1 + (V_1 - V_2)/R1
  F[1] = (V_2 - V_3)/R2 + (-V_1 + V_2)/R1
  F[2] = I_X1_out + (-V_2 + V_3)/R2
  F[3] = -V1 + V_1
  F[4] = -V_2
--------------------
--------------------

Point DC calculé:

Tensions aux nœuds (V0=0):
V1 = 1.0000V
V2 = 0.0000V
V3 = -2.0000V

Courants DC sources tension (+ vers -):
  V1 = -1.0000e-03 A

Courants DC inductances (+ vers -):
Aucun inductances dans le circuit.

Courants DC résistances (+ vers -):
  R1 = 1.0000e-03 A
  R2 = 1.0000e-03 A

Courants DC condensateurs (+ vers -):
Aucun condensateurs dans le circuit.
  Note: Les condensateurs sont des circuits ouverts en DC (I=0)

Courants DC des BJT (Ic, Ib):
Aucun BJT.

Courants DC MOSFET (Id):
Aucun MOSFETs dans le circuit.

Courants sortie DC AOPs (sortant):
  X1 = 1.0000e-03 A

Courants DC Diodes (Anode vers Cathode):
Aucun Diodes dans le circuit.

=== Paramètres Petit Signal (Console) ===

Paramètres calculés à partir du point de polarisation DC résolu:

Aucun BJT.

Aucun MOSFET.

Aucun Diode.

=== Paramètres AC OpAmps (Console) ===
  X1:
    Av (Num)  = 1.0e+05
    Fc1 (Num) = 1000000.0 Hz
    Fc2 (Num) = 1.0e+07 Hz
    Fc3 (Num) = 1.0e+08 Hz
    Fc4 (Num) = 1.0e+09 Hz

=== Résultats AC Entièrement Symboliques (Console - Troncature) ===

Note: Résultats en fct de s et symboles composants.

Tensions aux nœuds AC (V0=0):
V1 (brut) = V1

V2 (brut) = (((1/R2 - 1/(R2**2*(1/R2 + 1/R1)))*(-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1) + V1/(R1*R2*(1/R2 + 1/R1)))/(R2*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))) + V1/R1)/(1/R2 + 1/R1)

V3 (brut) = ((1/R2 - 1/(R2**2*(1/R2 + 1/R1)))*(-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1) + V1/(R1*R2*(1/R2 + 1/R1)))/(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))


Courants AC sources tension (+ vers -):
I_V1 (brut) = -V1/R1 + (-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(R1*R2*(1/R2 + 1/R1)*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)) + V1/(R1**2*(1/R2 + 1/R1)) + V1/(R1**2*R2**2*(1/R2 + 1/R1)**2*(1/R2 - 1/(R2**2*(1/R2 + 1/R1))))


Courants sortie AC AOPs (sortant):
I_X1_out (brut) = (1/R2 - 1/(R2**2*(1/R2 + 1/R1)))*(-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)


Courants AC éléments passifs (+ vers -):
I_R1 (brut) = (V1 - (((1/R2 - 1/(R2**2*(1/R2 + 1/R1)))*(-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1) + V1/(R1*R2*(1/R2 + 1/R1)))/(R2*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))) + V1/R1)/(1/R2 + 1/R1))/R1

I_R2 (brut) = (-((1/R2 - 1/(R2**2*(1/R2 + 1/R1)))*(-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1) + V1/(R1*R2*(1/R2 + 1/R1)))/(1/R2 - 1/(R2**2*(1/R2 + 1/R1))) + (((1/R2 - 1/(R2**2*(1/R2 + 1/R1)))*(-Av_X1*V1/(R1*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) - V1*(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1)/(R1*R2*(1/R2 + 1/R1)*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))))/(Av_X1/(R2*(1 + s/(2*pi*Fc1_X1))*(1 + s/(2*pi*Fc2_X1))*(1 + s/(2*pi*Fc3_X1))*(1 + s/(2*pi*Fc4_X1))*(1/R2 + 1/R1)) + 1) + V1/(R1*R2*(1/R2 + 1/R1)))/(R2*(1/R2 - 1/(R2**2*(1/R2 + 1/R1)))) + V1/R1)/(1/R2 + 1/R1))/R2


=== Résultats AC avec Paramètres Numériques (Console - Troncature) ===

Note: Params SS calculés du DC. Cs de netlist/défaut.

Tensions aux nœuds AC (V0=0):
V1 (brut) = 4.81639165845957e-5*(-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1))) + 1.0
V1 (approx) = (1.9248716810113e-30*s**4 + 1.34367955908785e-20*s**3 + 8.51933846015724e-12*s**2 + 0.00053046342760116*s + 100002999.183621)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)

V2 (brut) = 1000.00004416392*(-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1))) + 0.999999998
V2 (approx) = (1.28324779137695e-30*s**4 + 8.95786375542666e-21*s**3 + 5.67955899130154e-12*s**2 + 0.000353642286179718*s + 1999.58362285781)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)

V3 (brut) = 3000.00003816392*(-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1))) + 0.999999996
V3 (approx) = (1.46747335194303e-38*s**4 + 1.0243872181007e-28*s**3 + 6.49492757870268e-20*s**2 + 4.04411863847506e-12*s - 199999999.616369)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)


Courants AC sources tension (+ vers -):
I_V1 (brut) = 0.999999996*(-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1))) - 2.99999999101259e-12
I_V1 (approx) = (-6.41623891559226e-34*s**4 - 4.47893184888863e-24*s**3 - 2.83977947737503e-15*s**2 - 1.76821141951905e-7*s - 100000.999700001)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)


Courants sortie AC AOPs (sortant):
I_X1_out (brut) = (-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))
I_X1_out (approx) = (-6.41623888351106e-34*s**4 - 4.47893182649397e-24*s**3 - 2.83977946317613e-15*s**2 - 1.768211410678e-7*s - 100000.999799996)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)


Courants AC éléments passifs (+ vers -):
I_R1 (brut) = -0.999999996*(-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1))) + 1.99999994343614e-12
I_R1 (approx) = (6.41623889634354e-34*s**4 + 4.47893183545183e-24*s**3 + 2.83977946885569e-15*s**2 + 1.76821141421442e-7*s + 100000.999599998)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)

I_R2 (brut) = -0.999999997*(-0.999999996 - 99999.9998/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1)))/(3000.00006103516 + 100000004.0/((5.0e-10*s/pi + 1)*(5.0e-9*s/pi + 1)*(5.0e-8*s/pi + 1)*(5.0e-7*s/pi + 1))) + 1.00000002722922e-12
I_R2 (approx) = (6.41623888351106e-34*s**4 + 4.47893182649397e-24*s**3 + 2.83977946317613e-15*s**2 + 1.768211410678e-7*s + 100000.999599996)/(1.92487171191442e-30*s**4 + 1.34367958066014e-20*s**3 + 8.51933859693214e-12*s**2 + 0.000530463436117559*s + 100003004.000061)


=== Expressions Temporelles AC+DC (Console) ===
Fréquence pour expressions temporelles [Hz] (défaut: 1000 Hz): 
Expressions temporelles évaluées à 1000.0 Hz:

Tensions aux nœuds (V0=0):
V1(t) = 1.000000 + 1.000000·cos(2π·1000·t + 0.000000)

V2(t) = 0.000020·cos(2π·1000·t + 0.001111)

V3(t) = -2.000000 + 1.999940·cos(2π·1000·t + 3.141593)


Courants des sources de tension (+ vers -):
I_V1(t) = -0.001000 + 0.001000·cos(2π·1000·t + 3.141593)


Courants des éléments passifs (+ vers -):
I_R1(t) = 0.001000 + 0.001000·cos(2π·1000·t + -0.000000)

I_R2(t) = 0.001000 + 0.001000·cos(2π·1000·t + -0.000000)


Courants de sortie des OpAmps:
I_X1_out(t) = 0.001000 + 0.001000·cos(2π·1000·t + 3.141593)


=== Analyse de Fonction de Transfert ===
Nœuds disponibles: [1, 2, 3]
Nœud d'entrée (source): Nœud de sortie: 