#!/usr/bin/env python3
"""
Test final du circuit avec les corrections génériques
"""

import test

def main():
    print("🧪 === TEST FINAL DU CIRCUIT AVEC CORRECTIONS GÉNÉRIQUES ===")
    
    # Circuit de test
    netlist_str = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit testé:")
    print(netlist_str)
    print("\n" + "="*60)
    
    # Paramètres d'analyse
    frequency_hz = 1000.0
    do_transfer_function = True
    input_node = "1"
    output_node = "7"
    freq_min = 1.0
    freq_max = 1e6  # Réduire la plage pour accélérer
    
    print(f"📊 Analyse H(s) = V{output_node}/V{input_node}")
    print(f"   Fréquence: {frequency_hz} Hz")
    print(f"   Plage: {freq_min} Hz à {freq_max} Hz")
    
    try:
        # Lancer l'analyse
        print("\n🔧 Lancement de l'analyse...")
        
        test.solve_circuit(
            netlist_str=netlist_str,
            frequency_hz=frequency_hz,
            do_transfer_function=do_transfer_function,
            input_node=input_node,
            output_node=output_node,
            freq_min=freq_min,
            freq_max=freq_max,
            laplace_domain=True
        )
        
        print("\n✅ ANALYSE TERMINÉE AVEC SUCCÈS!")
        print("🎯 Les corrections génériques ont fonctionné")
        print("📁 Vérifiez le fichier Results_Simulation_*.txt pour les détails")
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
