#!/usr/bin/env python3

"""
Test script pour vérifier que les résistances de compensation BJT 
n'apparaissent pas dans les expressions symboliques mais sont toujours 
substituées par leurs valeurs numériques.
"""

import sys
import os
import re

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import (
    add_bjt_compensation_components, 
    substitute_values, 
    dc_analysis, 
    ac_analysis,
    is_bjt_compensation_component
)

def test_bjt_compensation_resistance_substitution():
    """Test que les résistances de compensation BJT sont toujours numériques dans les expressions symboliques"""
    
    print("=== Test des résistances de compensation BJT ===")
    
    # Circuit simple avec un BJT
    original_netlist = [
        ['V1', '1', '0', 'DC', '5', 'AC', '1'],
        ['R1', '1', '2', '1k'],
        ['Q1', '3', '2', '0', '100', 'NPN'],
        ['R2', '3', '4', '2k'],
        ['V2', '4', '0', 'DC', '10']
    ]
    
    # Ajouter les composants de compensation BJT
    netlist, values, bjt_created_nodes = add_bjt_compensation_components(original_netlist, {})
    
    print(f"Netlist originale: {len(original_netlist)} composants")
    print(f"Netlist avec compensation: {len(netlist)} composants")
    print(f"Noeuds créés pour BJT: {bjt_created_nodes}")
    
    # Identifier les résistances de compensation créées
    compensation_resistors = []
    for comp in netlist:
        if comp[0][0].upper() == 'R' and is_bjt_compensation_component(comp[0], values):
            compensation_resistors.append(comp[0])
    
    print(f"Résistances de compensation créées: {compensation_resistors}")
    
    # Ajouter les valeurs des composants originaux
    for comp in original_netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = comp[3].replace('k', '000')
        elif comp_type == 'V':
            values[f"Vdc_{comp_name}"] = comp[4] if len(comp) > 4 else '0'
            values[f"Vac_{comp_name}"] = comp[6] if len(comp) > 6 else '0'
        elif comp_type == 'Q':
            values[f"Vbe_on_{comp_name}"] = "0.7"
            values[f"Vt_{comp_name}"] = "0.025"
            values[f"VA_{comp_name}"] = "100"
            values[f"beta_{comp_name}"] = comp[4] if len(comp) > 4 else "100"
    
    # Obtenir tous les noeuds
    all_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    print(f"\nValeurs des composants:")
    for comp_name, comp_value in values.items():
        if not comp_name.startswith('comp_BJT_'):
            print(f"  {comp_name} = {comp_value}")
    
    # Effectuer l'analyse DC
    print(f"\n=== Analyse DC ===")
    voltages_dc, currents_dc = dc_analysis(netlist, values, all_nodes)
    
    # Vérifier les expressions symboliques DC
    print(f"\nExpressions symboliques DC:")
    for node, voltage in voltages_dc.items():
        if node != '0':
            print(f"  V{node} = {voltage}")
            # Vérifier qu'aucune résistance de compensation n'apparaît symboliquement
            for comp_res in compensation_resistors:
                if comp_res in str(voltage):
                    print(f"    ❌ ERREUR: Résistance de compensation {comp_res} trouvée dans V{node}")
                else:
                    print(f"    ✓ OK: Résistance de compensation {comp_res} non trouvée dans V{node}")
    
    # Effectuer la substitution numérique
    print(f"\n=== Substitution numérique DC ===")
    dc_voltages_num = substitute_values(voltages_dc, values)
    
    print(f"Expressions numériques DC:")
    for node, voltage in dc_voltages_num.items():
        if node != '0':
            print(f"  V{node} = {voltage}")
    
    # Effectuer l'analyse AC
    print(f"\n=== Analyse AC ===")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_nodes, laplace_domain=True)
    
    # Vérifier les expressions symboliques AC
    print(f"\nExpressions symboliques AC:")
    for node, voltage in voltages_ac.items():
        if node != '0':
            print(f"  V{node} = {voltage}")
            # Vérifier qu'aucune résistance de compensation n'apparaît symboliquement
            for comp_res in compensation_resistors:
                if comp_res in str(voltage):
                    print(f"    ❌ ERREUR: Résistance de compensation {comp_res} trouvée dans V{node}")
                else:
                    print(f"    ✓ OK: Résistance de compensation {comp_res} non trouvée dans V{node}")
    
    # Effectuer la substitution numérique AC
    print(f"\n=== Substitution numérique AC ===")
    ac_voltages_num = substitute_values(voltages_ac, values)
    
    print(f"Expressions numériques AC:")
    for node, voltage in ac_voltages_num.items():
        if node != '0':
            print(f"  V{node} = {voltage}")
    
    print(f"\n=== Résumé du test ===")
    
    # Compter les erreurs
    total_errors = 0
    total_checks = 0
    
    for analysis_name, voltages in [("DC", voltages_dc), ("AC", voltages_ac)]:
        for node, voltage in voltages.items():
            if node != '0':
                for comp_res in compensation_resistors:
                    total_checks += 1
                    if comp_res in str(voltage):
                        total_errors += 1
                        print(f"❌ {analysis_name}: Résistance {comp_res} trouvée dans V{node}")
    
    if total_errors == 0:
        print(f"✅ SUCCÈS: Aucune résistance de compensation trouvée dans les expressions symboliques ({total_checks} vérifications)")
    else:
        print(f"❌ ÉCHEC: {total_errors} résistances de compensation trouvées sur {total_checks} vérifications")
    
    return total_errors == 0

if __name__ == '__main__':
    success = test_bjt_compensation_resistance_substitution()
    sys.exit(0 if success else 1)
