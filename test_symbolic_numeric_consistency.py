#!/usr/bin/env python3
"""
Script de test pour vérifier la cohérence entre les expressions symboliques et numériques
pour les circuits contenant des transistors BJT et des amplificateurs opérationnels.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def test_simple_bjt_circuit():
    """Test avec un circuit simple contenant un transistor BJT"""
    print("=== Test Circuit BJT Simple ===")
    
    netlist_str = """
V1 1 0 DC 5 AC 1
R1 1 2 1k
Q1 3 2 0 100 NPN
R2 3 4 2k
V2 4 0 DC 10
"""
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    original_nodes = sorted(list(set(comp[i] for comp in original_netlist for i in [1,2]) - {'0'}))
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'Q':
            values[f"Vbe_on_{comp_name}"] = "0.7"
            values[f"Vt_{comp_name}"] = "0.025"
            values[f"VA_{comp_name}"] = "100"
            values[f"beta_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100"
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse DC
    print("Analyse DC...")
    voltages_dc, currents_dc = dc_analysis(netlist, values, all_unique_nodes)
    dc_voltages_num = substitute_values(voltages_dc, values)
    dc_currents_num = substitute_values(currents_dc, values)
    
    # Analyse AC
    print("Analyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    ac_voltages_num = substitute_values(voltages_ac, values)
    ac_currents_num = substitute_values(currents_ac, values)
    
    # Vérification de cohérence
    print("\n=== Vérification de cohérence DC ===")
    dc_inconsistencies = 0
    for node in original_nodes:
        if node in voltages_dc and node in dc_voltages_num:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_dc[node], dc_voltages_num[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_DC:")
                print(f"   Symbolique: {voltages_dc[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                dc_inconsistencies += 1
            else:
                print(f"✅ V{node}_DC cohérent")
    
    for comp in netlist:
        if comp[0][0].upper() == 'Q':
            for suffix in ['_c', '_b', '_e']:
                key = f"{comp[0]}{suffix}"
                if key in currents_dc and key in dc_currents_num:
                    consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                        currents_dc[key], dc_currents_num[key], values
                    )
                    if not consistent:
                        print(f"❌ INCOHÉRENCE I{key}_DC:")
                        print(f"   Symbolique: {currents_dc[key]}")
                        print(f"   Substitution manuelle: {manual}")
                        print(f"   Substitution automatique: {numeric}")
                        print(f"   Erreur: {error}")
                        dc_inconsistencies += 1
                    else:
                        print(f"✅ I{key}_DC cohérent")
    
    print(f"\nRésultat DC: {dc_inconsistencies} incohérences trouvées")
    
    print("\n=== Vérification de cohérence AC ===")
    ac_inconsistencies = 0
    for node in original_nodes:
        if node in voltages_ac and node in ac_voltages_num:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_ac[node], ac_voltages_num[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_AC:")
                print(f"   Symbolique: {voltages_ac[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                ac_inconsistencies += 1
            else:
                print(f"✅ V{node}_AC cohérent")
    
    for comp in netlist:
        if comp[0][0].upper() == 'Q':
            for suffix in ['_c', '_b', '_e']:
                key = f"{comp[0]}{suffix}"
                if key in currents_ac and key in ac_currents_num:
                    consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                        currents_ac[key], ac_currents_num[key], values
                    )
                    if not consistent:
                        print(f"❌ INCOHÉRENCE I{key}_AC:")
                        print(f"   Symbolique: {currents_ac[key]}")
                        print(f"   Substitution manuelle: {manual}")
                        print(f"   Substitution automatique: {numeric}")
                        print(f"   Erreur: {error}")
                        ac_inconsistencies += 1
                    else:
                        print(f"✅ I{key}_AC cohérent")
    
    print(f"\nRésultat AC: {ac_inconsistencies} incohérences trouvées")
    
    return dc_inconsistencies == 0 and ac_inconsistencies == 0

def test_simple_opamp_circuit():
    """Test avec un circuit simple contenant un amplificateur opérationnel"""
    print("\n=== Test Circuit OpAmp Simple ===")
    
    netlist_str = """
V1 1 0 DC 0 AC 1
R1 1 2 1k
X1 0 2 3 100k 1G 100G
R2 3 0 2k
"""
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    original_nodes = sorted(list(set(comp[i] for comp in original_netlist for i in [1,2]) - {'0'}))
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse DC
    print("Analyse DC...")
    voltages_dc, currents_dc = dc_analysis(netlist, values, all_unique_nodes)
    dc_voltages_num = substitute_values(voltages_dc, values)
    dc_currents_num = substitute_values(currents_dc, values)
    
    # Analyse AC
    print("Analyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    ac_voltages_num = substitute_values(voltages_ac, values)
    ac_currents_num = substitute_values(currents_ac, values)
    
    # Vérification de cohérence
    print("\n=== Vérification de cohérence DC ===")
    dc_inconsistencies = 0
    for node in original_nodes:
        if node in voltages_dc and node in dc_voltages_num:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_dc[node], dc_voltages_num[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_DC:")
                print(f"   Symbolique: {voltages_dc[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                dc_inconsistencies += 1
            else:
                print(f"✅ V{node}_DC cohérent")
    
    print(f"\nRésultat DC: {dc_inconsistencies} incohérences trouvées")
    
    print("\n=== Vérification de cohérence AC ===")
    ac_inconsistencies = 0
    for node in original_nodes:
        if node in voltages_ac and node in ac_voltages_num:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_ac[node], ac_voltages_num[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_AC:")
                print(f"   Symbolique: {voltages_ac[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                ac_inconsistencies += 1
            else:
                print(f"✅ V{node}_AC cohérent")
    
    print(f"\nRésultat AC: {ac_inconsistencies} incohérences trouvées")
    
    return dc_inconsistencies == 0 and ac_inconsistencies == 0

if __name__ == "__main__":
    print("Test de cohérence entre expressions symboliques et numériques")
    print("=" * 60)
    
    bjt_success = test_simple_bjt_circuit()
    opamp_success = test_simple_opamp_circuit()
    
    print("\n" + "=" * 60)
    print("RÉSUMÉ DES TESTS:")
    print(f"Circuit BJT: {'✅ SUCCÈS' if bjt_success else '❌ ÉCHEC'}")
    print(f"Circuit OpAmp: {'✅ SUCCÈS' if opamp_success else '❌ ÉCHEC'}")
    
    if bjt_success and opamp_success:
        print("\n🎉 Tous les tests sont passés avec succès!")
    else:
        print("\n⚠️  Des incohérences ont été détectées. Correction nécessaire.")
