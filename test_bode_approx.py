#!/usr/bin/env python3
"""
Test du bouton approx dans bode_H1_final.py
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Importer et lancer l'application
if __name__ == "__main__":
    # Vérifier que le fichier FTC.txt existe
    if not os.path.exists("FTC.txt"):
        print("Erreur : Le fichier FTC.txt n'existe pas dans le répertoire courant.")
        print("Veuillez vous assurer que FTC.txt est présent.")
        sys.exit(1)
    
    print("Lancement de l'analyseur de Bode avec fonction de simplification...")
    print("Instructions :")
    print("1. Configurez les paramètres dans la boîte de dialogue")
    print("2. Une fois l'interface ouverte, cliquez sur le bouton 'Approx' pour simplifier")
    print("3. <PERSON><PERSON><PERSON>z un seuil d'erreur (recommandé : 2.0 à 5.0 dB)")
    print("4. Utilisez 'Reset' pour revenir à l'expression originale")
    print()
    
    # Importer et lancer l'application principale
    from bode_H1_final import main
    main()
