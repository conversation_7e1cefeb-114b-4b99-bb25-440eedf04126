#!/usr/bin/env python3
"""
Script de test pour la nouvelle fonctionnalité d'approximation intelligente
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_expressions():
    """Crée des expressions de test optimales pour l'approximation intelligente"""
    
    expressions = {
        "Termes similaires (ordre 1)": 
        "R1*C1*s + R2*C2*s + R3*C3*s + 1",
        
        "Termes factorisables": 
        "(R1*R2*C1*s + R1*R2*C2*s + R1*R3*C1*s + 1)/(R1*R2*C1*C2*s^2 + (R1*C1 + R2*C2)*s + 1)",
        
        "Polynôme complexe avec ordres multiples":
        "(R1*C1*s^2 + R2*C2*s^2 + R3*C3*s + R4*C4*s + R5*C5 + R6*C6)/(L1*C1*s^2 + L2*C2*s^2 + R7*s + R8*s + 1)",
        
        "Expression du FTC.txt (très complexe)":
        "gm_Q1*R2*R3/(C1*C2*R1*R2*R3*s^2 + C1*R1*R2*s + C1*R1*R3*s + C2*R2*R3*s + R1 + R2 + R3)"
    }
    
    return expressions

def create_test_files():
    """Crée des fichiers de test pour chaque expression"""
    
    expressions = create_test_expressions()
    
    for name, expr in expressions.items():
        filename = f"FTC_approx_{name.replace(' ', '_').replace('(', '').replace(')', '').replace('/', '_')}.txt"
        with open(filename, 'w') as f:
            f.write(expr)
        print(f"✅ Créé {filename}")

def analyze_approximation_potential():
    """Analyse le potentiel d'approximation des expressions de test"""
    
    expressions = create_test_expressions()
    
    print("\n" + "="*80)
    print("🧠 ANALYSE DU POTENTIEL D'APPROXIMATION INTELLIGENTE")
    print("="*80)
    
    for name, expr in expressions.items():
        print(f"\n📊 {name}:")
        print(f"  Expression: {expr}")
        
        # Analyser la structure
        plus_count = expr.count('+')
        terms_approx = plus_count + 1
        print(f"  Termes approximatifs: {terms_approx}")
        
        # Analyser les ordres de s
        s_orders = []
        if 's^2' in expr:
            s_orders.append(2)
        if 's' in expr and 's^' not in expr.replace('s^2', ''):
            s_orders.append(1)
        if any(char.isalpha() and char != 's' for char in expr):
            s_orders.append(0)
        
        print(f"  Ordres de s détectés: {s_orders}")
        
        # Potentiel d'approximation
        if len(s_orders) > 1 and terms_approx > 3:
            potential = "🟢 ÉLEVÉ"
            reason = "Plusieurs ordres avec plusieurs termes"
        elif terms_approx > 5:
            potential = "🟡 MOYEN"
            reason = "Beaucoup de termes, structure à analyser"
        elif len(s_orders) > 1:
            potential = "🟡 MOYEN"
            reason = "Plusieurs ordres mais peu de termes"
        else:
            potential = "🔴 FAIBLE"
            reason = "Structure simple ou peu de termes"
        
        print(f"  Potentiel d'approximation: {potential}")
        print(f"  Raison: {reason}")

def demonstrate_approximation_strategies():
    """Démontre les différentes stratégies d'approximation"""
    
    print("\n" + "="*80)
    print("🔧 STRATÉGIES D'APPROXIMATION INTELLIGENTE")
    print("="*80)
    
    strategies = [
        ("🎯 Approximation par ordre de s", [
            "Grouper les termes par puissance de s",
            "Créer un terme représentatif pour chaque ordre",
            "Exemple: R1*C1*s + R2*C2*s → R_eq*C_eq*s"
        ]),
        
        ("🔗 Factorisation commune", [
            "Utiliser sp.factor() pour trouver des facteurs communs",
            "Accepter seulement si réduction ≥ 20%",
            "Exemple: R1*R2*C1*s + R1*R2*C2*s → R1*R2*(C1+C2)*s"
        ]),
        
        ("📊 Coefficient représentatif", [
            "Priorité 1: Constantes numériques (la plus grande)",
            "Priorité 2: Variables communes (le plus complet)",
            "Priorité 3: Expression la plus simple"
        ]),
        
        ("🔬 Validation multi-plages", [
            "Test sur 5 variations: 0.5×, 0.75×, 1×, 1.25×, 2×",
            "Critère: erreur ≤ 150% du seuil utilisateur",
            "Robustesse sur toute la plage des sliders"
        ])
    ]
    
    for strategy_name, details in strategies:
        print(f"\n{strategy_name}:")
        for detail in details:
            print(f"  • {detail}")

def usage_instructions():
    """Instructions d'utilisation de la nouvelle fonctionnalité"""
    
    print("\n" + "="*80)
    print("📖 INSTRUCTIONS D'UTILISATION")
    print("="*80)
    
    print("\n🚀 Pour tester l'approximation intelligente:")
    print("1. Lancez l'application: python bode_H1_final.py")
    print("2. Utilisez un des fichiers FTC_approx_*.txt créés")
    print("3. Configurez les valeurs des composants")
    print("4. Cliquez sur 'Analyse de sensibilité'")
    print("5. Cliquez sur 'Simplifier la fonction de transfert'")
    print("6. Observez les messages d'approximation intelligente")
    
    print("\n🔍 Messages à surveiller:")
    print("• '🧠 Tentative d'approximation intelligente...'")
    print("• '📐 Ordres détectés: [0, 1, 2]'")
    print("• '✅ Ordre s^1: 3 termes → 1 terme (2 termes supprimés)'")
    print("• '🔬 Validation sur plages de composants...'")
    print("• '✅ Approximation validée'")
    print("• '🎯 Approximation intelligente réussie'")
    
    print("\n⚠️ Si l'approximation intelligente échoue:")
    print("• Message: '🔄 Approximation intelligente non applicable'")
    print("• L'algorithme utilise automatiquement la méthode classique")
    print("• Aucune dégradation par rapport à l'ancien comportement")
    
    print("\n🎮 Test de robustesse:")
    print("1. Après simplification, bougez les sliders")
    print("2. Observez que la courbe simplifiée reste cohérente")
    print("3. Comparez avec l'ajustement manuel si nécessaire")

def expected_improvements():
    """Améliorations attendues avec l'approximation intelligente"""
    
    print("\n" + "="*80)
    print("📈 AMÉLIORATIONS ATTENDUES")
    print("="*80)
    
    improvements = [
        ("🎯 Précision sur toute la plage", [
            "Validation sur ±50% et ×2 des valeurs nominales",
            "Approximations qui restent valides quand les sliders bougent",
            "Cohérence physique préservée"
        ]),
        
        ("🧠 Intelligence adaptative", [
            "Analyse structurelle de l'expression",
            "Factorisation automatique quand possible",
            "Choix du coefficient représentatif optimal"
        ]),
        
        ("⚡ Performance optimisée", [
            "Fallback intelligent vers la méthode classique",
            "Validation rapide sur 20 points de fréquence",
            "Critères stricts pour rejeter les approximations douteuses"
        ]),
        
        ("🎮 Expérience utilisateur", [
            "Messages détaillés sur le processus d'approximation",
            "Transparence complète sur les décisions prises",
            "Confiance dans les résultats de simplification"
        ])
    ]
    
    for category, items in improvements:
        print(f"\n{category}:")
        for item in items:
            print(f"  ✓ {item}")

def comparison_old_vs_new():
    """Comparaison entre l'ancienne et la nouvelle approche"""
    
    print("\n" + "="*80)
    print("⚖️ COMPARAISON ANCIENNE vs NOUVELLE APPROCHE")
    print("="*80)
    
    print("\n🔴 ANCIENNE APPROCHE (Suppression):")
    print("• Supprime les termes les moins importants")
    print("• Peut devenir imprécis quand les composants varient")
    print("• Pas de validation sur les plages de composants")
    print("• Exemple: R1*C1*s + R2*C2*s + 1 → R1*C1*s + 1")
    print("• Problème: Imprécis si R2*C2 devient important")
    
    print("\n🟢 NOUVELLE APPROCHE (Approximation):")
    print("• Approxime les termes similaires intelligemment")
    print("• Reste valide sur toute la plage des composants")
    print("• Validation multi-plages automatique")
    print("• Exemple: R1*C1*s + R2*C2*s + 1 → (R1*C1+R2*C2)*s + 1")
    print("• Avantage: Précis même si R2*C2 varie")
    
    print("\n📊 RÉSULTATS ATTENDUS:")
    print("• Même niveau de réduction (30-60%)")
    print("• Précision supérieure sur les plages")
    print("• Robustesse aux variations de sliders")
    print("• Confiance utilisateur améliorée")

def main():
    """Fonction principale de test"""
    
    print("🧪 TEST DE L'APPROXIMATION INTELLIGENTE")
    print("="*60)
    
    # Créer les fichiers de test
    print("\n📁 Création des fichiers de test...")
    create_test_files()
    
    # Analyser le potentiel d'approximation
    analyze_approximation_potential()
    
    # Démontrer les stratégies
    demonstrate_approximation_strategies()
    
    # Instructions d'utilisation
    usage_instructions()
    
    # Améliorations attendues
    expected_improvements()
    
    # Comparaison
    comparison_old_vs_new()
    
    print("\n" + "="*80)
    print("✅ SCRIPT DE TEST TERMINÉ")
    print("="*80)
    print("\n🎯 Prochaines étapes:")
    print("1. Testez avec les fichiers FTC_approx_*.txt créés")
    print("2. Observez les messages d'approximation intelligente")
    print("3. Vérifiez la robustesse en bougeant les sliders")
    print("4. Comparez avec l'ajustement manuel si nécessaire")
    print("\n🚀 L'approximation intelligente devrait donner des résultats")
    print("   plus robustes et plus précis sur toute la plage des sliders!")

if __name__ == "__main__":
    main()
