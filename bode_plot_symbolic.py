#!/usr/bin/env python3
"""
Script pour tracer le diagramme de Bode de la fonction de transfert symbolique
H(s) = V3/V1 avec les valeurs du circuit spécifiées
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp

def parse_symbolic_transfer_function():
    """Parse et évalue la fonction de transfert symbolique avec les valeurs du circuit"""
    
    # Valeurs du circuit
    values = {
        'Av_X1': 100000,      # 100k
        'fc_cut1_X1': 1e6,    # 1M Hz
        'fc_cut2_X1': 1e9,    # 1G Hz  
        'R1': 1000,           # 1k Ohm
        'R2': 2000,           # 2k Ohm
        'pi': np.pi
    }
    
    print("=== Valeurs du circuit ===")
    print("V1 1 0 DC 1 AC 1")
    print("R1 1 2 1k")
    print("X1 2 0 3 100k 1M 1G")
    print("R2 2 3 2k")
    print()
    print(f"Av_X1 = {values['Av_X1']:,.0f}")
    print(f"fc_cut1_X1 = {values['fc_cut1_X1']/1e6:.0f} MHz")
    print(f"fc_cut2_X1 = {values['fc_cut2_X1']/1e9:.0f} GHz")
    print(f"R1 = {values['R1']:,.0f} Ω")
    print(f"R2 = {values['R2']:,.0f} Ω")
    
    # Fonction de transfert symbolique
    s = sp.Symbol('s')
    
    # Numérateur: -16*Av_X1*fc_cut1_X1*fc_cut2_X1*fc_cut2_X1*fc_cut1_X1*R2*pi^4
    num_symbolic = -16 * values['Av_X1'] * values['fc_cut1_X1'] * values['fc_cut2_X1']**2 * values['fc_cut1_X1'] * values['R2'] * values['pi']**4
    
    # Dénominateur (expression complète développée)
    den_symbolic = (
        16*values['Av_X1']*values['fc_cut1_X1']*values['fc_cut2_X1']**2*values['fc_cut1_X1']*values['R1']*values['pi']**4 +
        16*values['fc_cut1_X1']*values['fc_cut2_X1']**2*values['fc_cut1_X1']*values['R1']*values['pi']**4 +
        16*values['fc_cut1_X1']*values['fc_cut2_X1']**2*values['fc_cut1_X1']*values['R2']*values['pi']**4 +
        8*values['fc_cut1_X1']*values['fc_cut2_X1']**2*values['R1']*values['pi']**3*s +
        8*values['fc_cut1_X1']*values['fc_cut2_X1']**2*values['R2']*values['pi']**3*s +
        8*values['fc_cut1_X1']*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R1']*values['pi']**3*s +
        8*values['fc_cut1_X1']*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R2']*values['pi']**3*s +
        4*values['fc_cut1_X1']*values['fc_cut2_X1']*values['R1']*values['pi']**2*s**2 +
        4*values['fc_cut1_X1']*values['fc_cut2_X1']*values['R2']*values['pi']**2*s**2 +
        8*values['fc_cut1_X1']*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R1']*values['pi']**3*s +
        8*values['fc_cut1_X1']*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R2']*values['pi']**3*s +
        4*values['fc_cut1_X1']*values['fc_cut2_X1']*values['R1']*values['pi']**2*s**2 +
        4*values['fc_cut1_X1']*values['fc_cut2_X1']*values['R2']*values['pi']**2*s**2 +
        4*values['fc_cut1_X1']**2*values['R1']*values['pi']**2*s**2 +
        4*values['fc_cut1_X1']**2*values['R2']*values['pi']**2*s**2 +
        2*values['fc_cut1_X1']*values['R1']*values['pi']*s**3 +
        2*values['fc_cut1_X1']*values['R2']*values['pi']*s**3 +
        8*values['fc_cut2_X1']**2*values['fc_cut1_X1']*values['R1']*values['pi']**3*s +
        8*values['fc_cut2_X1']**2*values['fc_cut1_X1']*values['R2']*values['pi']**3*s +
        4*values['fc_cut2_X1']**2*values['R1']*values['pi']**2*s**2 +
        4*values['fc_cut2_X1']**2*values['R2']*values['pi']**2*s**2 +
        4*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R1']*values['pi']**2*s**2 +
        4*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R2']*values['pi']**2*s**2 +
        2*values['fc_cut2_X1']*values['R1']*values['pi']*s**3 +
        2*values['fc_cut2_X1']*values['R2']*values['pi']*s**3 +
        4*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R1']*values['pi']**2*s**2 +
        4*values['fc_cut2_X1']*values['fc_cut1_X1']*values['R2']*values['pi']**2*s**2 +
        2*values['fc_cut2_X1']*values['R1']*values['pi']*s**3 +
        2*values['fc_cut2_X1']*values['R2']*values['pi']*s**3 +
        2*values['fc_cut1_X1']*values['R1']*values['pi']*s**3 +
        2*values['fc_cut1_X1']*values['R2']*values['pi']*s**3 +
        values['R1']*s**4 +
        values['R2']*s**4
    )
    
    print(f"\n=== Fonction de transfert ===")
    print(f"Numérateur = {num_symbolic:.2e}")
    print(f"H(s) = {num_symbolic:.2e} / (polynôme de degré 4)")
    
    return num_symbolic, den_symbolic, values, s

def extract_polynomial_coefficients(den_expr, s_symbol):
    """Extrait les coefficients du polynôme du dénominateur"""
    
    # Développer l'expression
    expanded = sp.expand(den_expr)
    
    # Extraire les coefficients (ordre décroissant: s^4, s^3, s^2, s^1, s^0)
    coeffs = []
    for power in [4, 3, 2, 1, 0]:
        coeff = expanded.coeff(s_symbol, power)
        if coeff is None:
            coeff = 0
        coeffs.append(float(coeff))
    
    print(f"\n=== Coefficients du dénominateur ===")
    for i, coeff in enumerate(coeffs):
        power = 4 - i
        if power > 0:
            print(f"s^{power}: {coeff:.2e}")
        else:
            print(f"s^0 (constant): {coeff:.2e}")
    
    return coeffs

def create_scipy_transfer_function(num, den_coeffs):
    """Crée une fonction de transfert scipy à partir des coefficients"""
    
    # Pour scipy.signal, on a besoin des coefficients du polynôme
    # Le numérateur est une constante
    num_coeffs = [num]
    
    # Créer la fonction de transfert
    tf = signal.TransferFunction(num_coeffs, den_coeffs)
    
    return tf

def plot_bode_diagram(tf, values):
    """Trace le diagramme de Bode"""
    
    # Gamme de fréquences (1 Hz à 1 THz)
    frequencies = np.logspace(0, 12, 10000)  # 1 Hz à 1 THz
    omega = 2 * np.pi * frequencies
    
    print(f"\n=== Calcul de la réponse fréquentielle ===")
    print("Calcul en cours...")
    
    # Calcul de la réponse fréquentielle
    w, h = signal.freqresp(tf, omega)
    
    # Conversion en dB et phase en degrés
    magnitude_db = 20 * np.log10(np.abs(h))
    phase_deg = np.angle(h) * 180 / np.pi
    
    print("Calcul terminé.")
    
    # Création du graphique
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # Graphique de magnitude
    ax1.semilogx(frequencies, magnitude_db, 'b-', linewidth=2)
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - H(s) = V3/V1 (Fonction symbolique)')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(1, 1e12)
    
    # Marquer les fréquences importantes
    fc1 = values['fc_cut1_X1']
    fc2 = values['fc_cut2_X1']
    
    # Trouver les valeurs de gain aux fréquences de coupure
    idx_fc1 = np.argmin(np.abs(frequencies - fc1))
    idx_fc2 = np.argmin(np.abs(frequencies - fc2))
    
    gain_fc1 = magnitude_db[idx_fc1]
    gain_fc2 = magnitude_db[idx_fc2]
    
    ax1.axvline(fc1, color='r', linestyle='--', alpha=0.7, label=f'fc_cut1 = {fc1/1e6:.0f} MHz')
    ax1.axvline(fc2, color='g', linestyle='--', alpha=0.7, label=f'fc_cut2 = {fc2/1e9:.0f} GHz')
    ax1.plot(fc1, gain_fc1, 'ro', markersize=8, label=f'Gain à fc1 = {gain_fc1:.1f} dB')
    ax1.plot(fc2, gain_fc2, 'go', markersize=8, label=f'Gain à fc2 = {gain_fc2:.1f} dB')
    ax1.legend()
    
    # Graphique de phase
    ax2.semilogx(frequencies, phase_deg, 'r-', linewidth=2)
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(1, 1e12)
    ax2.axvline(fc1, color='r', linestyle='--', alpha=0.7)
    ax2.axvline(fc2, color='g', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('bode_diagram_symbolic.png', dpi=300, bbox_inches='tight')
    print("Graphique sauvegardé sous 'bode_diagram_symbolic.png'")
    # plt.show()  # Commenté pour éviter l'affichage interactif
    
    # Affichage des informations importantes
    print(f"\n=== Analyse du diagramme de Bode ===")
    print(f"Gain DC: {magnitude_db[0]:.1f} dB")
    print(f"Gain à fc_cut1 ({fc1/1e6:.0f} MHz): {gain_fc1:.1f} dB")
    print(f"Gain à fc_cut2 ({fc2/1e9:.0f} GHz): {gain_fc2:.1f} dB")
    
    # Recherche des coupures à -3dB
    gain_dc = magnitude_db[0]
    cutoff_level = gain_dc - 3
    
    cutoff_freqs = []
    for i in range(1, len(magnitude_db)):
        if magnitude_db[i] <= cutoff_level and magnitude_db[i-1] > cutoff_level:
            # Interpolation linéaire pour plus de précision
            f_cutoff = frequencies[i-1] + (frequencies[i] - frequencies[i-1]) * \
                      (cutoff_level - magnitude_db[i-1]) / (magnitude_db[i] - magnitude_db[i-1])
            cutoff_freqs.append(f_cutoff)
    
    print(f"\nFréquences de coupure (-3dB) détectées:")
    for i, fc in enumerate(cutoff_freqs):
        if fc < 1e6:
            print(f"  Coupure {i+1}: {fc/1e3:.1f} kHz")
        elif fc < 1e9:
            print(f"  Coupure {i+1}: {fc/1e6:.1f} MHz")
        else:
            print(f"  Coupure {i+1}: {fc/1e9:.2f} GHz")

def main():
    """Fonction principale"""
    
    print("=== Analyse de la fonction de transfert symbolique H(s) = V3/V1 ===")
    
    # Parse la fonction de transfert
    num, den, values, s = parse_symbolic_transfer_function()
    
    # Extraire les coefficients du polynôme
    den_coeffs = extract_polynomial_coefficients(den, s)
    
    # Créer la fonction de transfert scipy
    tf = create_scipy_transfer_function(num, den_coeffs)
    
    print(f"\n=== Fonction de transfert scipy ===")
    print(f"Numérateur: {tf.num}")
    print(f"Dénominateur: {tf.den}")
    
    # Tracer le diagramme de Bode
    plot_bode_diagram(tf, values)
    
    print(f"\n=== Fichier sauvegardé ===")
    print("Le diagramme de Bode a été sauvegardé sous 'bode_diagram_symbolic.png'")

if __name__ == "__main__":
    main()
