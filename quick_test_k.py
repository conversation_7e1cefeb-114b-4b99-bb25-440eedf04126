#!/usr/bin/env python3
"""
Test rapide de la substitution de k
"""
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    # Circuit très simple
    netlist_str = """V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n"""
    
    print("=== Test rapide substitution k ===")
    print("Circuit:", netlist_str.replace('\n', ' | '))
    
    solve_circuit(netlist_str, 1000.0, True, "1", "2", 1.0, 1e6, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {e}")
    import traceback
    traceback.print_exc()
