#!/usr/bin/env python3

# Test de l'amplificateur opérationnel avec réponse fréquentielle

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

# Test 1: Amplificateur opérationnel avec fréquences de coupure personnalisées
print("=== Test 1: Amplificateur opérationnel avec fc1=100Hz, fc2=10kHz ===")
netlist_custom_freq = """V1 1 0 DC 0 AC 1
R1 1 2 1k
OP1 0 2 3 1000000 100 10000
R2 3 4 1k
R3 4 0 1k"""

print("Netlist:")
print(netlist_custom_freq)
print("\nTest avec fonction de transfert de 1Hz à 1MHz:")

try:
    solve_circuit(netlist_custom_freq, 
                 frequency_hz=1000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=1.0, 
                 freq_max=1000000.0, 
                 laplace_domain=True)
    print("Test 1 réussi!")
except Exception as e:
    print(f"Erreur lors du test 1: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*70 + "\n")

# Test 2: Amplificateur opérationnel avec valeurs par défaut
print("=== Test 2: Amplificateur opérationnel avec valeurs par défaut (fc1=10Hz, fc2=1MHz) ===")
netlist_default = """V1 1 0 DC 0 AC 1
R1 1 2 1k
OP1 0 2 3 100000
R2 3 4 1k
R3 4 0 1k"""

print("Netlist:")
print(netlist_default)
print("\nTest avec fonction de transfert de 0.1Hz à 10MHz:")

try:
    solve_circuit(netlist_default, 
                 frequency_hz=1000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=0.1, 
                 freq_max=10000000.0, 
                 laplace_domain=True)
    print("Test 2 réussi!")
except Exception as e:
    print(f"Erreur lors du test 2: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*70 + "\n")

# Test 3: Comparaison avec amplificateur idéal (gain très élevé, fréquences très hautes)
print("=== Test 3: Amplificateur quasi-idéal (fc1=1MHz, fc2=100MHz) ===")
netlist_quasi_ideal = """V1 1 0 DC 0 AC 1
R1 1 2 1k
OP1 0 2 3 1000000 1000000 100000000
R2 3 4 1k
R3 4 0 1k"""

print("Netlist:")
print(netlist_quasi_ideal)
print("\nTest avec fonction de transfert de 1Hz à 1GHz:")

try:
    solve_circuit(netlist_quasi_ideal, 
                 frequency_hz=1000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=1.0, 
                 freq_max=1000000000.0, 
                 laplace_domain=True)
    print("Test 3 réussi!")
except Exception as e:
    print(f"Erreur lors du test 3: {e}")
    import traceback
    traceback.print_exc()
