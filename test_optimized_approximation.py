#!/usr/bin/env python3
"""
Méthode d'approximation par optimisation numérique
Objectif: erreur < 5 dB
"""

import numpy as np
import sympy
from scipy.optimize import minimize, differential_evolution
import time

def read_transfer_function(filename):
    """Lit la fonction de transfert depuis un fichier"""
    try:
        with open(filename, 'r') as f:
            content = f.read().strip()
        return content.replace('^', '**')
    except FileNotFoundError:
        print(f"Fichier {filename} non trouvé")
        return None

def extract_variables_from_expression(expr_str):
    """Extrait les variables d'une expression"""
    import re
    variables = set()
    pattern = r'\b([A-Za-z][A-Za-z0-9_]*)\b'
    matches = re.findall(pattern, expr_str)
    
    for match in matches:
        if match not in ['s', 'j', 'pi', 'e', 'I', 'E', 'sin', 'cos', 'tan', 'log', 'exp']:
            variables.add(match)
    
    return sorted(list(variables))

def evaluate_transfer_function_fast(expr_str, variables, values, frequencies):
    """Évalue rapidement la fonction de transfert"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s
        
        expr = sympy.sympify(expr_str, locals=symbols_dict)
        
        # Créer une fonction rapide
        func_args = [s] + [symbols_dict[var] for var in variables]
        fast_func = sympy.lambdify(func_args, expr, 'numpy')
        
        responses = []
        for freq in frequencies:
            s_val = 2j * np.pi * freq
            param_values = [values[var] for var in variables]
            
            try:
                response = fast_func(s_val, *param_values)
                responses.append(complex(response))
            except:
                responses.append(1e-12 + 0j)
        
        return np.array(responses)
        
    except Exception as e:
        print(f"Erreur évaluation: {e}")
        return np.ones(len(frequencies), dtype=complex) * 1e-12

def create_template_approximations(target_terms):
    """Crée des templates d'approximation de complexité croissante"""
    s = sympy.Symbol('s')
    
    templates = []
    
    if target_terms <= 3:
        # Forme très simple: K/(1 + s*tau)
        templates.append("a0 / (b0 + b1*s)")
        templates.append("a0 / (b0 + b1*s + b2*s**2)")
        
    elif target_terms <= 5:
        # Forme simple avec zéro: (a0 + a1*s) / (b0 + b1*s + b2*s**2)
        templates.append("(a0 + a1*s) / (b0 + b1*s)")
        templates.append("(a0 + a1*s) / (b0 + b1*s + b2*s**2)")
        templates.append("a0 / (b0 + b1*s + b2*s**2 + b3*s**3)")
        
    elif target_terms <= 8:
        # Forme intermédiaire
        templates.append("(a0 + a1*s + a2*s**2) / (b0 + b1*s + b2*s**2)")
        templates.append("(a0 + a1*s) / (b0 + b1*s + b2*s**2 + b3*s**3)")
        templates.append("(a0 + a1*s + a2*s**2) / (b0 + b1*s + b2*s**2 + b3*s**3)")
        
    else:
        # Forme complexe
        templates.append("(a0 + a1*s + a2*s**2 + a3*s**3) / (b0 + b1*s + b2*s**2 + b3*s**3)")
        templates.append("(a0 + a1*s + a2*s**2) / (b0 + b1*s + b2*s**2 + b3*s**3 + b4*s**4)")
        templates.append("(a0 + a1*s + a2*s**2 + a3*s**3) / (b0 + b1*s + b2*s**2 + b3*s**3 + b4*s**4)")
    
    return templates

def fit_template_to_data(template, original_responses, frequencies, variables, test_values):
    """Ajuste un template aux données par optimisation"""
    try:
        # Extraire les paramètres du template
        import re
        params = set()
        for match in re.findall(r'[ab]\d+', template):
            params.add(match)
        params = sorted(list(params))
        
        if not params:
            return None, float('inf')
        
        print(f"  Template: {template}")
        print(f"  Paramètres: {params}")
        
        # Fonction objectif
        def objective(param_values):
            try:
                # Créer le dictionnaire de substitution
                param_dict = {param: val for param, val in zip(params, param_values)}
                
                # Évaluer le template
                template_responses = []
                for freq in frequencies:
                    s_val = 2j * np.pi * freq
                    
                    # Remplacer s et les paramètres
                    expr_str = template
                    expr_str = expr_str.replace('s', f'({s_val})')
                    
                    for param, val in param_dict.items():
                        expr_str = expr_str.replace(param, str(val))
                    
                    try:
                        result = eval(expr_str)
                        template_responses.append(complex(result))
                    except:
                        return 1e6  # Pénalité énorme si évaluation échoue
                
                template_responses = np.array(template_responses)
                
                # Calculer l'erreur en dB
                orig_mag = np.abs(original_responses)
                temp_mag = np.abs(template_responses)
                
                # Éviter les divisions par zéro
                orig_mag = np.where(orig_mag > 1e-15, orig_mag, 1e-15)
                temp_mag = np.where(temp_mag > 1e-15, temp_mag, 1e-15)
                
                # Erreur RMS en dB
                orig_db = 20 * np.log10(orig_mag)
                temp_db = 20 * np.log10(temp_mag)
                
                error = np.sqrt(np.mean((orig_db - temp_db)**2))
                
                return error
                
            except Exception as e:
                return 1e6  # Pénalité si erreur
        
        # Optimisation avec bornes raisonnables
        bounds = []
        for param in params:
            if param.startswith('a'):
                bounds.append((-1e6, 1e6))  # Coefficients numérateur
            else:  # param.startswith('b')
                if param == 'b0':
                    bounds.append((1e-12, 1e6))  # b0 ne peut pas être zéro
                else:
                    bounds.append((-1e6, 1e6))  # Autres coefficients dénominateur
        
        # Essayer plusieurs méthodes d'optimisation
        best_error = float('inf')
        best_params = None
        
        # Méthode 1: Differential Evolution (globale)
        try:
            result = differential_evolution(objective, bounds, maxiter=100, seed=42)
            if result.fun < best_error:
                best_error = result.fun
                best_params = result.x
        except:
            pass
        
        # Méthode 2: Optimisation locale avec plusieurs points de départ
        for _ in range(3):
            try:
                # Point de départ aléatoire
                x0 = []
                for bound in bounds:
                    if bound[0] > 0:  # Pour b0
                        x0.append(np.random.uniform(bound[0], min(bound[1], 1000)))
                    else:
                        x0.append(np.random.uniform(max(bound[0], -1000), min(bound[1], 1000)))
                
                result = minimize(objective, x0, bounds=bounds, method='L-BFGS-B')
                if result.fun < best_error:
                    best_error = result.fun
                    best_params = result.x
            except:
                continue
        
        if best_params is not None:
            # Créer l'expression finale
            param_dict = {param: val for param, val in zip(params, best_params)}
            final_expr = template
            for param, val in param_dict.items():
                final_expr = final_expr.replace(param, f"({val:.6e})")
            
            return final_expr, best_error
        else:
            return None, float('inf')
            
    except Exception as e:
        print(f"  Erreur fitting: {e}")
        return None, float('inf')

def optimized_approximation_method(original_expr, variables, target_reduction=0.6):
    """Méthode d'approximation par optimisation numérique"""
    try:
        print("=== Approximation par optimisation numérique ===")
        
        # Calculer le nombre de termes cible
        original_terms = len(original_expr.split('+')) + len(original_expr.split('-'))
        target_terms = max(3, int(original_terms * (1 - target_reduction)))
        
        print(f"Objectif: réduire de ~{original_terms} à {target_terms} termes")
        
        # Valeurs de test
        test_values = {
            'C1': 1e-7, 'C2': 1e-5, 'C3': 1e-7,
            'L1': 1e-3,
            'R1': 100, 'R2': 10000, 'R3': 10000, 'R4': 1000, 'R5': 1000
        }
        
        # Fréquences de test
        frequencies = np.logspace(0, 6, 100)  # 1 Hz à 1 MHz
        
        # Évaluer la fonction originale
        print("Évaluation de la fonction originale...")
        original_responses = evaluate_transfer_function_fast(
            original_expr, variables, test_values, frequencies
        )
        
        # Tester différents templates
        templates = create_template_approximations(target_terms)
        
        best_approximation = None
        best_error = float('inf')
        
        print(f"Test de {len(templates)} templates...")
        
        for i, template in enumerate(templates):
            print(f"\nTemplate {i+1}/{len(templates)}:")
            
            approx_expr, error = fit_template_to_data(
                template, original_responses, frequencies, variables, test_values
            )
            
            if approx_expr and error < best_error:
                best_error = error
                best_approximation = approx_expr
                print(f"  ✅ Nouvelle meilleure approximation! Erreur: {error:.2f} dB")
            else:
                print(f"  ❌ Erreur: {error:.2f} dB")
        
        if best_approximation and best_error < 5.0:
            print(f"\n🎯 SUCCÈS! Approximation trouvée avec erreur {best_error:.2f} dB < 5 dB")
            return best_approximation
        elif best_approximation:
            print(f"\n⚠️  Meilleure approximation: erreur {best_error:.2f} dB (> 5 dB)")
            return best_approximation
        else:
            print(f"\n❌ Aucune approximation satisfaisante trouvée")
            return original_expr
            
    except Exception as e:
        print(f"Erreur optimisation: {e}")
        import traceback
        traceback.print_exc()
        return original_expr

def main():
    print("=== Test d'approximation par optimisation (objectif < 5 dB) ===")
    
    # Lire la fonction de transfert
    transfer_function = read_transfer_function("FTC.txt")
    if not transfer_function:
        return
    
    print(f"Fonction originale: {transfer_function[:100]}...")
    
    # Extraire les variables
    variables = extract_variables_from_expression(transfer_function)
    print(f"Variables: {variables}")
    
    # Tester avec différents niveaux de réduction
    for reduction in [0.6, 0.7, 0.8]:
        print(f"\n{'='*60}")
        print(f"TEST AVEC {reduction*100:.0f}% DE RÉDUCTION")
        print(f"{'='*60}")
        
        start_time = time.time()
        approximation = optimized_approximation_method(transfer_function, variables, reduction)
        elapsed_time = time.time() - start_time
        
        print(f"\nRésultat final:")
        print(f"Temps: {elapsed_time:.1f}s")
        print(f"Expression: {approximation[:150]}...")
        
        # Test de validation
        test_values = {
            'C1': 1e-7, 'C2': 1e-5, 'C3': 1e-7,
            'L1': 1e-3,
            'R1': 100, 'R2': 10000, 'R3': 10000, 'R4': 1000, 'R5': 1000
        }
        
        frequencies = np.logspace(0, 6, 50)
        
        original_responses = evaluate_transfer_function_fast(
            transfer_function, variables, test_values, frequencies
        )
        approx_responses = evaluate_transfer_function_fast(
            approximation, variables, test_values, frequencies
        )
        
        # Calculer l'erreur finale
        orig_mag = np.abs(original_responses)
        approx_mag = np.abs(approx_responses)
        
        orig_mag = np.where(orig_mag > 1e-15, orig_mag, 1e-15)
        approx_mag = np.where(approx_mag > 1e-15, approx_mag, 1e-15)
        
        orig_db = 20 * np.log10(orig_mag)
        approx_db = 20 * np.log10(approx_mag)
        
        error_rms = np.sqrt(np.mean((orig_db - approx_db)**2))
        error_max = np.max(np.abs(orig_db - approx_db))
        
        print(f"Validation finale:")
        print(f"  Erreur RMS: {error_rms:.2f} dB")
        print(f"  Erreur Max: {error_max:.2f} dB")
        
        if error_rms < 5.0:
            print(f"  🎯 OBJECTIF ATTEINT! (< 5 dB)")
            break
        else:
            print(f"  ❌ Objectif non atteint (> 5 dB)")

if __name__ == "__main__":
    main()
