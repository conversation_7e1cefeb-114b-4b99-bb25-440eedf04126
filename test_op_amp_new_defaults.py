#!/usr/bin/env python3

# Test des nouvelles valeurs par défaut de l'amplificateur opérationnel

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

print("=== Test des nouvelles valeurs par défaut ===")
print("fc1 = 1MHz, fc2 = 10MHz")
print()

# Amplificateur opérationnel avec valeurs par défaut
netlist_default = """V1 1 0 DC 0 AC 1
R1 1 2 1k
OP1 0 2 3 1000000
R2 3 4 1k
R3 4 0 1k"""

print("Netlist avec amplificateur opérationnel (valeurs par défaut):")
print(netlist_default)
print()

try:
    solve_circuit(netlist_default, 
                 frequency_hz=1000000.0,  # Test à 1MHz (fc1)
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=1000.0,         # 1kHz à 100MHz
                 freq_max=100000000.0, 
                 laplace_domain=True)
    print("✓ Test réussi avec les nouvelles valeurs par défaut!")
    print()
    print("Vérifiez dans le fichier de résultats :")
    print("- La fonction de transfert doit contenir fc1_OP1 et fc2_OP1")
    print("- fc1_OP1 = 1000000 (1MHz)")
    print("- fc2_OP1 = 10000000 (10MHz)")
    print("- Le gain doit être constant jusqu'à 1MHz")
    print("- Puis décroître de -20dB/décade entre 1MHz et 10MHz")
    print("- Puis décroître de -40dB/décade au-delà de 10MHz")
    
except Exception as e:
    print(f"✗ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
