# Icône personnalisée pour l'application Bode

## Fonctionnalité implémentée

L'application peut maintenant afficher une **icône personnalisée** dans la barre des tâches et dans la barre de titre de la fenêtre.

## Comment ça fonctionne

### 1. Chargement automatique d'icônes

L'application essaie de charger une icône dans cet ordre de priorité :

1. **Fichiers icônes externes** (dans le dossier de l'application) :
   - `icon.ico`
   - `icon.png` 
   - `bode_icon.ico`
   - `bode_icon.png`
   - `app_icon.ico`

2. **Icône générée automatiquement** (si aucun fichier trouvé) :
   - Icône créée par le code représentant un diagramme de Bode stylisé

### 2. Fonctions ajoutées

#### `create_application_icon()`
```python
def create_application_icon():
    """Crée une icône pour l'application"""
    # Crée une icône 64x64 avec :
    # - Fond bleu avec bordure arrondie
    # - Courbes représentant magnitude et phase
    # - Axes de coordonnées
    # - Style diagramme de Bode
```

#### `set_application_icon(app, window=None)`
```python
def set_application_icon(app, window=None):
    """Définit l'icône de l'application"""
    # 1. Essaie de charger un fichier icône
    # 2. Si échec, utilise l'icône générée
    # 3. Applique à l'application ET à la fenêtre
```

### 3. Modifications dans main()

```python
def main():
    app = QApplication(sys.argv)
    
    # Configurer l'icône de l'application
    set_application_icon(app)
    
    # Définir le nom de l'application pour la barre des tâches
    app.setApplicationName("Analyseur de Bode")
    app.setApplicationDisplayName("Analyseur de Bode - Fonctions de Transfert")
    app.setApplicationVersion("1.0")
    
    # ... reste du code ...
    
    window = BodeAnalyzer(...)
    
    # Appliquer l'icône à la fenêtre principale aussi
    set_application_icon(app, window)
```

## Utilisation

### Option 1: Utiliser l'icône générée automatiquement

**Rien à faire !** L'application génère automatiquement une icône si aucun fichier n'est trouvé.

### Option 2: Utiliser une icône personnalisée

1. **Créer une icône avec le script fourni :**
   ```bash
   python create_icon.py
   ```
   
2. **Ou utiliser votre propre icône :**
   - Placez votre fichier icône dans le dossier de l'application
   - Nommez-le : `bode_icon.png`, `bode_icon.ico`, `icon.png`, ou `icon.ico`
   - Formats supportés : PNG, ICO, JPG, BMP

3. **L'application chargera automatiquement l'icône au démarrage**

## Script de création d'icône

Le fichier `create_icon.py` permet de créer des icônes personnalisées :

```python
# Crée des icônes de différentes tailles (16x16 à 256x256)
# Sauvegarde en PNG et ICO
# Style : diagramme de Bode avec courbes magnitude/phase
```

**Utilisation :**
```bash
python create_icon.py
```

**Fichiers créés :**
- `bode_icon.png` (icône principale)
- `bode_icon.ico` (format Windows)
- `bode_icon_16.png`, `bode_icon_32.png`, etc. (différentes tailles)

## Apparence de l'icône générée

L'icône automatique représente un **diagramme de Bode stylisé** :

- 🔵 **Fond bleu** avec bordure arrondie
- 📊 **Grille** de coordonnées
- 📈 **Courbe jaune** : magnitude (décroissante)
- 📉 **Courbe cyan** : phase (en S)
- ⚪ **Axes blancs** : horizontal et vertical
- 🔤 **Lettre "B"** : pour "Bode"

## Avantages

✅ **Identification facile** : L'application est reconnaissable dans la barre des tâches
✅ **Professionnel** : Apparence plus soignée
✅ **Automatique** : Fonctionne sans configuration
✅ **Personnalisable** : Possibilité d'utiliser sa propre icône
✅ **Multi-format** : Support PNG, ICO, etc.
✅ **Multi-taille** : Adaptation automatique selon le contexte

## Messages informatifs

L'application affiche dans la console :

- `"Icône chargée depuis: bode_icon.png"` (si fichier trouvé)
- `"Icône générée automatiquement (diagramme de Bode stylisé)"` (si générée)
- `"Erreur lors du chargement de l'icône: [erreur]"` (en cas de problème)

## Compatibilité

- ✅ **Windows** : ICO et PNG supportés
- ✅ **Linux** : PNG supporté
- ✅ **macOS** : PNG supporté
- ✅ **Barre des tâches** : Icône visible
- ✅ **Barre de titre** : Icône visible
- ✅ **Alt+Tab** : Icône visible (Windows)

## Personnalisation avancée

Pour créer votre propre icône :

1. **Taille recommandée** : 64x64 pixels minimum
2. **Formats supportés** : PNG (recommandé), ICO, JPG, BMP
3. **Nom de fichier** : `bode_icon.png` ou `icon.png`
4. **Emplacement** : Même dossier que `bode_H1_final.py`

L'application détectera et utilisera automatiquement votre icône personnalisée !
