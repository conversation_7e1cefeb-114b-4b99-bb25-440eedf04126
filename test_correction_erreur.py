#!/usr/bin/env python3
"""
Script de test pour vérifier que l'erreur de simplification est corrigée
"""

import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test d'importation du module"""
    try:
        import bode_H1_final
        print("✅ Import du module bode_H1_final réussi")
        return True
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def test_syntax():
    """Test de syntaxe Python"""
    try:
        import py_compile
        py_compile.compile('bode_H1_final.py', doraise=True)
        print("✅ Syntaxe Python valide")
        return True
    except Exception as e:
        print(f"❌ Erreur de syntaxe: {e}")
        return False

def create_test_expression():
    """Crée une expression de test simple"""
    test_expr = "1/(R1*C1*s + 1)"
    
    with open("FTC_test_simple.txt", "w") as f:
        f.write(test_expr)
    
    print(f"✅ Expression de test créée: {test_expr}")
    return test_expr

def test_class_structure():
    """Test de la structure des classes"""
    try:
        import bode_H1_final
        
        # Vérifier que les classes existent
        assert hasattr(bode_H1_final, 'SensitivityResultsDialog')
        assert hasattr(bode_H1_final, 'BodeAnalyzer')
        print("✅ Structure des classes correcte")
        
        # Vérifier que les méthodes clés existent
        dialog_class = bode_H1_final.SensitivityResultsDialog
        assert hasattr(dialog_class, 'simplify_symbolic')
        assert hasattr(dialog_class, 'simplify_polynomial_terms')
        assert hasattr(dialog_class, 'evaluate_term_impact')
        print("✅ Méthodes de simplification présentes")
        
        return True
    except Exception as e:
        print(f"❌ Erreur de structure: {e}")
        return False

def test_method_signatures():
    """Test des signatures de méthodes"""
    try:
        import bode_H1_final
        import inspect
        
        dialog_class = bode_H1_final.SensitivityResultsDialog
        
        # Vérifier la signature de simplify_symbolic
        sig = inspect.signature(dialog_class.simplify_symbolic)
        params = list(sig.parameters.keys())
        assert 'self' in params
        assert 'max_error_db' in params
        print("✅ Signature de simplify_symbolic correcte")
        
        # Vérifier la signature de simplify_polynomial_terms
        sig = inspect.signature(dialog_class.simplify_polynomial_terms)
        params = list(sig.parameters.keys())
        expected_params = ['self', 'poly_expr', 'test_frequencies', 'mag_ref', 'max_error_db', 'part_name']
        for param in expected_params:
            assert param in params, f"Paramètre manquant: {param}"
        print("✅ Signature de simplify_polynomial_terms correcte")
        
        return True
    except Exception as e:
        print(f"❌ Erreur de signature: {e}")
        return False

def main():
    """Fonction principale de test"""
    
    print("🧪 TEST DE CORRECTION DE L'ERREUR DE SIMPLIFICATION")
    print("="*60)
    
    tests = [
        ("Import du module", test_import),
        ("Syntaxe Python", test_syntax),
        ("Structure des classes", test_class_structure),
        ("Signatures des méthodes", test_method_signatures)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Exception dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Créer l'expression de test
    print(f"\n📁 Création du fichier de test")
    create_test_expression()
    
    # Résumé des résultats
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSÉ" if result else "❌ ÉCHOUÉ"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Résultat global: {passed}/{total} tests passés")
    
    if passed == total:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print("La correction de l'erreur semble réussie.")
        print("\nPour tester la simplification:")
        print("1. Lancez: python bode_H1_final.py")
        print("2. Utilisez le fichier FTC_test_simple.txt créé")
        print("3. Testez l'analyse de sensibilité et la simplification")
    else:
        print(f"\n⚠️ {total - passed} test(s) ont échoué.")
        print("Vérifiez les erreurs ci-dessus avant de continuer.")

if __name__ == "__main__":
    main()
