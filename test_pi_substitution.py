#!/usr/bin/env python3
"""
Test pour vérifier que la substitution de pi fonctionne correctement
"""

def test_pi_substitution():
    """Test de la substitution de pi dans les expressions d'amplificateur opérationnel"""
    
    print("=== Test de substitution de pi ===")
    
    # Expression typique générée par l'amplificateur opérationnel
    # AVANT la correction (avec valeur numérique de pi)
    old_expression = "(Av_X1) / (((1 + s / (2 * 3.14159265359 * Fc1_X1)) * (1 + s / (2 * 3.14159265359 * Fc2_X1)) * (1 + s / (2 * 3.14159265359 * Fc3_X1)) * (1 + s / (2 * 3.14159265359 * Fc4_X1))))"
    
    # APRÈS la correction (avec symbole pi)
    new_expression = "(Av_X1) / (((1 + s / (2 * pi * Fc1_X1)) * (1 + s / (2 * pi * Fc2_X1)) * (1 + s / (2 * pi * Fc3_X1)) * (1 + s / (2 * pi * Fc4_X1))))"
    
    print("Expression AVANT correction (avec valeur numérique de pi):")
    print(f"  {old_expression}")
    print()
    
    print("Expression APRÈS correction (avec symbole pi):")
    print(f"  {new_expression}")
    print()
    
    # Simulation de la substitution des valeurs
    values = {
        'Av_X1': '100000',
        'Fc1_X1': '10',
        'Fc2_X1': '1000000',
        'Fc3_X1': '10000000',
        'Fc4_X1': '100000000'
    }
    
    print("Valeurs à substituer:")
    for key, value in values.items():
        print(f"  {key} = {value}")
    print("  pi = 3.14159265359")
    print()
    
    # Simulation de la substitution (sans xcas)
    substituted_expression = new_expression
    for var_name, var_value in values.items():
        substituted_expression = substituted_expression.replace(var_name, var_value)
    substituted_expression = substituted_expression.replace('pi', '3.14159265359')
    
    print("Expression après substitution:")
    print(f"  {substituted_expression}")
    print()
    
    # Vérification qu'il n'y a plus de symboles non substitués
    remaining_symbols = []
    for symbol in ['Av_X1', 'Fc1_X1', 'Fc2_X1', 'Fc3_X1', 'Fc4_X1', 'pi']:
        if symbol in substituted_expression:
            remaining_symbols.append(symbol)
    
    if remaining_symbols:
        print(f"❌ ERREUR: Symboles non substitués: {remaining_symbols}")
        return False
    else:
        print("✅ SUCCÈS: Tous les symboles ont été substitués")
        print("✅ L'expression ne contient plus que des valeurs numériques et la variable 's'")
        return True

def test_expression_complexity():
    """Test pour montrer la différence de complexité"""
    
    print("\n=== Test de complexité des expressions ===")
    
    # Calcul approximatif de 2*pi*fc pour différentes fréquences
    frequencies = [10, 1000000, 10000000, 100000000]  # fc1, fc2, fc3, fc4
    
    print("Calculs de 2*pi*fc pour chaque fréquence de coupure:")
    for i, fc in enumerate(frequencies, 1):
        result = 2 * 3.14159265359 * fc
        print(f"  2*pi*Fc{i} = 2 * 3.14159265359 * {fc} = {result:.2f}")
    print()
    
    print("Avantages de l'utilisation du symbole 'pi':")
    print("  ✓ Expressions symboliques plus lisibles")
    print("  ✓ Évite les très grands nombres numériques dans les expressions intermédiaires")
    print("  ✓ Xcas peut mieux optimiser les calculs symboliques")
    print("  ✓ Substitution numérique seulement à la fin")
    
    return True

if __name__ == "__main__":
    print("Tests de la correction de substitution de pi")
    print("=" * 60)
    
    success = True
    success &= test_pi_substitution()
    success &= test_expression_complexity()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Tous les tests ont réussi!")
        print("\nRésumé de la correction:")
        print("1. Remplacement de '3.14159265359' par 'pi' dans la génération de la fonction de transfert")
        print("2. Ajout de la substitution 'pi' -> '3.14159265359' dans plot_bode()")
        print("3. Les expressions symboliques sont maintenant plus propres")
        print("4. Les très grands nombres numériques ne devraient plus apparaître")
    else:
        print("❌ Certains tests ont échoué.")
    
    print("\nNote: Cette correction devrait éliminer les très grands nombres")
    print("      comme '333356093337442523017800001' dans les expressions symboliques.")
