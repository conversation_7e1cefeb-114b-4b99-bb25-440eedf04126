# Guide du Bouton "Approx" dans bode_H1_final.py

## 🎯 Nouvelles fonctionnalités ajoutées

### Bouton "Approx"
- **Localisation** : Dans le panneau des sliders à droite
- **Couleur** : Vert avec effet hover
- **Fonction** : Lance l'analyse de sensibilité et la simplification automatique

### Bouton "Reset" 
- **Localisation** : Sous le bouton "Approx"
- **Couleur** : Rouge
- **Fonction** : Remet la fonction de transfert originale

## 🔧 Comment utiliser la simplification

### 1. Lancer l'application
```bash
python bode_H1_final.py
```

### 2. Configurer les paramètres
- Définir les valeurs des composants
- Choisir la plage de fréquences
- Valider avec OK

### 3. Simplifier l'expression
1. Cliquer sur le bouton **"Approx"**
2. Choisir le seuil d'erreur maximum (recommandé : 2-5 dB)
3. Attendre l'analyse de sensibilité (quelques secondes)
4. Voir les résultats de la simplification

### 4. Analyser les résultats
- **Variables supprimées** : Liste des composants éliminés
- **Réduction de longueur** : Pourcentage de simplification
- **Courbes comparatives** : Original (bleu) vs Simplifié (vert pointillé)

### 5. Revenir à l'original
- Cliquer sur **"Reset"** pour restaurer l'expression complète

## 📊 Méthode de simplification

### Analyse de sensibilité
1. **Test de variation** : Chaque variable est doublée (+100%)
2. **Calcul d'impact** : Mesure de l'effet sur la réponse fréquentielle
3. **Score géométrique** : Moyenne géométrique des variations de magnitude

### Stratégie de suppression
- **Variables peu sensibles** : Supprimées en premier
- **Validation d'erreur** : Vérification que l'erreur reste sous le seuil
- **Substitution symbolique** :
  - Capacités → 0 (circuit ouvert)
  - Résistances → 0 ou ∞ selon le contexte
  - Autres paramètres → 0

### Affichage des résultats
- **Courbe originale** : Bleue continue
- **Courbe simplifiée** : Verte pointillée
- **Sliders supprimés** : Les variables éliminées disparaissent
- **Message de résultat** : Détails de la simplification

## ⚙️ Paramètres recommandés

### Seuils d'erreur
- **Conservateur** : 1-2 dB (peu de simplification, haute précision)
- **Équilibré** : 3-5 dB (bon compromis)
- **Agressif** : 5-10 dB (forte simplification, précision réduite)

### Types de circuits
- **Filtres simples** : Seuil 2-3 dB
- **Amplificateurs** : Seuil 3-5 dB  
- **Circuits complexes** : Seuil 5-10 dB

## 🚀 Avantages

1. **Simplification automatique** : Plus besoin de deviner quels composants négliger
2. **Validation quantitative** : Contrôle précis de l'erreur introduite
3. **Interface intuitive** : Boutons intégrés dans l'interface existante
4. **Comparaison visuelle** : Voir immédiatement l'impact de la simplification
5. **Réversibilité** : Possibilité de revenir à l'original

## 📝 Exemple d'utilisation

1. **Expression complexe** : 87 caractères avec 5 variables
2. **Après simplification** : 16 caractères avec 3 variables  
3. **Réduction** : 81.6% de réduction de taille
4. **Variables supprimées** : C2, R2 (impact < 2 dB)
5. **Erreur maximale** : 1.7 dB (sous le seuil de 2 dB)

## 🔍 Diagnostic

Si la simplification ne fonctionne pas :
- Vérifier que le fichier FTC.txt existe
- Essayer un seuil d'erreur plus élevé
- Vérifier la console pour les messages d'erreur
- S'assurer que l'expression est valide
