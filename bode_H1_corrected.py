#!/usr/bin/env python3
"""
Script corrigé pour tracer le diagramme de Bode de H1(s) = V7/V1
avec les MÊMES valeurs pour symbolique et numérique
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re

def extract_h1_functions(filename):
    """Extrait les fonctions de transfert H1(s) = V7/V1 du fichier"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire la fonction symbolique
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert num.*?rique|$)', content, re.DOTALL)
    h1_symbolic = h1_symbolic_match.group(1).strip()
    
    # Extraire la fonction numérique
    h1_numeric_match = re.search(r'Fonction de transfert num.*?rique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    h1_numeric = h1_numeric_match.group(1).strip()
    
    return h1_symbolic, h1_numeric

def parse_transfer_function_expression(tf_expr):
    """Parse une expression de fonction de transfert complexe"""
    
    tf_expr = tf_expr.strip()
    
    # Compter les parenthèses pour trouver la séparation
    paren_count = 0
    split_pos = -1
    
    for i, char in enumerate(tf_expr):
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
            if paren_count == 0:
                if i + 1 < len(tf_expr) and tf_expr[i + 1] == '/':
                    split_pos = i + 1
                    break
    
    if split_pos > 0:
        num_expr = tf_expr[1:split_pos-1]
        den_expr = tf_expr[split_pos+2:-1]
        return num_expr, den_expr
    
    return None, None

def reverse_engineer_parameters_from_numeric(h1_numeric):
    """Détermine les valeurs des paramètres à partir de la fonction numérique"""
    
    print("🔍 === RÉTRO-INGÉNIERIE DES PARAMÈTRES ===")
    
    # Parser la fonction numérique
    num_expr, den_expr = parse_transfer_function_expression(h1_numeric)
    
    if num_expr is None or den_expr is None:
        print("❌ Impossible de parser la fonction numérique")
        return None
    
    s = sp.Symbol('s')
    
    # Parser les expressions numériques
    num_numeric = sp.sympify(num_expr)
    den_numeric = sp.sympify(den_expr)
    
    # Extraire les coefficients
    num_poly = sp.Poly(num_numeric, s)
    den_poly = sp.Poly(den_numeric, s)
    
    num_coeffs = [float(c) for c in num_poly.all_coeffs()]
    den_coeffs = [float(c) for c in den_poly.all_coeffs()]
    
    print(f"Coefficients numériques extraits :")
    print(f"Numérateur (degré {len(num_coeffs)-1}) : {num_coeffs}")
    print(f"Dénominateur (degré {len(den_coeffs)-1}) : {den_coeffs}")
    
    # Analyser le terme constant du dénominateur pour déduire R7
    # Le terme constant contient des produits incluant R7
    den_constant = den_coeffs[-1]
    
    print(f"Terme constant du dénominateur : {den_constant:.6e}")
    
    # Analyser les termes avec R7 dans l'expression symbolique
    # Si R7 = 0, tous les termes contenant R7 s'annulent
    print("🎯 ANALYSE : La fonction numérique a été générée avec R7 = 0")
    print("   (tous les termes contenant R7 dans l'expression symbolique s'annulent)")
    r7_value = 0
    
    return r7_value

def create_corrected_symbolic_function(h1_symbolic, r7_value):
    """Crée la fonction symbolique avec la valeur correcte de R7"""
    
    print(f"\n🔧 === CORRECTION DE LA FONCTION SYMBOLIQUE ===")
    print(f"Utilisation de R7 = {r7_value}")
    
    # Parser l'expression symbolique
    num_expr, den_expr = parse_transfer_function_expression(h1_symbolic)
    
    if num_expr is None or den_expr is None:
        print("❌ Impossible de parser la fonction symbolique")
        return None
    
    s = sp.Symbol('s')
    
    # Paramètres avec la valeur correcte de R7
    parameters = {
        'R1': 100.0,
        'R2': 10000.0,
        'R3': 10000.0,
        'R4': 1000.0,
        'R5': 1000.0,
        'R6': 800.0,
        'C1': 1e-7,
        'C2': 1e-5,
        'C3': 1e-7,
        'beta_Q1': 100.0,
        'Cbc_Q1': 2e-12,
        'Cbe_Q1': 1e-11,
        'ro_Q1': 100000.0,
        'rpi_Q1': 833.333333333,
        'R7': r7_value,  # Valeur corrigée
        'gm_Q1': 100.0/833.333333333,
        'Vbe_on_Q1': 0.7,
        'pi': 3.141592653589793
    }
    
    # Créer le dictionnaire de substitution
    subs_dict = {}
    for param, value in parameters.items():
        subs_dict[sp.Symbol(param)] = value
    
    # Parser et substituer
    num_sympy = sp.sympify(num_expr)
    den_sympy = sp.sympify(den_expr)
    
    num_substituted = num_sympy.subs(subs_dict)
    den_substituted = den_sympy.subs(subs_dict)
    
    # Extraire les coefficients
    num_poly = sp.Poly(num_substituted, s)
    den_poly = sp.Poly(den_substituted, s)
    
    num_coeffs = [float(c) for c in num_poly.all_coeffs()]
    den_coeffs = [float(c) for c in den_poly.all_coeffs()]
    
    print(f"Coefficients symboliques corrigés :")
    print(f"Numérateur (degré {len(num_coeffs)-1}) : {[f'{c:.6e}' for c in num_coeffs]}")
    print(f"Dénominateur (degré {len(den_coeffs)-1}) : {[f'{c:.6e}' for c in den_coeffs]}")
    
    # Créer la fonction de transfert
    tf = signal.TransferFunction(num_coeffs, den_coeffs)
    
    return tf

def create_numeric_function(h1_numeric):
    """Crée la fonction de transfert à partir de l'expression numérique"""
    
    print(f"\n📊 === TRAITEMENT DE LA FONCTION NUMÉRIQUE ===")
    
    # Parser l'expression numérique
    num_expr, den_expr = parse_transfer_function_expression(h1_numeric)
    
    if num_expr is None or den_expr is None:
        print("❌ Impossible de parser la fonction numérique")
        return None
    
    s = sp.Symbol('s')
    
    # Parser les expressions numériques
    num_numeric = sp.sympify(num_expr)
    den_numeric = sp.sympify(den_expr)
    
    # Extraire les coefficients
    num_poly = sp.Poly(num_numeric, s)
    den_poly = sp.Poly(den_numeric, s)
    
    num_coeffs = [float(c) for c in num_poly.all_coeffs()]
    den_coeffs = [float(c) for c in den_poly.all_coeffs()]
    
    # Créer la fonction de transfert
    tf = signal.TransferFunction(num_coeffs, den_coeffs)
    
    return tf

def plot_bode_comparison_corrected(tf_symbolic, tf_numeric):
    """Trace les diagrammes de Bode corrigés"""
    
    print(f"\n📈 === GÉNÉRATION DU DIAGRAMME DE BODE CORRIGÉ ===")
    
    # Gamme de fréquences
    frequencies = np.logspace(-1, 9, 10000)  # 0.1 Hz à 1 GHz
    omega = 2 * np.pi * frequencies
    
    # Création du graphique
    _, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    
    # Tracer la fonction symbolique
    if tf_symbolic is not None:
        _, h_sym = signal.freqresp(tf_symbolic, omega)
        magnitude_db_sym = 20 * np.log10(np.abs(h_sym))
        phase_deg_sym = np.angle(h_sym) * 180 / np.pi
        
        ax1.semilogx(frequencies, magnitude_db_sym, 'b-', linewidth=2, label='Symbolique (corrigée)')
        ax2.semilogx(frequencies, phase_deg_sym, 'b-', linewidth=2, label='Symbolique (corrigée)')
        
        print(f"✅ Fonction symbolique tracée")
    
    # Tracer la fonction numérique
    if tf_numeric is not None:
        _, h_num = signal.freqresp(tf_numeric, omega)
        magnitude_db_num = 20 * np.log10(np.abs(h_num))
        phase_deg_num = np.angle(h_num) * 180 / np.pi
        
        ax1.semilogx(frequencies, magnitude_db_num, 'r--', linewidth=2, label='Numérique')
        ax2.semilogx(frequencies, phase_deg_num, 'r--', linewidth=2, label='Numérique')
        
        print(f"✅ Fonction numérique tracée")
    
    # Vérifier si les courbes sont identiques
    if tf_symbolic is not None and tf_numeric is not None:
        max_diff_mag = np.max(np.abs(magnitude_db_sym - magnitude_db_num))
        max_diff_phase = np.max(np.abs(phase_deg_sym - phase_deg_num))
        
        print(f"📊 Différence maximale magnitude : {max_diff_mag:.6f} dB")
        print(f"📊 Différence maximale phase : {max_diff_phase:.6f} degrés")
        
        if max_diff_mag < 1e-6 and max_diff_phase < 1e-6:
            print(f"✅ LES FONCTIONS SONT IDENTIQUES !")
        else:
            print(f"❌ Les fonctions sont encore différentes")
    
    # Configuration des graphiques
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - H1(s) = V7/V1 - Comparaison Corrigée')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(0.1, 1e9)
    ax1.legend()
    
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(0.1, 1e9)
    ax2.legend()
    
    plt.tight_layout()
    
    # Nom du fichier de sortie
    output_filename = 'bode_H1_V7_V1_corrected.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"💾 Diagramme corrigé sauvegardé: {output_filename}")

def main():
    """Fonction principale"""
    
    print("🔧 === ANALYSE CORRIGÉE DE H1(s) = V7/V1 ===")
    
    filename = "Results_Simulation_20250614_1139.txt"
    
    # Extraire les fonctions
    h1_symbolic, h1_numeric = extract_h1_functions(filename)
    
    # Déterminer la valeur correcte de R7 à partir de la fonction numérique
    r7_value = reverse_engineer_parameters_from_numeric(h1_numeric)
    
    if r7_value is not None:
        # Créer la fonction symbolique corrigée
        tf_symbolic = create_corrected_symbolic_function(h1_symbolic, r7_value)
        
        # Créer la fonction numérique
        tf_numeric = create_numeric_function(h1_numeric)
        
        # Tracer les diagrammes de Bode
        if tf_symbolic is not None and tf_numeric is not None:
            plot_bode_comparison_corrected(tf_symbolic, tf_numeric)
        else:
            print("❌ Erreur lors de la création des fonctions de transfert")
    else:
        print("❌ Impossible de déterminer les paramètres corrects")

if __name__ == "__main__":
    main()
