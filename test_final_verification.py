#!/usr/bin/env python3
"""
Test final pour vérifier que la correction de cohérence fonctionne
pour tous les types de circuits.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def test_comprehensive_verification():
    """Test complet avec différents types de circuits"""
    
    test_circuits = [
        {
            "name": "Circuit simple résistif",
            "netlist": """
V1 1 0 DC 5 AC 1
R1 1 2 1k
R2 2 0 2k
""",
            "expected_success": True
        },
        {
            "name": "Circuit OpAmp simple",
            "netlist": """
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 1M 1G
R2 2 3 2k
""",
            "expected_success": True
        },
        {
            "name": "Circuit BJT complexe",
            "netlist": """
V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800
""",
            "expected_success": True
        },
        {
            "name": "Circuit mixte BJT + OpAmp",
            "netlist": """
V1 1 0 DC 5 AC 1
R1 1 2 1k
Q1 3 2 0 100 NPN
R2 3 4 2k
X1 0 4 5 100k 1G 100G
R3 5 0 1k
""",
            "expected_success": True
        }
    ]
    
    all_success = True
    
    for i, test_case in enumerate(test_circuits):
        print(f"\n=== Test {i+1}: {test_case['name']} ===")
        
        try:
            success = analyze_circuit_consistency(test_case['netlist'], test_case['name'])
            
            if success == test_case['expected_success']:
                print(f"✅ Test {i+1} réussi")
            else:
                print(f"❌ Test {i+1} échoué - Résultat inattendu")
                all_success = False
                
        except Exception as e:
            print(f"❌ Test {i+1} échoué avec erreur: {e}")
            all_success = False
    
    return all_success

def analyze_circuit_consistency(netlist_str, circuit_name):
    """Analyse la cohérence d'un circuit donné"""
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    original_nodes = sorted(list(set(comp[i] for comp in original_netlist for i in [1,2]) - {'0'}))
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'Q':
            values[f"Vbe_on_{comp_name}"] = "0.7"
            values[f"Vt_{comp_name}"] = "0.025"
            values[f"VA_{comp_name}"] = "100"
            values[f"beta_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100"
            values[f"Cbe_{comp_name}"] = parse_value(comp[6]) if len(comp) > 6 else "0"
            values[f"Cbc_{comp_name}"] = parse_value(comp[7]) if len(comp) > 7 else "0"
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse DC
    voltages_dc, currents_dc = dc_analysis(netlist, values, all_unique_nodes)
    
    # Test avec l'ancienne méthode
    dc_voltages_num_old = substitute_values(voltages_dc, values, use_improved_method=False)
    
    # Test avec la nouvelle méthode
    dc_voltages_num_new = substitute_values(voltages_dc, values, use_improved_method=True)
    
    # Vérifier la cohérence entre les deux méthodes
    inconsistencies = 0
    
    for node in original_nodes:
        if node in voltages_dc and node in dc_voltages_num_old and node in dc_voltages_num_new:
            # Vérifier que la nouvelle méthode est cohérente avec la substitution manuelle
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_dc[node], dc_voltages_num_new[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_DC avec nouvelle méthode:")
                print(f"   Symbolique: {voltages_dc[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                inconsistencies += 1
            else:
                print(f"✅ V{node}_DC cohérent avec nouvelle méthode")
    
    # Analyse AC rapide pour vérifier que ça fonctionne aussi
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    ac_voltages_num_new = substitute_values(voltages_ac, values, use_improved_method=True)
    
    # Vérifier quelques nœuds AC
    for node in original_nodes[:2]:  # Juste les 2 premiers pour gagner du temps
        if node in voltages_ac and node in ac_voltages_num_new:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_ac[node], ac_voltages_num_new[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_AC avec nouvelle méthode")
                inconsistencies += 1
            else:
                print(f"✅ V{node}_AC cohérent avec nouvelle méthode")
    
    print(f"\nRésultat pour {circuit_name}: {inconsistencies} incohérences trouvées")
    
    return inconsistencies == 0

if __name__ == "__main__":
    print("Test final de vérification de la cohérence")
    print("=" * 50)
    
    success = test_comprehensive_verification()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!")
        print("✅ La correction de cohérence fonctionne correctement.")
        print("✅ Les expressions symboliques et numériques sont maintenant cohérentes.")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("⚠️  Des problèmes de cohérence persistent.")
