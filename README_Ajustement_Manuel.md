# Guide du Bouton "Ajustement manuel" dans bode_H1_final.py

## 🎯 Nouvelle fonctionnalité ajoutée

### Bouton "Ajustement manuel"
- **Localisation** : Dans la fenêtre principale, en dessous du bouton "Analyse de sensibilité"
- **Couleur** : Orange avec effet hover
- **Fonction** : Permet la modification manuelle de l'expression de transfert
- **Activation** : Toujours actif, disponible dès le lancement

## 🔧 Comment utiliser l'ajustement manuel

### 1. Prérequis
1. Lancer l'application : `python bode_H1_final.py`
2. Configurer les paramètres des composants

### 2. Accéder à l'ajustement manuel
1. Dans la fenêtre principale, panneau de droite
2. Cliquer sur le bouton **"Ajustement manuel"** (orange, en dessous d'Analyse de sensibilité)
3. Une nouvelle fenêtre s'ouvre avec l'expression actuelle

### 3. Modifier l'expression
1. **Zone d'édition** : L'expression actuelle est affichée (simplifiée si disponible, sinon originale)
2. **Modification** : Éditer directement l'expression symbolique
3. **Syntaxe** : Utiliser la syntaxe SymPy (Python)
4. **Validation** : L'expression est vérifiée avant application

### 4. Appliquer les modifications
1. Cliquer sur **"Valider et afficher"** (vert)
2. L'expression est validée syntaxiquement
3. Une nouvelle courbe verte pointillée apparaît sur le diagramme de Bode
4. La courbe répond aux changements de sliders en temps réel

### 5. Options disponibles
- **"Remettre l'expression originale"** : Restaure l'expression de transfert originale
- **"Annuler"** : Ferme la fenêtre sans modifications

## 📊 Fonctionnalités techniques

### Validation d'expression
- **Syntaxe SymPy** : L'expression doit être valide en Python/SymPy
- **Variables** : Utiliser les mêmes noms que dans l'expression originale
- **Opérateurs** : +, -, *, /, **, sqrt(), sin(), cos(), etc.

### Affichage sur le diagramme
- **Courbe verte pointillée** : Expression modifiée manuellement
- **Courbe rouge tiretée** : Expression simplifiée automatiquement
- **Courbe bleue continue** : Expression originale
- **Légende** : Mise à jour automatique

### Réactivité aux sliders
- **Temps réel** : La courbe manuelle se met à jour instantanément
- **Toutes les variables** : Répond aux changements de tous les composants
- **Performance** : Optimisée avec la même méthode que les autres courbes

## 🎨 Exemples d'utilisation

### Exemple 1 : Simplification manuelle
```
Expression originale : (R1*R2*C1*s + R1 + R2)/(R1*R2*C1*C2*s^2 + (R1*C1 + R2*C2)*s + 1)
Expression simplifiée : 1/(R1*C1*s + 1)
Modification manuelle : 1/(R1*C1*s + 1 + 0.1*R2*C2*s)
```

### Exemple 2 : Ajout de termes correctifs
```
Expression simplifiée : R2/(R1 + R2)
Modification manuelle : R2/(R1 + R2) * (1 - 0.01*C1*s)
```

### Exemple 3 : Approximation personnalisée
```
Expression simplifiée : 1/(s + 1000)
Modification manuelle : 1000/(s + 1000)  # Ajout du gain DC
```

## ⚠️ Conseils et bonnes pratiques

### Syntaxe recommandée
- Utiliser des parenthèses pour clarifier les priorités
- Préférer `**` à `^` pour les puissances
- Utiliser `sqrt()` plutôt que `**0.5`

### Variables disponibles
- Toutes les variables de l'expression originale
- Respecter exactement les noms (R1, C1, etc.)
- Éviter d'introduire de nouvelles variables non définies

### Validation
- Tester l'expression avant de l'appliquer
- Vérifier que la courbe résultante est cohérente
- Utiliser les sliders pour valider le comportement

## 🔍 Dépannage

### Erreurs courantes
1. **Syntaxe invalide** : Vérifier les parenthèses et opérateurs
2. **Variable inconnue** : Utiliser uniquement les variables existantes
3. **Division par zéro** : Éviter les expressions qui peuvent devenir nulles

### Messages d'erreur
- **"L'expression n'est pas valide"** : Problème de syntaxe SymPy
- **"L'expression ne peut pas être vide"** : Zone de texte vide
- **"Erreur lors de l'application"** : Problème lors du calcul

### Solutions
1. Vérifier la syntaxe dans la documentation SymPy
2. Tester avec des expressions simples d'abord
3. Utiliser le bouton "Remettre l'expression simplifiée" pour recommencer

## 📈 Intégration avec les autres fonctionnalités

### Compatibilité
- **Analyse de sensibilité** : Fonctionne avec tous les résultats
- **Simplification automatique** : Point de départ pour l'ajustement
- **Sliders** : Réactivité complète maintenue
- **Sauvegarde** : L'expression manuelle peut être copiée depuis l'interface

### Workflow recommandé
1. Configuration des paramètres → Définition des valeurs et plages
2. Ajustement manuel → Modification directe de l'expression
3. Validation avec sliders → Vérification du comportement
4. Analyse de sensibilité (optionnel) → Identification des variables importantes
5. Analyse comparative → Comparaison des courbes originale et manuelle
