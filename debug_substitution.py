#!/usr/bin/env python3
"""
Script de diagnostic pour vérifier la substitution des paramètres
dans la fonction symbolique H1(s) = V7/V1
"""
import sympy as sp
import re

def extract_h1_functions(filename):
    """Extrait les fonctions de transfert H1(s) = V7/V1 du fichier"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire la fonction symbolique
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert num.*?rique|$)', content, re.DOTALL)
    h1_symbolic = h1_symbolic_match.group(1).strip()
    
    # Extraire la fonction numérique
    h1_numeric_match = re.search(r'Fonction de transfert num.*?rique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    h1_numeric = h1_numeric_match.group(1).strip()
    
    return h1_symbolic, h1_numeric

def extract_all_symbols_from_expression(expr_str):
    """Extrait tous les symboles d'une expression"""
    
    # Parser l'expression
    expr = sp.sympify(expr_str)
    
    # Extraire tous les symboles
    symbols = expr.free_symbols
    
    return symbols

def parse_transfer_function_expression(tf_expr):
    """Parse une expression de fonction de transfert complexe"""
    
    tf_expr = tf_expr.strip()
    
    # Compter les parenthèses pour trouver la séparation
    paren_count = 0
    split_pos = -1
    
    for i, char in enumerate(tf_expr):
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
            if paren_count == 0:
                if i + 1 < len(tf_expr) and tf_expr[i + 1] == '/':
                    split_pos = i + 1
                    break
    
    if split_pos > 0:
        num_expr = tf_expr[1:split_pos-1]
        den_expr = tf_expr[split_pos+2:-1]
        return num_expr, den_expr
    
    return None, None

def main():
    """Fonction principale de diagnostic"""
    
    print("🔍 === DIAGNOSTIC DE SUBSTITUTION ===")
    
    filename = "Results_Simulation_20250614_1139.txt"
    
    # Extraire les fonctions
    h1_symbolic, h1_numeric = extract_h1_functions(filename)
    
    print(f"\n📝 Longueurs des expressions :")
    print(f"Symbolique : {len(h1_symbolic)} caractères")
    print(f"Numérique  : {len(h1_numeric)} caractères")
    
    # Parser les expressions
    num_sym, den_sym = parse_transfer_function_expression(h1_symbolic)
    num_num, den_num = parse_transfer_function_expression(h1_numeric)
    
    if num_sym and den_sym and num_num and den_num:
        print(f"\n🔍 === ANALYSE DES SYMBOLES ===")
        
        # Extraire les symboles de la fonction symbolique
        print(f"\n📊 Symboles dans le numérateur symbolique :")
        num_symbols = extract_all_symbols_from_expression(num_sym)
        for symbol in sorted(num_symbols, key=str):
            if str(symbol) != 's':
                print(f"  {symbol}")
        
        print(f"\n📊 Symboles dans le dénominateur symbolique :")
        den_symbols = extract_all_symbols_from_expression(den_sym)
        for symbol in sorted(den_symbols, key=str):
            if str(symbol) != 's':
                print(f"  {symbol}")
        
        # Tous les symboles uniques
        all_symbols = num_symbols.union(den_symbols)
        param_symbols = {str(s) for s in all_symbols if str(s) != 's'}
        
        print(f"\n🎯 TOUS LES PARAMÈTRES REQUIS ({len(param_symbols)}) :")
        for param in sorted(param_symbols):
            print(f"  {param}")
        
        # Paramètres disponibles
        parameters = {
            'R1': 100.0,
            'R2': 10000.0,
            'R3': 10000.0,
            'R4': 1000.0,
            'R5': 1000.0,
            'R6': 800.0,
            'C1': 1e-7,
            'C2': 1e-5,
            'C3': 1e-7,
            'beta_Q1': 100.0,
            'Cbc_Q1': 2e-12,
            'Cbe_Q1': 1e-11,
            'ro_Q1': 100000.0,
            'rpi_Q1': 833.333333333,
            'R7': 1.0,
            'gm_Q1': 100.0/833.333333333,
            'Vbe_on_Q1': 0.7,
            'pi': 3.141592653589793
        }
        
        print(f"\n✅ PARAMÈTRES DISPONIBLES ({len(parameters)}) :")
        for param, value in sorted(parameters.items()):
            print(f"  {param} = {value}")
        
        # Vérifier les paramètres manquants
        available_params = set(parameters.keys())
        missing_params = param_symbols - available_params
        extra_params = available_params - param_symbols
        
        if missing_params:
            print(f"\n❌ PARAMÈTRES MANQUANTS ({len(missing_params)}) :")
            for param in sorted(missing_params):
                print(f"  {param}")
        else:
            print(f"\n✅ TOUS LES PARAMÈTRES SONT DISPONIBLES")
        
        if extra_params:
            print(f"\n⚠️  PARAMÈTRES SUPPLÉMENTAIRES ({len(extra_params)}) :")
            for param in sorted(extra_params):
                print(f"  {param}")
        
        # Test de substitution
        print(f"\n🧪 === TEST DE SUBSTITUTION ===")
        
        try:
            s = sp.Symbol('s')
            
            # Créer le dictionnaire de substitution
            subs_dict = {}
            for param, value in parameters.items():
                subs_dict[sp.Symbol(param)] = value
            
            print(f"Dictionnaire de substitution créé avec {len(subs_dict)} entrées")
            
            # Parser et substituer
            num_sympy = sp.sympify(num_sym)
            den_sympy = sp.sympify(den_sym)
            
            print(f"Expressions symboliques parsées")
            
            # Substituer
            num_substituted = num_sympy.subs(subs_dict)
            den_substituted = den_sympy.subs(subs_dict)
            
            print(f"Substitution effectuée")
            
            # Vérifier s'il reste des symboles non substitués
            remaining_num_symbols = num_substituted.free_symbols
            remaining_den_symbols = den_substituted.free_symbols
            
            remaining_params_num = {str(s) for s in remaining_num_symbols if str(s) != 's'}
            remaining_params_den = {str(s) for s in remaining_den_symbols if str(s) != 's'}
            
            if remaining_params_num or remaining_params_den:
                print(f"\n❌ SYMBOLES NON SUBSTITUÉS :")
                all_remaining = remaining_params_num.union(remaining_params_den)
                for param in sorted(all_remaining):
                    print(f"  {param}")
            else:
                print(f"\n✅ SUBSTITUTION COMPLÈTE - AUCUN SYMBOLE RESTANT")
            
            # Comparer avec la fonction numérique
            print(f"\n🔍 === COMPARAISON AVEC FONCTION NUMÉRIQUE ===")
            
            num_numeric = sp.sympify(num_num)
            den_numeric = sp.sympify(den_num)
            
            # Évaluer à s=0 pour comparer les gains DC
            s_val = 0

            try:
                num_dc_sym = num_substituted.subs(s, s_val)
                den_dc_sym = den_substituted.subs(s, s_val)

                print(f"Numérateur symbolique à s=0 : {num_dc_sym}")
                print(f"Dénominateur symbolique à s=0 : {den_dc_sym}")

                if den_dc_sym == 0:
                    print(f"❌ DÉNOMINATEUR SYMBOLIQUE = 0 à s=0 !")
                    dc_symbolic = float('inf')
                else:
                    dc_symbolic = float(num_dc_sym / den_dc_sym)

            except Exception as e:
                print(f"❌ Erreur calcul symbolique : {e}")
                dc_symbolic = float('inf')

            try:
                num_dc_num = num_numeric.subs(s, s_val)
                den_dc_num = den_numeric.subs(s, s_val)

                print(f"Numérateur numérique à s=0 : {num_dc_num}")
                print(f"Dénominateur numérique à s=0 : {den_dc_num}")

                if den_dc_num == 0:
                    print(f"❌ DÉNOMINATEUR NUMÉRIQUE = 0 à s=0 !")
                    dc_numeric = float('inf')
                else:
                    dc_numeric = float(num_dc_num / den_dc_num)

            except Exception as e:
                print(f"❌ Erreur calcul numérique : {e}")
                dc_numeric = float('inf')

            # Afficher les résultats
            if dc_symbolic != float('inf') and not sp.oo in [dc_symbolic]:
                try:
                    dc_sym_val = float(dc_symbolic)
                    if dc_sym_val != 0:
                        print(f"Gain DC symbolique : {dc_sym_val:.6e} ({20*sp.log(abs(dc_sym_val), 10):.1f} dB)")
                    else:
                        print(f"Gain DC symbolique : 0 (-∞ dB)")
                except:
                    print(f"Gain DC symbolique : {dc_symbolic}")
            else:
                print(f"Gain DC symbolique : INFINI ou INDÉTERMINÉ")

            if dc_numeric != float('inf') and not sp.oo in [dc_numeric]:
                try:
                    dc_num_val = float(dc_numeric)
                    if dc_num_val != 0:
                        print(f"Gain DC numérique  : {dc_num_val:.6e} ({20*sp.log(abs(dc_num_val), 10):.1f} dB)")
                    else:
                        print(f"Gain DC numérique  : 0 (-∞ dB)")
                except:
                    print(f"Gain DC numérique  : {dc_numeric}")
            else:
                print(f"Gain DC numérique  : INFINI ou INDÉTERMINÉ")

            if dc_symbolic != float('inf') and dc_numeric != float('inf'):
                if abs(dc_symbolic - dc_numeric) < 1e-10:
                    print(f"✅ GAINS DC IDENTIQUES")
                else:
                    print(f"❌ GAINS DC DIFFÉRENTS - Écart : {abs(dc_symbolic - dc_numeric):.6e}")
            else:
                print(f"❌ IMPOSSIBLE DE COMPARER - AU MOINS UN GAIN EST INFINI")

            # Analyser les coefficients des polynômes
            print(f"\n🔍 === ANALYSE DES COEFFICIENTS ===")

            try:
                # Extraire les coefficients symboliques
                num_poly_sym = sp.Poly(num_substituted, s)
                den_poly_sym = sp.Poly(den_substituted, s)

                num_coeffs_sym = num_poly_sym.all_coeffs()
                den_coeffs_sym = den_poly_sym.all_coeffs()

                print(f"Coefficients numérateur symbolique (degré {len(num_coeffs_sym)-1}) :")
                for i, coeff in enumerate(num_coeffs_sym):
                    print(f"  s^{len(num_coeffs_sym)-1-i}: {float(coeff):.6e}")

                print(f"Coefficients dénominateur symbolique (degré {len(den_coeffs_sym)-1}) :")
                for i, coeff in enumerate(den_coeffs_sym):
                    print(f"  s^{len(den_coeffs_sym)-1-i}: {float(coeff):.6e}")

            except Exception as e:
                print(f"❌ Erreur analyse coefficients symboliques : {e}")

            try:
                # Extraire les coefficients numériques
                num_poly_num = sp.Poly(num_numeric, s)
                den_poly_num = sp.Poly(den_numeric, s)

                num_coeffs_num = num_poly_num.all_coeffs()
                den_coeffs_num = den_poly_num.all_coeffs()

                print(f"\nCoefficients numérateur numérique (degré {len(num_coeffs_num)-1}) :")
                for i, coeff in enumerate(num_coeffs_num):
                    print(f"  s^{len(num_coeffs_num)-1-i}: {float(coeff):.6e}")

                print(f"Coefficients dénominateur numérique (degré {len(den_coeffs_num)-1}) :")
                for i, coeff in enumerate(den_coeffs_num):
                    print(f"  s^{len(den_coeffs_num)-1-i}: {float(coeff):.6e}")

            except Exception as e:
                print(f"❌ Erreur analyse coefficients numériques : {e}")
                
        except Exception as e:
            print(f"❌ Erreur lors du test : {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
