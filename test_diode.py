#!/usr/bin/env python3
"""
Test simple pour vérifier que le composant diode fonctionne correctement
"""

# Test de la fonction parse_value
def parse_value(value_str):
    value_str = str(value_str).strip()
    if value_str[-1].lower() in 'kmunpfg' or value_str[-1].upper() in 'MG':
        multiplier = {'k': 1e3, 'm': 1e-3, 'u': 1e-6, 'n': 1e-9, 'p': 1e-12, 'f': 1e-15, 'M': 1e6, 'G': 1e9, 'g': 1e9}[value_str[-1]]
        return str(float(value_str[:-1]) * multiplier)
    try: return str(float(value_str))
    except: return value_str

# Test de parsing d'une netlist avec diode
def test_diode_parsing():
    print("=== Test de parsing de diode ===")
    
    # Netlist de test avec une diode
    netlist_lines = [
        "V1 1 0 DC 5",
        "R1 1 2 1k", 
        "D1 2 0 0.7",
        "R2 2 3 2k"
    ]
    
    netlist = [line.split() for line in netlist_lines]
    values = {}
    
    # Traitement des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        print(f"Traitement du composant: {comp_name} (type: {comp_type})")
        
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
                print(f"  {comp_name} = {values[comp_name]}")
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: 
                values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
                print(f"  Vdc_{comp_name} = {values[f'Vdc_{comp_name}']}")
            if ac_val_str: 
                values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
                print(f"  Vac_{comp_name} = {values[f'Vac_{comp_name}']}")
        elif comp_type == 'D':
            # Diode avec tension de seuil
            values[f"Vth_{comp_name}"] = parse_value(comp[3]) if len(comp) > 3 else "0.7"
            values[f"rd_{comp_name}"] = "1e-3"  # Résistance dynamique par défaut
            print(f"  Vth_{comp_name} = {values[f'Vth_{comp_name}']} V")
            print(f"  rd_{comp_name} = {values[f'rd_{comp_name}']} Ohm")
    
    print("\n=== Valeurs extraites ===")
    for key, value in values.items():
        print(f"{key} = {value}")
    
    return netlist, values

# Test de la syntaxe utilisateur
def test_user_syntax():
    print("\n=== Test de la syntaxe utilisateur ===")
    print("Syntaxe pour les composants:")
    print("Resistance: Rnom noeud1 noeud2 valeur (ex: R1 1 2 1k)")
    print("Condensateur: Cnom noeud1 noeud2 valeur (ex: C1 1 2 1u)")
    print("Inductance: Lnom noeud1 noeud2 valeur (ex: L1 1 2 1m)")
    print("Source de tension: Vnom noeud+ noeud- DC valeur_dc AC valeur_ac (ex: V1 1 0 DC 5 AC 1)")
    print("Diode: Dnom anode cathode tension_seuil (ex: D1 1 2 0.7)")
    print("Transistor BJT: Qnom nc nb ne [beta] [type] (ex: Q1 c b e NPN ou Q1 c b e 150 PNP) avec VA_Q1=100 et beta_Q1=100 par defaut")
    print("Amplificateur operationnel: Xnom V- V+ Vout [Av] [fc_cut1] [fc_cut2] (ex: X1 0 2 3 100k 1G 100G)")

if __name__ == "__main__":
    test_user_syntax()
    netlist, values = test_diode_parsing()
    
    print("\n=== Test réussi ===")
    print("Le composant diode a été ajouté avec succès au simulateur!")
    print("- Syntaxe: Dnom anode cathode tension_seuil")
    print("- Paramètres symboliques: Vth_Dnom (tension de seuil), rd_Dnom (résistance dynamique)")
    print("- Exemple: D1 1 2 0.7 (diode D1 entre nœuds 1 et 2 avec tension de seuil 0.7V)")
