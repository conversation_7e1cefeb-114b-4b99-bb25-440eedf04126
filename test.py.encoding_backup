import subprocess
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import time
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import re
import sys
from datetime import datetime
import tempfile
import os
from functools import lru_cache
from collections import defaultdict

def file_print(message="", end='\n\n'): print(message, end=end)

def parse_value(value_str):
    value_str = str(value_str).strip()
    if value_str[-1].lower() in 'kmunpfg' or value_str[-1].upper() in 'MG':
        multiplier = {'k': 1e3, 'm': 1e-3, 'u': 1e-6, 'n': 1e-9, 'p': 1e-12, 'f': 1e-15, 'M': 1e6, 'G': 1e9, 'g': 1e9}[value_str[-1]]
        return str(float(value_str[:-1]) * multiplier)
    try: return str(float(value_str))
    except: return value_str

@lru_cache(maxsize=5000)
def xcas(cmd):
    if len(cmd) > 4096:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.xcas', encoding='utf-8') as tmp_file:
            tmp_file.write(cmd)
            tmp_file_name = tmp_file.name
        result = ""
        try:
            p = subprocess.run(['C:\\xcaswin\\icas.exe', tmp_file_name], capture_output=True, text=True, encoding='utf-8')
            result = p.stdout.strip()
        except FileNotFoundError: raise
        finally:
            if os.path.exists(tmp_file_name): os.remove(tmp_file_name)
    else:
        p = subprocess.run(['C:\\xcaswin\\icas.exe', '--eval', cmd], capture_output=True, text=True, encoding='utf-8')
        result = p.stdout.strip()
    if '[undef,' in result:
        start = result.find('[undef,') + 7
        end = result.rfind(']')
        if start < end: return result[start:end].strip()
    return result

def trace_series_resistive_path(netlist, start_node, capacitor_name, bjt_emitter_node, values_dict):
    current_node, path_resistors_values, last_component_name = start_node, [], capacitor_name
    visited_resistors_on_path = {capacitor_name}
    for _ in range(len(netlist) + 5):
        if current_node == '0': return path_resistors_values, True
        components_on_current_node = [c for c in netlist if c[1] == current_node or c[2] == current_node]
        next_options = [comp for comp in components_on_current_node if comp[0] != last_component_name]
        if not next_options: return path_resistors_values, True
        if len(next_options) > 1: return path_resistors_values, False
        next_comp_details = next_options[0]
        if next_comp_details[0][0].upper() != 'R': return path_resistors_values, False
        resistor_name = next_comp_details[0]
        if resistor_name in visited_resistors_on_path: return path_resistors_values, False
        resistor_value_str = values_dict.get(resistor_name)
        if resistor_value_str is None: return path_resistors_values, False
        try: _ = float(parse_value(resistor_value_str))
        except ValueError: return path_resistors_values, False
        path_resistors_values.append(resistor_value_str)
        visited_resistors_on_path.add(resistor_name)
        node1, node2 = next_comp_details[1], next_comp_details[2]
        next_node = node2 if node1 == current_node else node1
        if next_node == bjt_emitter_node: return path_resistors_values, False
        last_component_name, current_node = resistor_name, next_node
    return path_resistors_values, False

def should_modify_voltage_sources(netlist, bjt_components_info, values_dict):
    if not bjt_components_info: return False
    for bjt_comp_details in bjt_components_info:
        name_q, _, _, ne = bjt_comp_details[0], bjt_comp_details[1], bjt_comp_details[2], bjt_comp_details[3]
        bjt_type_str = bjt_comp_details[5].upper() if len(bjt_comp_details) > 5 and len(bjt_comp_details[5]) > 0 else 'NPN'
        emitter_components = [c for c in netlist if (c[1] == ne or c[2] == ne) and c[0] != name_q]
        is_purely_resistive_emitter = True
        if not emitter_components and ne != '0': is_purely_resistive_emitter = False
        elif not emitter_components and ne == '0': pass
        else:
            for ec in emitter_components:
                if ec[0][0].upper() in ['L', 'C']:
                    is_purely_resistive_emitter = False
                    break
        if is_purely_resistive_emitter: return True
        if bjt_type_str == 'PNP':
            for emitter_conn_comp in emitter_components:
                if emitter_conn_comp[0][0].upper() == 'C':
                    capacitor_name = emitter_conn_comp[0]
                    node_cap_other_end = emitter_conn_comp[1] if emitter_conn_comp[2] == ne else emitter_conn_comp[2]
                    series_r_strings, _ = trace_series_resistive_path(netlist, node_cap_other_end, capacitor_name, ne, values_dict)
                    if series_r_strings:
                        try:
                            total_series_resistance = sum(float(parse_value(r_val_str)) for r_val_str in series_r_strings)
                            if total_series_resistance >= 500.0: return True
                        except ValueError: pass
    return False

def create_ac_netlist_for_bjt_estimation(netlist, values):
    ac_netlist, ac_values_for_estimation = [], values.copy()
    bjt_components_info_for_estimation = [comp_info for comp_info in netlist if comp_info[0][0].upper() == 'Q']
    if bjt_components_info_for_estimation and should_modify_voltage_sources(netlist, bjt_components_info_for_estimation, values):
        for comp_item_cond in netlist:
            if comp_item_cond[0][0].upper() == 'V' and comp_item_cond[0] != 'V1':
                comp_name_cond = comp_item_cond[0]
                ac_values_for_estimation[f"Vac_{comp_name_cond}"] = '1'
    for comp in netlist:
        new_comp, comp_type, comp_name = comp[:], comp[0][0].upper(), comp[0]
        if comp_type == 'V':
            dc_val_from_dict = ac_values_for_estimation.get(f"Vdc_{comp_name}", '0')
            ac_val_from_dict = ac_values_for_estimation.get(f"Vac_{comp_name}", '0')
            dc_val_parsed, ac_val_parsed = parse_value(dc_val_from_dict), parse_value(ac_val_from_dict)
            total_val_for_estimation_source = xcas(f"simplify({dc_val_parsed} + {ac_val_parsed})")
            new_comp_entry = [comp_name, comp[1], comp[2], 'DC', total_val_for_estimation_source]
            ac_values_for_estimation[f"Vdc_{comp_name}"] = total_val_for_estimation_source
            ac_values_for_estimation.pop(f"Vac_{comp_name}", None)
            ac_netlist.append(new_comp_entry)
        elif comp_type == 'C':
            new_res_name = f"R_C{comp_name[1:]}_short"
            new_comp_entry = [new_res_name, comp[1], comp[2], '1e-9']
            ac_values_for_estimation[new_res_name] = '1e-9'
            ac_netlist.append(new_comp_entry)
        elif comp_type == 'L': ac_netlist.append(new_comp)
        else: ac_netlist.append(new_comp)
    return ac_netlist, ac_values_for_estimation

def dc_analysis(netlist, values, nodes):
    node_groups = {node: i for i, node in enumerate(nodes)}
    for comp in netlist:
        if comp[0][0].upper() == 'L' and comp[1] != '0' and comp[2] != '0':
            g1, g2 = node_groups[comp[1]], node_groups[comp[2]]
            if g1 == g2: continue
            keys_to_update = [k for k, v in node_groups.items() if v == g2]
            for k in keys_to_update: node_groups[k] = g1
    unique_effective_groups = sorted(list(set(node_groups.values())))
    group_to_compact_idx = {group: i for i, group in enumerate(unique_effective_groups)}
    node_name_to_compact_idx = {node: group_to_compact_idx[node_groups[node]] for node in nodes}
    n_effective_nodes = len(unique_effective_groups)
    bjt_components_info = [comp for comp in netlist if comp[0][0].upper() == 'Q']
    op_components_info = [comp for comp in netlist if comp[0][0].upper() == 'X']
    num_bjt_current_vars = len(bjt_components_info)
    num_op_current_vars = len(op_components_info)
    n_total_vars = n_effective_nodes + num_bjt_current_vars + num_op_current_vars
    G_dc = [['0']*n_total_vars for _ in range(n_total_vars)]
    I_dc = ['0']*n_total_vars
    for comp in netlist:
        name, n1, n2, comp_type = comp[0], comp[1], comp[2], comp[0][0].upper()
        idx1 = node_name_to_compact_idx.get(n1) if n1 != '0' else None
        idx2 = node_name_to_compact_idx.get(n2) if n2 != '0' else None
        if comp_type == 'V':
            dc_val = next((comp[i+1] for i in range(3, len(comp), 2) if i < len(comp) and comp[i].upper() == 'DC'), None)
            if dc_val:
                if n1 != '0':
                    row_idx = idx1
                    G_dc[row_idx] = ['0']*n_total_vars
                    G_dc[row_idx][row_idx] = '1'
                    I_dc[row_idx] = dc_val
                elif n2 != '0':
                    row_idx = idx2
                    G_dc[row_idx] = ['0']*n_total_vars
                    G_dc[row_idx][row_idx] = '1'
                    I_dc[row_idx] = f"-({dc_val})"
        elif comp_type == 'R':
            # Pour les résistances de compensation BJT, utiliser directement la valeur numérique
            if is_bjt_compensation_component(name, values):
                resistance_value = values.get(name, '1')
                admittance = f"1/{resistance_value}"
            else:
                admittance = f"1/{name}"
            for i, j in [(idx1, idx2), (idx2, idx1)]:
                if i is not None and G_dc[i][i] != '1':
                    G_dc[i][i] = f"({G_dc[i][i]})+({admittance})" if G_dc[i][i] != '0' else admittance
                    if j is not None:
                        G_dc[i][j] = f"({G_dc[i][j]})-({admittance})" if G_dc[i][j] != '0' else f"-({admittance})"

    bjt_current_var_offset = n_effective_nodes
    for k, comp in enumerate(bjt_components_info):
        name_q, nc, nb, ne = comp[0], comp[1], comp[2], comp[3]
        beta_sym, bjt_type = f"beta_{name_q}", comp[5] if len(comp) > 5 else 'NPN'
        idx_c = node_name_to_compact_idx.get(nc) if nc != '0' else None
        idx_b = node_name_to_compact_idx.get(nb) if nb != '0' else None
        idx_e = node_name_to_compact_idx.get(ne) if ne != '0' else None
        idx_ib_var = bjt_current_var_offset + k
        Vbe_on_sym = f"Vbe_on_{name_q}"
        if bjt_type.upper() == 'NPN':
            if idx_b is not None: G_dc[idx_ib_var][idx_b] = '1'
            if idx_e is not None: G_dc[idx_ib_var][idx_e] = '-1'
            I_dc[idx_ib_var] = Vbe_on_sym
        else:
            if idx_e is not None: G_dc[idx_ib_var][idx_e] = '1'
            if idx_b is not None: G_dc[idx_ib_var][idx_b] = '-1'
            I_dc[idx_ib_var] = Vbe_on_sym
        if idx_b is not None: G_dc[idx_b][idx_ib_var] = "1"
        if idx_c is not None: G_dc[idx_c][idx_ib_var] = beta_sym
        if idx_e is not None:
            ie_coeff = f"-(1+{beta_sym})"
            G_dc[idx_e][idx_ib_var] = ie_coeff

    op_current_var_offset = n_effective_nodes + num_bjt_current_vars
    for k, comp in enumerate(op_components_info):
        name_op, v_minus, v_plus, v_out = comp[0], comp[1], comp[2], comp[3]
        av_sym = f"Av_{name_op}"
        idx_minus, idx_plus, idx_out = (node_name_to_compact_idx.get(v_minus) if v_minus != '0' else None), (node_name_to_compact_idx.get(v_plus) if v_plus != '0' else None), (node_name_to_compact_idx.get(v_out) if v_out != '0' else None)
        idx_iout_var = op_current_var_offset + k
        if idx_out is not None:
            G_dc[idx_iout_var][idx_out] = '1'
            if idx_plus is not None: G_dc[idx_iout_var][idx_plus] = f"-({av_sym})"
            if idx_minus is not None: G_dc[idx_iout_var][idx_minus] = av_sym
            I_dc[idx_iout_var] = '0'
            G_dc[idx_out][idx_iout_var] = "1"

    voltages_dc, currents_dc = {}, {}
    if n_total_vars > 0:
        G_dc_matrix = f"[{','.join(['[' + ','.join(row) + ']' for row in G_dc])}]"
        det_G_dc = xcas(f"det({G_dc_matrix})")
        if det_G_dc == '0':
            for node in nodes: voltages_dc[node] = 'undef'
            for comp in netlist:
                if comp[0][0].upper() == 'Q':
                    currents_dc[f"{comp[0]}_c"] = 'undef'
                    currents_dc[f"{comp[0]}_b"] = 'undef'
                    currents_dc[f"{comp[0]}_e"] = 'undef'
                elif comp[0][0].upper() == 'X':
                    currents_dc[f"{comp[0]}_out"] = 'undef'
                else: currents_dc[comp[0]] = 'undef'
            return voltages_dc, currents_dc
        variables_to_solve = [(node_name_to_compact_idx[node], node) for node in nodes if node != '0']
        variables_to_solve.extend([(bjt_current_var_offset + k, f"I_{comp[0]}_Ib") for k, comp in enumerate(bjt_components_info)])
        variables_to_solve.extend([(op_current_var_offset + k, f"I_{comp[0]}_out") for k, comp in enumerate(op_components_info)])
        def solve_variable_cramer(args):
            i, var_name = args
            G_temp = [row[:] for row in G_dc]
            for j in range(n_total_vars): G_temp[j][i] = I_dc[j]
            G_temp_matrix = f"[{','.join(['[' + ','.join(row) + ']' for row in G_temp])}]"
            det_temp = xcas(f"det({G_temp_matrix})")
            return var_name, xcas(f"simplify(({det_temp})/({det_G_dc}))")
        with ThreadPoolExecutor(max_workers=mp.cpu_count()) as executor:
            solution_results = list(executor.map(solve_variable_cramer, variables_to_solve))
        solution_dict = dict(solution_results)
        for node in nodes: voltages_dc[node] = solution_dict.get(node, '0') if node != '0' else '0'
        for comp in bjt_components_info:
            name_q = comp[0]
            beta_sym = f"beta_{name_q}"
            ib_sym = solution_dict.get(f"I_{name_q}_Ib", '0')
            ic_sym = xcas(f"simplify({beta_sym}*({ib_sym}))")
            ie_sym = xcas(f"simplify(({ib_sym})+({ic_sym}))")
            bjt_type = comp[5] if len(comp) > 5 else 'NPN'
            if bjt_type.upper() == 'NPN':
                currents_dc[f"{name_q}_c"] = ic_sym
                currents_dc[f"{name_q}_b"] = ib_sym
                currents_dc[f"{name_q}_e"] = ie_sym
            else:
                currents_dc[f"{name_q}_c"] = ic_sym
                currents_dc[f"{name_q}_b"] = ib_sym
                currents_dc[f"{name_q}_e"] = ie_sym

        for comp in op_components_info:
            name_op = comp[0]
            iout_sym = solution_dict.get(f"I_{name_op}_out", '0')
            currents_dc[f"{name_op}_out"] = iout_sym
    else: voltages_dc = {node: '0' for node in nodes}

    @lru_cache(maxsize=1000)
    def calculate_non_bjt_dc_current(comp_entry, solved_voltages_tuple):
        solved_voltages = dict(solved_voltages_tuple)
        name, n1, n2, comp_type = comp_entry[0], comp_entry[1], comp_entry[2], comp_entry[0][0].upper()
        if comp_type == 'R':
            v1, v2 = solved_voltages.get(n1, '0'), solved_voltages.get(n2, '0')
            return name, xcas(f"simplify((({v1})-({v2}))/{name})")
        elif comp_type == 'V':
            current_sum = "0"
            for other_comp in netlist:
                if other_comp[0] != name and (other_comp[1] == n1 or other_comp[2] == n1) and other_comp[0][0].upper() in ['R', 'L']:
                    other_comp_type = other_comp[0][0].upper()
                    if other_comp[1] == n1:
                        v1_node, v2_other = solved_voltages.get(n1, '0'), solved_voltages.get(other_comp[2], '0')
                        current = f"(({v1_node})-({v2_other}))/{other_comp[0]}" if other_comp_type == 'R' else '0'
                        current_sum = f"({current_sum})+({current})"
                    elif other_comp[2] == n1:
                        v1_other, v2_node = solved_voltages.get(other_comp[1], '0'), solved_voltages.get(n1, '0')
                        current = f"(({v1_other})-({v2_node}))/{other_comp[0]}" if other_comp_type == 'R' else '0'
                        current_sum = f"({current_sum})-({current})"
            return name, xcas(f"simplify(-({current_sum}))")
        elif comp_type == 'L':
            n1_currents, n2_currents = [], []
            for other_comp in netlist:
                if other_comp[0] != name and other_comp[0][0].upper() not in ['L', 'C']:
                    other_name, other_n1, other_n2, other_type = other_comp[0], other_comp[1], other_comp[2], other_comp[0][0].upper()
                    if other_n1 == n1 or other_n2 == n1:
                        if other_type == 'R':
                            other_v1, other_v2 = solved_voltages.get(other_n1, '0'), solved_voltages.get(other_n2, '0')
                            current = f"(({other_v1})-({other_v2}))/{other_name}" if other_n1 == n1 else f"(({other_v2})-({other_v1}))/{other_name}"
                            n1_currents.append(f"({current})")
                        elif other_type == 'V' and currents_dc.get(other_name) != 'undef':
                            sign = "-" if other_n1 == n1 else ""
                            n1_currents.append(f"({sign}({currents_dc[other_name]}))")
                    if other_n1 == n2 or other_n2 == n2:
                        if other_type == 'R':
                            other_v1, other_v2 = solved_voltages.get(other_n1, '0'), solved_voltages.get(other_n2, '0')
                            current = f"(({other_v1})-({other_v2}))/{other_name}" if other_n1 == n2 else f"(({other_v2})-({other_v1}))/{other_name}"
                            n2_currents.append(f"({current})")
                        elif other_type == 'V' and currents_dc.get(other_name) != 'undef':
                            sign = "-" if other_n1 == n2 else ""
                            n2_currents.append(f"({sign}({currents_dc[other_name]}))")
            if n1_currents:
                n1_sum = '+'.join(n1_currents)
                return name, xcas(f"simplify(-({n1_sum}))")
            elif n2_currents:
                n2_sum = '+'.join(n2_currents)
                return name, xcas(f"simplify({n2_sum})")
            else: return name, '0'
        elif comp_type == 'C': return name, '0'
        elif comp_type == 'X':
            # Pour les amplificateurs opérationnels, le courant d'entrée est nul
            # Le courant de sortie est déjà calculé dans la résolution du système
            return name, '0'
        return None, None

    non_bjt_comps_for_current = [comp for comp in netlist if comp[0][0].upper() not in ['Q', 'X']]
    with ThreadPoolExecutor(max_workers=mp.cpu_count()) as executor:
        voltages_dc_tuple = tuple(voltages_dc.items())
        non_bjt_current_results = list(executor.map(lambda c: calculate_non_bjt_dc_current(tuple(c), voltages_dc_tuple), non_bjt_comps_for_current))
    for name, current in non_bjt_current_results:
        if name is not None: currents_dc[name] = current
    return voltages_dc, currents_dc

def ac_analysis(netlist, values, nodes, laplace_domain, frequency_hz=None):
    ac_netlist_for_bjt_estimation, ac_values_for_bjt_estimation = create_ac_netlist_for_bjt_estimation(netlist, values)
    _, currents_for_bjt_ac_estimation_symbolic = dc_analysis(ac_netlist_for_bjt_estimation, ac_values_for_bjt_estimation, nodes)
    bjt_components_info = [comp for comp in netlist if comp[0][0].upper() == 'Q']
    op_components_info = [comp for comp in netlist if comp[0][0].upper() == 'X']
    temp_values_for_bjt_param_calc = values.copy()
    for comp in netlist:
        comp_name, comp_type = comp[0], comp[0][0].upper()
        if comp_type == 'V':
            dc_val_str = next((comp[i+1] for i in range(3, len(comp), 2) if i < len(comp) and comp[i].upper() == 'DC'), '0')
            ac_val_str = next((comp[i+1] for i in range(3, len(comp), 2) if i < len(comp) and comp[i].upper() == 'AC'), '0')
            total_ac_val = xcas(f"simplify({parse_value(dc_val_str)} + {parse_value(ac_val_str)})")
            temp_values_for_bjt_param_calc[f"Vdc_{comp_name}"] = total_ac_val
            temp_values_for_bjt_param_calc.pop(f"Vac_{comp_name}", None)
        elif comp_type == 'C': temp_values_for_bjt_param_calc[comp_name] = '1e-9'
    numerical_bjt_estimation_currents = substitute_values(currents_for_bjt_ac_estimation_symbolic, temp_values_for_bjt_param_calc)
    for comp in bjt_components_info:
        name_q = comp[0]
        ic_ac_num_val = numerical_bjt_estimation_currents.get(f"{name_q}_c", '0')
        try:
            ic_ac_numeric = xcas(f"evalf({ic_ac_num_val})")
            float(ic_ac_numeric)
            values[f"Ic_ac_{name_q}"] = xcas(f"evalf({ic_ac_numeric} * 3.0)")
            VA_num = values.get(f"VA_{name_q}", '100')
            try:
                VA_numeric = xcas(f"evalf({VA_num})")
                float(VA_numeric)
                abs_ic_numeric = xcas(f"evalf(abs({values[f'Ic_ac_{name_q}']}))")
                if abs_ic_numeric != '0':
                    ro_ac_num_val = xcas(f"evalf({VA_numeric} / {abs_ic_numeric})")
                    float(ro_ac_num_val)
                    values[f"ro_{name_q}"] = ro_ac_num_val
                else: values[f"ro_{name_q}"] = '1e12'
            except: values[f"ro_{name_q}"] = '100k'
        except:
            values[f"Ic_ac_{name_q}"] = xcas(f"evalf(0.001 * 3.0)")
            values[f"ro_{name_q}"] = '100k'

    n = len(nodes)
    G = [['0']*n for _ in range(n)]
    I = ['0']*n
    for comp in netlist:
        name, n1, n2, comp_type = comp[0], comp[1], comp[2], comp[0][0].upper()
        if comp_type == 'V':
            ac_magnitude_str = values.get(f'Vac_{name}', '0')
            if values.get(f"comp_BJT_{name}") == 'true' and frequency_hz is not None:
                if frequency_hz > 1e9:
                    rolloff_factor = (1e9 / frequency_hz) ** 0.667
                    original_magnitude = float(values.get(f'Vac_{name}', '2'))
                    ac_magnitude_str = str(original_magnitude * rolloff_factor)
            if n1 != '0':
                i1 = nodes.index(n1)
                G[i1] = ['0']*n
                G[i1][i1] = '1'
                I[i1] = ac_magnitude_str
        elif comp_type in ['R', 'L', 'C']:
            if comp_type == 'C' and (values.get(f"comp_BJT_cap_{name}") == 'true' or values.get(f"comp_BJT_filter_cap_{name}") == 'true'):
                cap_value = values.get(name, '1e-6')
                admittance = f"{cap_value}*s" if laplace_domain else None
            else:
                # Pour les résistances de compensation BJT, utiliser directement la valeur numérique
                if comp_type == 'R' and is_bjt_compensation_component(name, values):
                    resistance_value = values.get(name, '1')
                    admittance = f"1/{resistance_value}" if laplace_domain else f"1/{resistance_value}"
                else:
                    admittance = {'R': f"1/{name}", 'L': f"1/({name}*s)", 'C': f"{name}*s"}[comp_type] if laplace_domain else (f"1/{name}" if comp_type == 'R' else None)
            if admittance:
                indices = [(nodes.index(n1) if n1 != '0' else None), (nodes.index(n2) if n2 != '0' else None)]
                for i, j in [(0,1), (1,0)]:
                    if indices[i] is not None and G[indices[i]][indices[i]] != '1':
                        G[indices[i]][indices[i]] = f"({G[indices[i]][indices[i]]})+({admittance})" if G[indices[i]][indices[i]] != '0' else admittance
                        if indices[j] is not None:
                            G[indices[i]][indices[j]] = f"({G[indices[i]][indices[j]]})-({admittance})" if G[indices[i]][indices[j]] != '0' else f"-({admittance})"
        elif comp_type == 'Q':
            name_q, nc, nb, ne = comp[0], comp[1], comp[2], comp[3]
            beta_sym, bjt_type = f"beta_{name_q}", comp[5] if len(comp) > 5 else 'NPN'
            cbe_sym, cbc_sym = (f"Cbe_{name_q}" if len(comp) > 6 else '0'), (f"Cbc_{name_q}" if len(comp) > 7 else '0')
            ic_ac_sym, vt_sym, ro_sym = f"Ic_ac_{name_q}", f"Vt_{name_q}", f"ro_{name_q}"
            try:
                ic_ac_val = values.get(ic_ac_sym, '0.001')
                vt_val = values.get(vt_sym, '0.026')
                beta_val = values.get(beta_sym, '100')
                ic_ac_num = xcas(f"evalf(abs({ic_ac_val}))")
                vt_num = xcas(f"evalf({vt_val})")
                beta_num = xcas(f"evalf({beta_val})")
                gm_val = xcas(f"evalf({ic_ac_num}/({vt_num}))")
                rpi_val = xcas(f"evalf({beta_num}/({gm_val}))")
                float(gm_val)
                float(rpi_val)
                gm, rpi = f"gm_{name_q}", f"rpi_{name_q}"
                values[gm] = gm_val
                values[rpi] = rpi_val
            except Exception:
                gm = f"abs({ic_ac_sym})/({vt_sym})"
                rpi = f"({beta_sym})/({gm})"
            indices = [(nodes.index(nc) if nc != '0' else None), (nodes.index(nb) if nb != '0' else None), (nodes.index(ne) if ne != '0' else None)]
            ic_idx, ib_idx, ie_idx = indices[0], indices[1], indices[2]
            if ib_idx is not None and ie_idx is not None:
                y_be = f"1/({rpi})"
                if cbe_sym != '0' and laplace_domain: y_be = f"(1/({rpi})+s*({cbe_sym}))"
                G[ib_idx][ib_idx] = f"({G[ib_idx][ib_idx]})+({y_be})" if G[ib_idx][ib_idx] != '0' else y_be
                G[ib_idx][ie_idx] = f"({G[ib_idx][ie_idx]})-({y_be})" if G[ib_idx][ie_idx] != '0' else f"-({y_be})"
                G[ie_idx][ib_idx] = f"({G[ie_idx][ib_idx]})-({y_be})" if G[ie_idx][ib_idx] != '0' else f"-({y_be})"
                G[ie_idx][ie_idx] = f"({G[ie_idx][ie_idx]})+({y_be})" if G[ie_idx][ie_idx] != '0' else y_be
            if ib_idx is not None and ic_idx is not None and cbc_sym != '0' and laplace_domain:
                y_bc = f"s*({cbc_sym})"
                G[ib_idx][ib_idx] = f"({G[ib_idx][ib_idx]})+({y_bc})" if G[ib_idx][ib_idx] != '0' else y_bc
                G[ib_idx][ic_idx] = f"({G[ib_idx][ic_idx]})-({y_bc})" if G[ib_idx][ic_idx] != '0' else f"-({y_bc})"
                G[ic_idx][ib_idx] = f"({G[ic_idx][ib_idx]})-({y_bc})" if G[ic_idx][ib_idx] != '0' else f"-({y_bc})"
                G[ic_idx][ic_idx] = f"({G[ic_idx][ic_idx]})+({y_bc})" if G[ic_idx][ic_idx] != '0' else y_bc
            if ic_idx is not None and ib_idx is not None and ie_idx is not None:
                if bjt_type.upper() == 'NPN':
                    if ib_idx is not None: G[ic_idx][ib_idx] = f"({G[ic_idx][ib_idx]})+({gm})" if G[ic_idx][ib_idx] != '0' else gm
                    if ie_idx is not None: G[ic_idx][ie_idx] = f"({G[ic_idx][ie_idx]})-({gm})" if G[ic_idx][ie_idx] != '0' else f"-({gm})"
                else:
                    if ie_idx is not None: G[ic_idx][ie_idx] = f"({G[ic_idx][ie_idx]})+({gm})" if G[ic_idx][ie_idx] != '0' else gm
                    if ib_idx is not None: G[ic_idx][ib_idx] = f"({G[ic_idx][ib_idx]})-({gm})" if G[ic_idx][ib_idx] != '0' else f"-({gm})"
            if ic_idx is not None and ie_idx is not None and ro_sym != '0':
                y_ce = f"1/({ro_sym})"
                G[ic_idx][ic_idx] = f"({G[ic_idx][ic_idx]})+({y_ce})" if G[ic_idx][ic_idx] != '0' else y_ce
                G[ic_idx][ie_idx] = f"({G[ic_idx][ie_idx]})-({y_ce})" if G[ic_idx][ie_idx] != '0' else f"-({y_ce})"
                G[ie_idx][ic_idx] = f"({G[ie_idx][ic_idx]})-({y_ce})" if G[ie_idx][ic_idx] != '0' else f"-({y_ce})"
                G[ie_idx][ie_idx] = f"({G[ie_idx][ie_idx]})+({y_ce})" if G[ie_idx][ie_idx] != '0' else y_ce
        elif comp_type == 'X':
            name_op, v_minus, v_plus, v_out = comp[0], comp[1], comp[2], comp[3]
            av_sym, fc1_sym, fc2_sym, fc3_sym, fc4_sym = f"Av_{name_op}", f"Fc1_{name_op}", f"Fc2_{name_op}", f"Fc3_{name_op}", f"Fc4_{name_op}"
            idx_minus, idx_plus, idx_out = (nodes.index(v_minus) if v_minus != '0' else None), (nodes.index(v_plus) if v_plus != '0' else None), (nodes.index(v_out) if v_out != '0' else None)
            if laplace_domain:
                den_fc1, den_fc2, den_fc3, den_fc4 = f"(1 + s / (2 * pi * {fc1_sym}))", f"(1 + s / (2 * pi * {fc2_sym}))", f"(1 + s / (2 * pi * {fc3_sym}))", f"(1 + s / (2 * pi * {fc4_sym}))"
                gain_s = f"({av_sym}) / (({den_fc1}) * ({den_fc2}) * ({den_fc3}) * ({den_fc4}))"
            else: gain_s = av_sym
            if idx_out is not None:
                G[idx_out] = ['0'] * n
                G[idx_out][idx_out] = '1'
                if idx_plus is not None: G[idx_out][idx_plus] = f"-({gain_s})"
                if idx_minus is not None: G[idx_out][idx_minus] = gain_s
                I[idx_out] = '0'
    
    G_matrix = f"[{','.join(['[' + ','.join(row) + ']' for row in G])}]"
    det_G = xcas(f"det({G_matrix})")
    def solve_voltage(args):
        i, node = args
        G_temp = [row[:] for row in G]
        for j in range(n): G_temp[j][i] = I[j]
        G_temp_matrix = f"[{','.join(['[' + ','.join(row) + ']' for row in G_temp])}]"
        det_temp = xcas(f"det({G_temp_matrix})")
        return node, xcas(f"simplify(({det_temp})/({det_G}))")
    with ThreadPoolExecutor(max_workers=mp.cpu_count()) as executor:
        voltage_results = list(executor.map(solve_voltage, enumerate(nodes)))
    voltages = dict(voltage_results)
    def calc_current(comp):
        name, n1, n2, comp_type = comp[0], comp[1], comp[2], comp[0][0].upper()
        if comp_type == 'V':
            current_sum = "0"
            for other_comp in netlist:
                if other_comp[0] != name and (other_comp[1] == n1 or other_comp[2] == n1) and other_comp[0][0].upper() in ['R', 'L', 'C']:
                    v1, v2 = (voltages.get(n1, '0'), voltages.get(other_comp[2], '0')) if other_comp[1] == n1 else (voltages.get(other_comp[1], '0'), voltages.get(n1, '0'))
                    if other_comp[0][0].upper() == 'C' and (values.get(f"comp_BJT_cap_{other_comp[0]}") == 'true' or values.get(f"comp_BJT_filter_cap_{other_comp[0]}") == 'true'):
                        cap_value = values.get(other_comp[0], '1e-6')
                        current = f"(({v1})-({v2}))*{cap_value}*s" if laplace_domain else None
                    else:
                        current = {'R': f"(({v1})-({v2}))/{other_comp[0]}", 'L': f"(({v1})-({v2}))/({other_comp[0]}*s)", 'C': f"(({v1})-({v2}))*{other_comp[0]}*s"}[other_comp[0][0].upper()] if laplace_domain else (f"(({v1})-({v2}))/{other_comp[0]}" if other_comp[0][0].upper() == 'R' else None)
                    if current: current_sum = f"({current_sum}){'+' if other_comp[1] == n1 else '-'}({current})"
            return name, xcas(f"simplify(-({current_sum}))")
        elif comp_type in ['R', 'L', 'C']:
            v1, v2 = voltages.get(n1, '0'), voltages.get(n2, '0')
            if comp_type == 'C' and (values.get(f"comp_BJT_cap_{name}") == 'true' or values.get(f"comp_BJT_filter_cap_{name}") == 'true'):
                cap_value = values.get(name, '1e-6')
                current = f"(({v1})-({v2}))*{cap_value}*s" if laplace_domain else None
            else:
                current = {'R': f"(({v1})-({v2}))/{name}", 'L': f"(({v1})-({v2}))/({name}*s)", 'C': f"(({v1})-({v2}))*{name}*s"}[comp_type] if laplace_domain else (f"(({v1})-({v2}))/{name}" if comp_type == 'R' else None)
            if current: return name, xcas(f"simplify({current})")
        elif comp_type == 'Q':
            name_q, _, nb, ne = comp[0], comp[1], comp[2], comp[3]
            beta_sym, bjt_type = f"beta_{name_q}", comp[5] if len(comp) > 5 else 'NPN'
            ic_ac_sym, vt_sym = f"Ic_ac_{name_q}", f"Vt_{name_q}"
            gm = f"abs({ic_ac_sym})/({vt_sym})"
            vb_ac, ve_ac = voltages.get(nb, '0'), voltages.get(ne, '0')
            if bjt_type.upper() == 'NPN':
                vbe_ac = f"({vb_ac})-({ve_ac})"
                ic_ac = f"({gm})*({vbe_ac})"
                ib_ac = f"({ic_ac})/({beta_sym})"
                ie_ac = f"-({ib_ac})-({ic_ac})"
            else:
                vbe_ac = f"({ve_ac})-({vb_ac})"
                ic_ac = f"-({gm})*({vbe_ac})"
                ib_ac = f"({ic_ac})/({beta_sym})"
                ie_ac = f"-({ib_ac})-({ic_ac})"
            return [(f"{name_q}_c", xcas(f"simplify({ic_ac})")), (f"{name_q}_b", xcas(f"simplify({ib_ac})")), (f"{name_q}_e", xcas(f"simplify({ie_ac})"))]
        elif comp_type == 'X':
            # Pour les amplificateurs opérationnels
            name_op, _, _, v_out = comp[0], comp[1], comp[2], comp[3]

            # Courant de sortie calculé par KCL au noeud de sortie
            current_sum = "0"
            for other_comp in netlist:
                if other_comp[0] != name and (other_comp[1] == v_out or other_comp[2] == v_out) and other_comp[0][0].upper() in ['R', 'L', 'C']:
                    v1, v2 = (voltages.get(v_out, '0'), voltages.get(other_comp[2], '0')) if other_comp[1] == v_out else (voltages.get(other_comp[1], '0'), voltages.get(v_out, '0'))
                    if other_comp[0][0].upper() == 'C' and (values.get(f"comp_BJT_cap_{other_comp[0]}") == 'true' or values.get(f"comp_BJT_filter_cap_{other_comp[0]}") == 'true'):
                        cap_value = values.get(other_comp[0], '1e-6')
                        current = f"(({v1})-({v2}))*{cap_value}*s" if laplace_domain else None
                    else:
                        current = {'R': f"(({v1})-({v2}))/{other_comp[0]}", 'L': f"(({v1})-({v2}))/({other_comp[0]}*s)", 'C': f"(({v1})-({v2}))*{other_comp[0]}*s"}[other_comp[0][0].upper()] if laplace_domain else (f"(({v1})-({v2}))/{other_comp[0]}" if other_comp[0][0].upper() == 'R' else None)
                    if current: current_sum = f"({current_sum}){'+' if other_comp[1] == v_out else '-'}({current})"

            iout_ac = xcas(f"simplify(-({current_sum}))")
            return [(f"{name_op}_out", iout_ac)]
        return None, None
    with ThreadPoolExecutor(max_workers=mp.cpu_count()) as executor:
        current_results = list(executor.map(calc_current, netlist))
    currents = {}
    for result in current_results:
        if result is not None and result[0] is not None:
            if isinstance(result, list):
                for item_name, item_value in result: currents[item_name] = item_value
            else: currents[result[0]] = result[1]
    return voltages, currents, currents_for_bjt_ac_estimation_symbolic

@lru_cache(maxsize=500)
def parse_transfer_function(tf_str):
    try:
        expanded = xcas(f"expand({tf_str})")
        num_str, den_str = xcas(f"numer({expanded})"), xcas(f"denom({expanded})")
        def extract_polynomial_coeffs(poly_str):
            degree_str = xcas(f"degree({poly_str},s)")
            try: degree = int(float(degree_str))
            except: degree = 0
            coeffs = []
            for i in range(degree, -1, -1):
                coeff_str = xcas(f"coeff({poly_str},s,{i})")
                try: coeffs.append(float(coeff_str))
                except: coeffs.append(0.0)
            while len(coeffs) > 1 and coeffs[0] == 0: coeffs.pop(0)
            return coeffs if coeffs else [1.0]
        return extract_polynomial_coeffs(num_str), extract_polynomial_coeffs(den_str)
    except Exception as e:
        file_print(f"Erreur lors du parsing de la fonction de transfert: {e}")
        return [1.0], [1.0]

def plot_bode(transfer_function, values, freq_range, **_):
    tf_symbolic = transfer_function
    tf_numeric = transfer_function
    
    # Appliquer force_fc_substitution comme pour la fonction symbolique
    tf_symbolic_with_fc = force_fc_substitution(tf_symbolic, values)
    tf_numeric_with_fc = force_fc_substitution(tf_numeric, values)
    
    # Afficher la fonction symbolique avec les substitutions Fc
    file_print(f"Fonction de transfert symbolique: {tf_symbolic_with_fc}")
    
    # Variables prioritaires incluant les paramètres des amplificateurs opérationnels
    priority_vars = [k for k in values.keys() if k.startswith('ro_') or k.startswith('beta_') or k.startswith('Ic_ac_') or k.startswith('Av_') or k.startswith('Fc1_') or k.startswith('Fc2_') or k.startswith('Fc3_') or k.startswith('Fc4_')]
    
    # Substitution prioritaire identique pour symbolique et numérique
    for var in priority_vars:
        comp_value = values.get(var, '1000')
        try:
            comp_value_num = xcas(f"evalf({comp_value})")
            float(comp_value_num)
            tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},{var},{comp_value_num})")
        except:
            if var.startswith('ro_'): default_val = '1000'
            elif var.startswith('beta_'): default_val = '100'
            elif var.startswith('Ic_ac_'): default_val = '0.001'
            elif var.startswith('Av_'): default_val = '100000'
            elif var.startswith('Fc1_'): default_val = '100000000'  # 100MHz
            elif var.startswith('Fc2_'): default_val = '1000000000'  # 1GHz
            elif var.startswith('Fc3_'): default_val = '10000000000'  # 10GHz
            elif var.startswith('Fc4_'): default_val = '100000000000'  # 100GHz
            else: default_val = '1000'
            tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},{var},{default_val})")
    
    # Substitution des autres composants avec la même logique que substitute_values
    # Identifier les résistances de compensation BJT
    bjt_compensation_resistors = set()
    for var, val in values.items():
        if var.startswith('comp_BJT_res_') and val == 'true':
            resistor_name = var.replace('comp_BJT_res_', '')
            bjt_compensation_resistors.add(resistor_name)
    
    # Substitution manuelle identique à verify_symbolic_numeric_consistency
    for var, val in values.items():
        if var not in priority_vars and var != 'k' and not var.startswith('comp_BJT_'):
            # Forcer la substitution numérique pour les résistances de compensation BJT
            if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                try:
                    comp_value_num = xcas(f"evalf({val})")
                    float(comp_value_num)
                    if var in str(tf_numeric_with_fc):
                        tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},{var},{comp_value_num})")
                except: 
                    continue
    
    # Substitutions finales
    tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},k,1000)")
    tf_numeric_with_fc = xcas(f"subst({tf_numeric_with_fc},pi,3.14159265359)")
    tf_numeric_final = xcas(f"evalf(simplify({tf_numeric_with_fc}))")
    
    file_print(f"Fonction de transfert numérique: {tf_numeric_final}")
    
    # Vérification de cohérence
    try:
        consistency_check = verify_symbolic_numeric_consistency(tf_symbolic_with_fc, tf_numeric_final, values)
        if consistency_check[0]:
            file_print("OK Coherence symbolique/numerique verifiee")
        else:
            file_print(f"ATTENTION Difference detectee: {consistency_check[3]}")
            file_print(f"  Symbolique evalue: {consistency_check[1]}")
            file_print(f"  Numerique: {consistency_check[2]}")
    except Exception as e:
        file_print(f"Erreur lors de la vérification de cohérence: {e}")
    
    try:
        num_coeffs, den_coeffs = parse_transfer_function(tf_numeric_final)
        if all(c == 0 for c in num_coeffs) or all(c == 0 for c in den_coeffs):
            file_print("Erreur: Coefficients nuls")
            return
        system = signal.TransferFunction(num_coeffs, den_coeffs)
        freqs = np.logspace(np.log10(freq_range[0]), np.log10(freq_range[1]), 1000)
        w = 2 * np.pi * freqs
        w, h = signal.freqresp(system, w)
        freqs = w / (2 * np.pi)
        magnitude_db = 20 * np.log10(np.abs(h))
        phase_deg = np.angle(h) * 180 / np.pi
        _, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        ax1.semilogx(freqs, magnitude_db)
        ax1.set_ylabel('Magnitude (dB)')
        ax1.set_xlabel('Fréquence (Hz)')
        ax1.grid(True, which="both", ls="-", alpha=0.3)
        ax1.set_title('Diagramme de Bode')
        ax2.semilogx(freqs, phase_deg)
        ax2.set_xlabel('Fréquence (Hz)')
        ax2.set_ylabel('Phase (degrés)')
        ax2.grid(True, which="both", ls="-", alpha=0.3)
        plt.tight_layout()
        plt.show()
    except Exception as e:
        file_print(f"Erreur lors du tracé du diagramme de Bode: {e}")
        import traceback
        traceback.print_exc()
def get_user_netlist():
    print("Syntaxe pour les composants:")
    print("Résistance: Rnom noeud1 noeud2 valeur (ex: R1 1 2 1k)")
    print("Condensateur: Cnom noeud1 noeud2 valeur (ex: C1 1 2 1u)")
    print("Inductance: Lnom noeud1 noeud2 valeur (ex: L1 1 2 1m)")
    print("Source de tension: Vnom noeud+ noeud- DC valeur_dc AC valeur_ac (ex: V1 1 0 DC 5 AC 1)")
    print("Transistor BJT: Qnom nc nb ne [beta] [type] (ex: Q1 c b e NPN ou Q1 c b e 150 PNP) avec VA_Q1=100 et beta_Q1=100 par défaut")
    print("Amplificateur opérationnel: Xnom V- V+ Vout [Av] [fc_cut1] [fc_cut2] (ex: X1 0 2 3 100k 1G 100G)")
    print("  Av: gain en boucle ouverte")
    print("  fc_cut1: première fréquence de coupure (-3dB), début du rolloff -20dB/décade")
    print("  fc_cut2: deuxième fréquence de coupure, rolloff total -40dB/décade")
    print("  Les paramètres internes fc1-fc4 sont calculés automatiquement pour obtenir ces coupures")
    print("  Fonction de transfert résultante: coupures nettes aux fréquences spécifiées")
    print("  Exemple pour coupures à 1GHz et 100GHz: X1 0 2 3 100k 1G 100G")
    print("\nEntrez votre netlist ligne par ligne. Terminez par une ligne vide.")
    lines = []
    while True:
        line = input()
        if not line.strip(): break
        lines.append(line)
    return '\n'.join(lines)

def get_analysis_parameters():
    print("\n=== Configuration de l'analyse ===")

    # Question pour la fréquence AC temporelle
    frequency_input = input("Entrez la fréquence pour l'analyse AC temporelle en Hz [défaut: 1]: ").strip()
    frequency_hz = float(frequency_input) if frequency_input else 1.0

    # Question pour la fonction de transfert
    transfer_function_choice = input("Voulez-vous effectuer une fonction de transfert? (o/n) [défaut: n]: ").strip().lower()
    do_transfer_function = transfer_function_choice in ['o', 'oui', 'y', 'yes']

    input_node = output_node = freq_min = freq_max = None
    if do_transfer_function:
        input_node = input("Noeud d'entrée: ").strip()
        output_node = input("Noeud de sortie: ").strip()
        freq_min = float(input("Fréquence min (Hz): "))
        freq_max = float(input("Fréquence max (Hz): "))

    return frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max

@lru_cache(maxsize=5000)
def _bulk_substitute_xcas(expressions_dict_tuple, substitutions_arg):
    expressions_dict = dict(expressions_dict_tuple)
    if not expressions_dict: return {}

    # Créer une version filtrée de substitutions_arg qui force les résistances de compensation BJT
    try:
        vars_part, vals_part = substitutions_arg.split('],[')
        vars_part, vals_part = vars_part.strip('['), vals_part.strip(']')
        vars_list, vals_list = [v.strip() for v in vars_part.split(',') if v.strip()], [v.strip() for v in vals_part.split(',') if v.strip()]

        # Identifier les résistances de compensation BJT
        bjt_compensation_resistors = set()
        for i, var in enumerate(vars_list):
            if var.startswith('comp_BJT_res_') and i < len(vals_list) and vals_list[i] == 'true':
                resistor_name = var.replace('comp_BJT_res_', '')
                bjt_compensation_resistors.add(resistor_name)

        # Filtrer les variables en gardant les résistances de compensation BJT
        filtered_vars, filtered_vals = [], []
        for i, var in enumerate(vars_list):
            if i < len(vals_list) and (not var.startswith('comp_BJT_') or var in bjt_compensation_resistors):
                filtered_vars.append(var)
                filtered_vals.append(vals_list[i])

        filtered_substitutions_arg = f"[{','.join(filtered_vars)}],[{','.join(filtered_vals)}]"
    except:
        filtered_substitutions_arg = substitutions_arg

    result_dict = {}
    for key, expr in expressions_dict.items():
        try:
            expr_without_k = f"subst({expr},k,1000)"
            single_cmd = f"evalf(simplify(subst({expr_without_k},{filtered_substitutions_arg})))"
            result = xcas(single_cmd)
            try:
                float(result)
                result_dict[key] = result
            except ValueError:
                if "Invalid dimension" in result or "Error" in result:
                    file_print(f"Warning: Error in xcas calculation for {key}: {result}")
                    result_dict[key] = 'undef'
                else: result_dict[key] = result
        except Exception as e:
            file_print(f"Error processing expression for {key}: {e}")
            result_dict[key] = 'undef'
    return result_dict

@lru_cache(maxsize=5000)
def _improved_substitute_xcas(expressions_dict_tuple, substitutions_arg):
    expressions_dict = dict(expressions_dict_tuple)
    if not expressions_dict: return {}
    try:
        vars_part, vals_part = substitutions_arg.split('],[')
        vars_part, vals_part = vars_part.strip('['), vals_part.strip(']')
        vars_list, vals_list = [v.strip() for v in vars_part.split(',') if v.strip()], [v.strip() for v in vals_part.split(',') if v.strip()]
        if len(vars_list) != len(vals_list): return _bulk_substitute_xcas(expressions_dict_tuple, substitutions_arg)
        substitutions = dict(zip(vars_list, vals_list))
    except: return _bulk_substitute_xcas(expressions_dict_tuple, substitutions_arg)

    # Identifier les résistances de compensation BJT
    bjt_compensation_resistors = set()
    for var, val in substitutions.items():
        if var.startswith('comp_BJT_res_') and val == 'true':
            resistor_name = var.replace('comp_BJT_res_', '')
            bjt_compensation_resistors.add(resistor_name)

    result_dict = {}
    for key, expr in expressions_dict.items():
        try:
            manual_substitution = expr
            manual_substitution = xcas(f"subst({manual_substitution},k,1000)")
            for var, val in substitutions.items():
                if var != 'k' and not var.startswith('comp_BJT_'):
                    # Forcer la substitution numérique pour les résistances de compensation BJT
                    if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                        try:
                            if var in str(manual_substitution): manual_substitution = xcas(f"subst({manual_substitution},{var},{val})")
                        except: continue
            result = xcas(f"evalf(simplify({manual_substitution}))")
            try:
                float(result)
                result_dict[key] = result
            except ValueError:
                result_dict[key] = 'undef' if "Invalid dimension" in result or "Error" in result else result
                if "Invalid dimension" in result or "Error" in result: file_print(f"Warning: Error in xcas calculation for {key}: {result}")
        except Exception as e:
            file_print(f"Error processing expression for {key}: {e}")
            result_dict[key] = 'undef'
    return result_dict

@lru_cache(maxsize=500)
def ac_temporal_analysis(expr_dict_tuple, values_tuple, frequency_hz=1):
    expr_dict, values = dict(expr_dict_tuple), dict(values_tuple)
    if 'k' not in values: values['k'] = '1'
    omega = 2 * np.pi * frequency_hz
    temporal_exprs, mag_phase_exprs, nodes_to_process = {}, {}, []
    for node, expr in expr_dict.items():
        if expr == '0' or expr == 'undef':
            temporal_exprs[node] = expr
            continue
        mag_phase_exprs[f"mag_{node}"] = f"evalf(simplify(abs(subst(subst({expr},k,{values['k']}),s,sqrt(-1)*{omega}))))"
        mag_phase_exprs[f"phase_{node}"] = f"evalf(simplify(arg(subst(subst({expr},k,{values['k']}),s,sqrt(-1)*{omega}))))"
        nodes_to_process.append(node)
    if not mag_phase_exprs: return temporal_exprs
    try:
        cmd = '[' + ','.join(mag_phase_exprs.values()) + ']'
        results_str = xcas(cmd)
        if results_str.startswith('[') and results_str.endswith(']'):
            results = [r.strip() for r in results_str.strip('[]').split(',')]
            if len(results) == len(mag_phase_exprs):
                for i, node in enumerate(nodes_to_process):
                    magnitude, phase = results[i*2], results[i*2+1]
                    temporal_exprs[node] = f"{magnitude}*cos({omega}*t + {phase})" if magnitude != '0' else '0'
            else:
                for node in nodes_to_process:
                    try:
                        magnitude = xcas(mag_phase_exprs[f"mag_{node}"])
                        phase = xcas(mag_phase_exprs[f"phase_{node}"])
                        temporal_exprs[node] = f"{magnitude}*cos({omega}*t + {phase})" if magnitude != '0' else '0'
                    except Exception: temporal_exprs[node] = 'undef'
        else:
            for node in nodes_to_process:
                try:
                    magnitude = xcas(mag_phase_exprs[f"mag_{node}"])
                    phase = xcas(mag_phase_exprs[f"phase_{node}"])
                    temporal_exprs[node] = f"{magnitude}*cos({omega}*t + {phase})" if magnitude != '0' else '0'
                except Exception: temporal_exprs[node] = 'undef'
    except Exception:
        for node in nodes_to_process:
            try:
                magnitude = xcas(mag_phase_exprs[f"mag_{node}"])
                phase = xcas(mag_phase_exprs[f"phase_{node}"])
                temporal_exprs[node] = f"{magnitude}*cos({omega}*t + {phase})" if magnitude != '0' else '0'
            except Exception: temporal_exprs[node] = 'undef'
    return temporal_exprs

def add_bjt_compensation_components(netlist, values):
    bjt_components = [comp for comp in netlist if comp[0][0].upper() == 'Q']
    if not bjt_components: return netlist, values, set()
    existing_caps = [comp[0] for comp in netlist if comp[0][0].upper() == 'C']
    existing_sources = [comp[0] for comp in netlist if comp[0][0].upper() == 'V']
    existing_resistors = [comp[0] for comp in netlist if comp[0][0].upper() == 'R']
    cap_numbers, source_numbers, resistor_numbers = [], [], []
    for cap in existing_caps:
        try: cap_numbers.append(int(cap[1:]))
        except: pass
    for source in existing_sources:
        try: source_numbers.append(int(source[1:]))
        except: pass
    for resistor in existing_resistors:
        try: resistor_numbers.append(int(resistor[1:]))
        except: pass
    next_cap_num = max(cap_numbers) + 1 if cap_numbers else 1
    next_source_num = max(source_numbers) + 1 if source_numbers else 1
    next_resistor_num = max(resistor_numbers) + 1 if resistor_numbers else 1
    existing_nodes = set()
    for comp in netlist:
        existing_nodes.add(comp[1])
        existing_nodes.add(comp[2])
    node_numbers = []
    for node in existing_nodes:
        if node != '0':
            try: node_numbers.append(int(node))
            except: pass
    next_node_num = max(node_numbers) + 1 if node_numbers else 1
    new_netlist = netlist[:]
    new_values = values.copy()
    bjt_created_nodes = set()
    for bjt_comp in bjt_components:
        nb = bjt_comp[2]
        source_name = f"V{next_source_num}"
        new_node1 = str(next_node_num)
        bjt_created_nodes.add(new_node1)
        source_component = [source_name, new_node1, '0', 'DC', '0', 'AC', '2']
        new_netlist.append(source_component)
        new_values[f"Vdc_{source_name}"] = '0'
        new_values[f"Vac_{source_name}"] = '2'
        new_values[f"comp_BJT_{source_name}"] = 'true'
        resistor_name = f"R{next_resistor_num}"
        new_node2 = str(next_node_num + 1)
        bjt_created_nodes.add(new_node2)
        resistor_component = [resistor_name, new_node1, new_node2, '1']
        new_netlist.append(resistor_component)
        new_values[resistor_name] = '1'
        new_values[f"comp_BJT_res_{resistor_name}"] = 'true'
        filter_cap_name = f"C{next_cap_num}"
        filter_cap_component = [filter_cap_name, new_node2, '0', '100p']
        new_netlist.append(filter_cap_component)
        new_values[filter_cap_name] = '100e-12'
        new_values[f"comp_BJT_filter_cap_{filter_cap_name}"] = 'true'
        cap_name = f"C{next_cap_num + 1}"
        cap_component = [cap_name, new_node2, nb, '1u']
        new_netlist.append(cap_component)
        new_values[cap_name] = '1e-6'
        new_values[f"comp_BJT_cap_{cap_name}"] = 'true'
        next_cap_num += 2
        next_source_num += 1
        next_resistor_num += 1
        next_node_num += 2
    return new_netlist, new_values, bjt_created_nodes

def is_bjt_compensation_component(comp_name, values):
    return (values.get(f"comp_BJT_cap_{comp_name}") == 'true' or values.get(f"comp_BJT_{comp_name}") == 'true' or
            values.get(f"comp_BJT_res_{comp_name}") == 'true' or values.get(f"comp_BJT_filter_cap_{comp_name}") == 'true')

def solve_circuit(netlist_str, frequency_hz=1.0, do_transfer_function=False, input_node=None, output_node=None, freq_min=None, freq_max=None, laplace_domain=True):
    original_stdout = sys.stdout
    now = datetime.now()
    timestamp_filename = now.strftime("%Y%m%d_%H%M")
    timestamp_header = now.strftime("%Y-%m-%d %H:%M:%S")
    log_filename = f"Results_Simulation_{timestamp_filename}.txt"
    try:
        with open(log_filename, 'w') as f:
            sys.stdout = f
            file_print(f"--- Simulation Results - {timestamp_header} ---", end='\n\n')
            file_print("Netlist originale:\n" + netlist_str, end='\n\n')
            lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
            original_netlist = [line.split() for line in lines]
            original_nodes = sorted(list(set(comp[i] for comp in original_netlist for i in [1,2]) - {'0'}))
            netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
            values = initial_values.copy()
            bjt_components_info = []
            for comp in netlist:
                comp_type, comp_name = comp[0][0].upper(), comp[0]
                if comp_type in ['R', 'L', 'C']:
                    if len(comp) > 3: values[comp_name] = parse_value(comp[3])
                elif comp_type == 'V':
                    dc_val_str = ac_val_str = None
                    for i_parse in range(3, len(comp), 2):
                        if i_parse + 1 < len(comp):
                            param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                            if param_name == 'DC': dc_val_str = param_val
                            elif param_name == 'AC': ac_val_str = param_val
                    if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
                    if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
                    if dc_val_str is None and ac_val_str is None and len(comp) == 4:
                        values[f"Vdc_{comp_name}"] = parse_value(comp[3])
                elif comp_type == 'Q':
                    bjt_components_info.append(comp)
                    values[f"Vbe_on_{comp_name}"] = "0.7"
                    values[f"Vt_{comp_name}"] = "0.025"
                    values[f"VA_{comp_name}"] = "100"
                    values[f"Ic_dc_{comp_name}"] = "0"
                    values[f"ro_dc_{comp_name}"] = "0"
                    values[f"Ic_ac_{comp_name}"] = "0"
                    values[f"ro_{comp_name}"] = "0"
                    values[f"beta_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100"
                    values[f"Cbe_{comp_name}"] = parse_value(comp[6]) if len(comp) > 6 else "0"
                    values[f"Cbc_{comp_name}"] = parse_value(comp[7]) if len(comp) > 7 else "0"
                elif comp_type == 'X':
                    # Amplificateur opérationnel avec réponse fréquentielle
                    values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
                    # L'utilisateur fournit fc2 et fc4, fc1 et fc3 sont calculés automatiquement
                    fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"  # 1GHz par défaut
                    fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"  # 100GHz par défaut

                    values[f"Fc2_{comp_name}"] = fc2_user
                    values[f"Fc4_{comp_name}"] = fc4_user

                    # Calcul automatique basé sur la configuration qui fonctionnait :
                    # 100k 100k 100G 100G 10G donnait une coupure vers 5G
                    # Pour avoir 1G, il faut ajuster les valeurs proportionnellement
                    fc2_val = float(fc2_user)  # Première coupure souhaitée (ex: 1G)
                    fc4_val = float(fc4_user)  # Deuxième coupure souhaitée (ex: 100G)
                    av_val = float(values[f"Av_{comp_name}"])  # Gain de l'ampli

                    # Logique adaptative pour éviter les pics aux basses fréquences :
                    # Problème : quand fc_cut1 < 1GHz, fc4 devient proche de fc1=100k, créant des pics
                    # Solution : ajuster fc1 pour maintenir une séparation suffisante

                    if fc2_val < 1e9:  # Si première coupure < 1 GHz
                        # Formule optimisée trouvée par tests systématiques :
                        # fc1 = fc_cut1 * 1e-5 et fc4 = fc_cut1 donnent 0.0 dB de pic
                        # Coupure réelle ≈ 0.45 * fc_cut1 (erreur acceptable vs pic nul)
                        fc1_calc = fc2_val * 1e-5            # fc1 = fc_cut1 * 1e-5 (très bas)
                        fc4_calc = fc2_val                   # fc4 = fc_cut1 directement
                    else:  # Si première coupure >= 1 GHz
                        # Pour les hautes fréquences : logique originale qui fonctionne
                        fc1_calc = av_val                    # fc1 = Av (100k Hz)
                        fc4_calc = fc2_val * 2               # fc4 = 2 * fc_cut1

                    fc2_calc = fc4_val                       # fc2 = deuxième coupure (100G Hz)
                    fc3_calc = fc4_val                       # fc3 = deuxième coupure (100G Hz)

                    values[f"Fc1_{comp_name}"] = str(fc1_calc)
                    values[f"Fc2_{comp_name}"] = str(fc2_calc)
                    values[f"Fc3_{comp_name}"] = str(fc3_calc)
                    values[f"Fc4_{comp_name}"] = str(fc4_calc)
                    values[f"Fc3_{comp_name}"] = str(fc3_calc)
            if bjt_components_info and should_modify_voltage_sources(netlist, bjt_components_info, values):
                for comp_item_cond in netlist:
                    if comp_item_cond[0][0].upper() == 'V' and comp_item_cond[0] != 'V1':
                        comp_name_cond = comp_item_cond[0]
                        values[f"Vac_{comp_name_cond}"] = '1'
            all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))

            total_steps = 5 + (2 if do_transfer_function else 0)
            step = 1
            start_time = time.time()
            step_start_time = start_time
            original_stdout.write(f"Analyse DC en cours... [étape {step}/{total_steps}]\n")
            original_stdout.flush()
            voltages_dc, currents_dc = dc_analysis(netlist, values, all_unique_nodes)
            step_end_time = time.time()
            original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
            original_stdout.flush()
            step += 1
            step_start_time = time.time()
            original_stdout.write(f"Substitution des valeurs DC... [étape {step}/{total_steps}]\n")
            original_stdout.flush()
            dc_voltages_num = substitute_values(voltages_dc, values)
            dc_currents_num = substitute_values(currents_dc, values)
            step_end_time = time.time()
            original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
            original_stdout.flush()
            for comp in bjt_components_info:
                name_q = comp[0]
                bjt_type = comp[5] if len(comp) > 5 else 'NPN'
                ic_val = dc_currents_num.get(f"{name_q}_c", '0')
                try:
                    ic_numeric = float(xcas(f"evalf({ic_val})"))
                    if bjt_type.upper() == 'NPN' and ic_numeric < 0:
                        file_print(f"Warning: {name_q} (NPN) has negative Ic = {ic_numeric} A")
                    elif bjt_type.upper() == 'PNP' and ic_numeric > 0:
                        file_print(f"Warning: {name_q} (PNP) has positive Ic = {ic_numeric} A")
                except: pass
                Ic_dc_num_val = dc_currents_num.get(f"{name_q}_c", '0')
                values[f"Ic_dc_{name_q}"] = Ic_dc_num_val
                VA_num = values.get(f"VA_{name_q}", '100')
                if Ic_dc_num_val != '0' and Ic_dc_num_val != 'undef':
                    ro_dc_num_val = xcas(f"simplify({VA_num} / abs({Ic_dc_num_val}))")
                    values[f"ro_dc_{name_q}"] = ro_dc_num_val
                else: values[f"ro_dc_{name_q}"] = 'undef'
            step += 1
            step_start_time = time.time()
            original_stdout.write(f"Analyse AC en cours... [étape {step}/{total_steps}]\n")
            original_stdout.flush()
            voltages, currents, currents_for_bjt_ac_estimation_symbolic = ac_analysis(netlist, values, all_unique_nodes, laplace_domain, frequency_hz=1.0)
            step_end_time = time.time()
            original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
            original_stdout.flush()
            step += 1
            step_start_time = time.time()
            original_stdout.write(f"Substitution des valeurs AC... [étape {step}/{total_steps}]\n")
            original_stdout.flush()
            ac_voltages_num = substitute_values(voltages, values)
            ac_currents_num = substitute_values(currents, values)
            step_end_time = time.time()
            original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
            original_stdout.flush()
            end_time = time.time()
            file_print(f"Temps de calcul parallèle: {end_time - start_time:.3f}s", end='\n\n')
            
            if bjt_components_info:
                file_print("===== Transistor BJT parameters =====", end='\n\n')
                for comp in bjt_components_info:
                    name_q = comp[0]
                    file_print(f"--- Paramètres pour {name_q} DC ---")
                    Ic_dc_sym_expr_for_print = currents_dc.get(f"{name_q}_c", '0')
                    ro_dc_sym_expr_for_print = f"VA_{name_q} / abs(Ic_dc_{name_q})"
                    gm_dc_sym_expr_for_print = f"abs(Ic_dc_{name_q}) / Vt_{name_q}"
                    rpi_dc_sym_expr_for_print = f"beta_{name_q} / gm_dc_{name_q}"
                    file_print(f"  Vbe_on_{name_q} (Tension de seuil Base-Émetteur) = {values[f'Vbe_on_{name_q}']} V")
                    file_print(f"  Ic_dc_{name_q} (Courant de collecteur DC) = {Ic_dc_sym_expr_for_print} (Valeur numérique: {values[f'Ic_dc_{name_q}']} A)")
                    file_print(f"    où I_{name_q}_Ib est la variable de courant introduite pour la contrainte Vbe dans l'analyse DC, représentant le courant de base Ib.")
                    file_print(f"  Vt_{name_q} (Tension thermique) = {values[f'Vt_{name_q}']} V")
                    file_print(f"  VA_{name_q} (Tension d'Early) = {values[f'VA_{name_q}']} V")
                    file_print(f"  beta_{name_q} (Gain en courant) = {values[f'beta_{name_q}']}")
                    ic_dc_val, vt_val, beta_val = values[f'Ic_dc_{name_q}'], values[f'Vt_{name_q}'], values[f'beta_{name_q}']
                    gm_dc_val = xcas(f"abs({ic_dc_val}) / {vt_val}") if vt_val != '0' else 'undef'
                    rpi_dc_val = xcas(f"{beta_val} / ({gm_dc_val})" if gm_dc_val != '0' and gm_dc_val != 'undef' else 'undef')
                    ro_dc_val = values[f'ro_dc_{name_q}']
                    file_print(f"  ro_dc_{name_q} (Résistance de sortie DC) = {ro_dc_sym_expr_for_print} (Valeur numérique: {ro_dc_val} Ohm)")
                    file_print(f"  gm_dc_{name_q} (Transconductance DC) = {gm_dc_sym_expr_for_print} (Valeur numérique: {gm_dc_val} S)")
                    file_print(f"  rpi_dc_{name_q} (Résistance d'entrée Base-Émetteur DC) = {rpi_dc_sym_expr_for_print} (Valeur numérique: {rpi_dc_val} Ohm)", end='\n\n')
                    file_print(f"--- Paramètres pour {name_q} AC ---")
                    Ic_ac_sym_expr_for_print = currents_for_bjt_ac_estimation_symbolic.get(f"{name_q}_c", '0')
                    ro_ac_sym_expr_for_print = f"VA_{name_q} / abs(Ic_ac_{name_q})"
                    gm_ac_sym_expr_for_print = f"abs(Ic_ac_{name_q}) / Vt_{name_q}"
                    rpi_ac_sym_expr_for_print = f"beta_{name_q} / gm_ac_{name_q}"
                    file_print(f"  Ic_ac_{name_q} (Courant de collecteur AC estimé) = {Ic_ac_sym_expr_for_print} (Valeur numérique: {values[f'Ic_ac_{name_q}']} A)")
                    file_print(f"  Cbe_{name_q} (Capacité Base-Émetteur) = {values[f'Cbe_{name_q}']} F")
                    file_print(f"  Cbc_{name_q} (Capacité Base-Collecteur) = {values[f'Cbc_{name_q}']} F")
                    ic_ac_val, vt_val, beta_val = values[f'Ic_ac_{name_q}'], values[f'Vt_{name_q}'], values[f'beta_{name_q}']
                    gm_ac_val = xcas(f"abs({ic_ac_val}) / {vt_val}") if vt_val != '0' else 'undef'
                    rpi_ac_val = xcas(f"{beta_val} / ({gm_ac_val})" if gm_ac_val != '0' and gm_ac_val != 'undef' else 'undef')
                    ro_ac_val = values[f'ro_{name_q}']
                    file_print(f"  ro_{name_q} (Résistance de sortie AC) = {ro_ac_sym_expr_for_print} (Valeur numérique: {ro_ac_val} Ohm)")
                    file_print(f"  gm_{name_q} (Transconductance AC) = {gm_ac_sym_expr_for_print} (Valeur numérique: {gm_ac_val} S)")
                    file_print(f"  rpi_{name_q} (Résistance d'entrée Base-Émetteur AC) = {rpi_ac_sym_expr_for_print} (Valeur numérique: {rpi_ac_val} Ohm)")
                    file_print(f"  Note: rpi_{name_q} est utilisé dans le modèle AC via l'admittance Ybe = 1/rpi_{name_q} + s*Cbe_{name_q}. Lors de la résolution symbolique par Xcas, rpi_{name_q} est substitué par beta_{name_q}/gm_{name_q}, ce qui explique pourquoi rpi n'apparaît pas directement dans les expressions finales des tensions nodales.", end='\n\n')
                    file_print(f"--- Valeurs numériques retenues pour {name_q} ---")
                    vbe_on_val = values.get(f'Vbe_on_{name_q}', '0.7')
                    file_print(f"  Vbe_on_{name_q} = {vbe_on_val}")
                    file_print(f"  beta_{name_q} = {beta_val}")
                    file_print(f"  Cbc_{name_q} = {values[f'Cbc_{name_q}']}")
                    file_print(f"  ro_{name_q} = {ro_ac_val}")
                    file_print(f"  rpi_{name_q} = {rpi_ac_val}")
                    file_print(f"  Cbe_{name_q} = {values[f'Cbe_{name_q}']}", end='\n\n')

            file_print("===== DC Analysis (Symbolic) =====", end='\n\n')
            for node in original_nodes: file_print(f"V{node} = {voltages_dc[node]}")
            for comp in netlist:
                if is_bjt_compensation_component(comp[0], values): continue
                if comp[0] in currents_dc: file_print(f"I_{comp[0]} = {currents_dc[comp[0]]}")
                elif f"{comp[0]}_c" in currents_dc: file_print(f"I_{comp[0]}_c = {currents_dc[f'{comp[0]}_c']}")
                if f"{comp[0]}_b" in currents_dc: file_print(f"I_{comp[0]}_b = {currents_dc[f'{comp[0]}_b']}")
                if f"{comp[0]}_e" in currents_dc: file_print(f"I_{comp[0]}_e = {currents_dc[f'{comp[0]}_e']}")
            file_print("===== DC Analysis (Numerical) =====", end='\n\n')
            for node in original_nodes: file_print(f"V{node} = {dc_voltages_num[node]}")
            for comp in netlist:
                if is_bjt_compensation_component(comp[0], values): continue
                if comp[0] in currents_dc: file_print(f"I_{comp[0]} = {dc_currents_num[comp[0]]}")
                elif f"{comp[0]}_c" in currents_dc: file_print(f"I_{comp[0]}_c = {dc_currents_num[f'{comp[0]}_c']}")
                if f"{comp[0]}_b" in currents_dc: file_print(f"I_{comp[0]}_b = {dc_currents_num[f'{comp[0]}_b']}")
                if f"{comp[0]}_e" in currents_dc: file_print(f"I_{comp[0]}_e = {dc_currents_num[f'{comp[0]}_e']}")
            file_print("===== AC Analysis (Symbolic) =====", end='\n\n')
            for node in original_nodes:
                voltage_with_fc = force_fc_substitution(voltages[node], values)
                file_print(f"V{node} = {voltage_with_fc}")
            for comp in netlist:
                if is_bjt_compensation_component(comp[0], values): continue
                if comp[0] in currents:
                    current_with_fc = force_fc_substitution(currents[comp[0]], values)
                    file_print(f"I_{comp[0]} = {current_with_fc}")
                elif f"{comp[0]}_c" in currents:
                    current_with_fc = force_fc_substitution(currents[f'{comp[0]}_c'], values)
                    file_print(f"I_{comp[0]}_c = {current_with_fc}")
                if f"{comp[0]}_b" in currents:
                    current_with_fc = force_fc_substitution(currents[f'{comp[0]}_b'], values)
                    file_print(f"I_{comp[0]}_b = {current_with_fc}")
                if f"{comp[0]}_e" in currents:
                    current_with_fc = force_fc_substitution(currents[f'{comp[0]}_e'], values)
                    file_print(f"I_{comp[0]}_e = {current_with_fc}")
            file_print("===== AC Analysis (Numerical) =====", end='\n\n')
            for node in original_nodes:
                value = str(ac_voltages_num[node]).replace('k', '1000')
                simplified_value = xcas(f"evalf(simplify({value}))")
                file_print(f"V{node} = {simplified_value}")
            for comp in netlist:
                if is_bjt_compensation_component(comp[0], values): continue
                if comp[0] in currents:
                    value = str(ac_currents_num[comp[0]]).replace('k', '1000')
                    simplified_value = xcas(f"evalf(simplify({value}))")
                    file_print(f"I_{comp[0]} = {simplified_value}")
                elif f"{comp[0]}_c" in currents:
                    value = str(ac_currents_num[f'{comp[0]}_c']).replace('k', '1000')
                    simplified_value = xcas(f"evalf(simplify({value}))")
                    file_print(f"I_{comp[0]}_c = {simplified_value}")
                if f"{comp[0]}_b" in currents:
                    value = str(ac_currents_num[f'{comp[0]}_b']).replace('k', '1000')
                    simplified_value = xcas(f"evalf(simplify({value}))")
                    file_print(f"I_{comp[0]}_b = {simplified_value}")
                if f"{comp[0]}_e" in currents:
                    value = str(ac_currents_num[f'{comp[0]}_e']).replace('k', '1000')
                    simplified_value = xcas(f"evalf(simplify({value}))")
                    file_print(f"I_{comp[0]}_e = {simplified_value}")
            
            if laplace_domain:
                step += 1
                step_start_time = time.time()
                original_stdout.write(f"Analyse temporelle AC en cours... [étape {step}/{total_steps}]\n")
                original_stdout.flush()
                file_print(f"===== AC Analysis (Temporal) - f = {frequency_hz} Hz =====", end='\n\n')
                with ThreadPoolExecutor(max_workers=mp.cpu_count()) as executor:
                    ac_voltages_tuple = tuple(ac_voltages_num.items())
                    ac_currents_tuple = tuple(ac_currents_num.items())
                    values_tuple = tuple(values.items())
                    temporal_voltages_future = executor.submit(ac_temporal_analysis, ac_voltages_tuple, values_tuple, frequency_hz)
                    temporal_currents_future = executor.submit(ac_temporal_analysis, ac_currents_tuple, values_tuple, frequency_hz)
                    temporal_voltages = temporal_voltages_future.result()
                    temporal_currents = temporal_currents_future.result()
                step_end_time = time.time()
                original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
                original_stdout.flush()
                for node in original_nodes: file_print(f"v{node}(t) = {temporal_voltages[node]}")
                for comp in netlist:
                    if is_bjt_compensation_component(comp[0], values): continue
                    if comp[0] in temporal_currents: file_print(f"i_{comp[0]}(t) = {temporal_currents[comp[0]]}")
                    elif f"{comp[0]}_c" in temporal_currents: file_print(f"i_{comp[0]}_c(t) = {temporal_currents[f'{comp[0]}_c']}")
                    if f"{comp[0]}_b" in temporal_currents: file_print(f"i_{comp[0]}_b(t) = {temporal_currents[f'{comp[0]}_b']}")
                    if f"{comp[0]}_e" in temporal_currents: file_print(f"i_{comp[0]}_e(t) = {temporal_currents[f'{comp[0]}_e']}")
                if do_transfer_function and input_node and output_node and freq_min and freq_max:
                    def calculate_transfer_function(in_node, out_node, f_min, f_max, tf_number=1, show_steps=True):
                        step_start_time = time.time()
                        if show_steps:
                            original_stdout.write(f"Calcul de la fonction de transfert {tf_number}... [étape {step}/{total_steps}]\n")
                        else:
                            original_stdout.write(f"Calcul de la fonction de transfert {tf_number}...\n")
                        original_stdout.flush()
                        file_print(f"===== Fonction de Transfert {tf_number} =====", end='\n\n')
                        if in_node in voltages and out_node in voltages:
                            v_in, v_out = voltages[in_node], voltages[out_node]
                            if v_in != '0':
                                transfer_function = xcas(f"simplify(({v_out})/({v_in}))")
                                transfer_function_with_fc_values = force_fc_substitution(transfer_function, values)
                                file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function_with_fc_values}")
                                # Afficher aussi la fonction symbolique pure (sans substitution Fc)
                                file_print(f"H{tf_number}(s) = V{out_node}/V{in_node} = {transfer_function}")
                                step_end_time = time.time()
                                original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
                                original_stdout.flush()
                                step_start_time = time.time()
                                if show_steps:
                                    original_stdout.write(f"Génération du diagramme de Bode {tf_number}... [étape {step+1}/{total_steps}]\n")
                                else:
                                    original_stdout.write(f"Génération du diagramme de Bode {tf_number}...\n")
                                original_stdout.flush()
                                plot_bode(transfer_function, values, (f_min, f_max))
                                step_end_time = time.time()
                                original_stdout.write(f"  ✓ Terminé en {step_end_time - step_start_time:.2f}s\n")
                                original_stdout.flush()
                                return True
                            else:
                                file_print("Erreur: tension d'entrée nulle")
                                return False
                        else:
                            file_print("Erreur: noeuds non trouvés")
                            return False

                    step += 1
                    calculate_transfer_function(input_node, output_node, freq_min, freq_max, 1, show_steps=True)
                    step += 1

                    tf_count = 2
                    while True:
                        sys.stdout = original_stdout
                        continue_tf = input("\nSouhaitez-vous calculer une autre fonction de transfert? (o/n) [défaut: n]: ").strip().lower()
                        if continue_tf not in ['o', 'oui', 'y', 'yes']:
                            sys.stdout = f
                            break
                        new_input_node = input("Noeud d'entrée: ").strip()
                        new_output_node = input("Noeud de sortie: ").strip()
                        new_freq_min = float(input("Fréquence min (Hz): "))
                        new_freq_max = float(input("Fréquence max (Hz): "))
                        sys.stdout = f
                        calculate_transfer_function(new_input_node, new_output_node, new_freq_min, new_freq_max, tf_count, show_steps=False)
                        tf_count += 1
    finally:
        sys.stdout = original_stdout
        print("Calculs terminés!")
        print(f"Simulation results saved to {log_filename}")

def substitute_values(expressions, values, use_improved_method=True):
    expressions_tuple = tuple(expressions.items())
    values_copy = values.copy()
    if 'k' not in values_copy: values_copy['k'] = '1000'
    values_tuple = tuple(values_copy.items())
    vars_list, vals_list = [], []
    for var, val in values_tuple: vars_list.append(var); vals_list.append(val)
    if not vars_list: return dict(expressions_tuple)
    substitution_arg = f"[{','.join(vars_list)}],[{','.join(vals_list)}]"
    return _improved_substitute_xcas(expressions_tuple, substitution_arg) if use_improved_method else _bulk_substitute_xcas(expressions_tuple, substitution_arg)

def force_fc_substitution(expression, values):
    result = expression
    for var, val in values.items():
        if var.startswith('Fc') and '_' in var:
            try:
                if var in str(result): result = xcas(f"subst({result},{var},{val})")
            except: continue
    return result

def verify_symbolic_numeric_consistency(symbolic_expr, numeric_expr, values, tolerance=1e-6):
    try:
        # Identifier les résistances de compensation BJT
        bjt_compensation_resistors = set()
        for var, val in values.items():
            if var.startswith('comp_BJT_res_') and val == 'true':
                resistor_name = var.replace('comp_BJT_res_', '')
                bjt_compensation_resistors.add(resistor_name)

        manual_substitution = symbolic_expr
        manual_substitution = xcas(f"subst({manual_substitution},k,1000)")
        for var, val in values.items():
            if var != 'k' and not var.startswith('comp_BJT_'):
                # Forcer la substitution numérique pour les résistances de compensation BJT
                if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                    try:
                        if var in str(manual_substitution): manual_substitution = xcas(f"subst({manual_substitution},{var},{val})")
                    except: continue
        manual_result = xcas(f"evalf(simplify({manual_substitution}))")
        try:
            manual_val, numeric_val = float(manual_result), float(numeric_expr)
            return (True, manual_result, numeric_expr, None) if abs(manual_val - numeric_val) < tolerance else (False, manual_result, numeric_expr, f"Difference: {abs(manual_val - numeric_val)}")
        except:
            return (True, manual_result, numeric_expr, None) if str(manual_result) == str(numeric_expr) else (False, manual_result, numeric_expr, "Expressions differentes")
    except Exception as e: return False, "Erreur", numeric_expr, str(e)

if __name__ == '__main__':
    netlist_str = get_user_netlist()
    frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max = get_analysis_parameters()
    print("\nDébut des calculs...")
    solve_circuit(netlist_str, frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max, laplace_domain=True)
