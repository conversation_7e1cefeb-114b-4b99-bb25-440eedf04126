#!/usr/bin/env python3
"""
Test pour vérifier le calcul physique de la résistance dynamique rd = n*VT/I
"""

def test_rd_calculation():
    print("=== Test du calcul physique de rd ===")
    print("Formule: rd = n * VT / I")
    print()
    
    # Paramètres physiques
    n = 1.0  # Facteur d'idéalité (diode idéale)
    VT = 0.026  # Tension thermique à 25°C (26 mV)
    
    print("Paramètres physiques:")
    print(f"  n (facteur d'idéalité) = {n}")
    print(f"  VT (tension thermique) = {VT} V = {VT*1000} mV")
    print()
    
    # Circuit test: V1(5V) → R1(100Ω) → D1(2.2V) → R2(100Ω) → GND
    print("Circuit test: V1(5V DC) → R1(100Ω) → D1(2.2V) → R2(100Ω) → GND")
    print()
    
    # Calcul du courant DC
    V1 = 5.0  # V
    Vth = 2.2  # V
    R1 = 100.0  # Ω
    R2 = 100.0  # Ω
    
    # En DC, la diode impose sa tension de seuil
    V2 = Vth  # V2 = Vth = 2.2V
    V3 = 0.0  # V3 = 0V (référence)
    
    # Courant dans la boucle (même courant partout en DC)
    I_dc = (V1 - V2) / R1  # Courant à travers R1
    # Vérification: V2 - V3 = I_dc * R2 + Vth
    # 2.2 - 0 = I_dc * 100 + 2.2 => I_dc * 100 = 0 => I_dc = 0
    # Erreur dans le raisonnement, recalculons...
    
    # Correction: V2 = V3 + Vth, donc V3 = V2 - Vth
    # Mais V3 = I_dc * R2, donc I_dc * R2 = V2 - Vth
    # Et I_dc = (V1 - V2) / R1
    # Donc: (V1 - V2) / R1 * R2 = V2 - Vth
    # (V1 - V2) * R2 = (V2 - Vth) * R1
    # V1*R2 - V2*R2 = V2*R1 - Vth*R1
    # V1*R2 + Vth*R1 = V2*R1 + V2*R2 = V2*(R1+R2)
    # V2 = (V1*R2 + Vth*R1) / (R1+R2)
    
    V2_calculated = (V1*R2 + Vth*R1) / (R1 + R2)
    V3_calculated = V2_calculated - Vth
    I_dc_calculated = (V1 - V2_calculated) / R1
    
    print("Calcul DC:")
    print(f"  V1 = {V1} V")
    print(f"  V2 = {V2_calculated:.3f} V")
    print(f"  V3 = {V3_calculated:.3f} V")
    print(f"  I_dc = {I_dc_calculated:.6f} A = {I_dc_calculated*1000:.3f} mA")
    print()
    
    # Calcul de la résistance dynamique
    rd_calculated = (n * VT) / I_dc_calculated
    
    print("Calcul de la résistance dynamique:")
    print(f"  rd = n * VT / I_dc")
    print(f"  rd = {n} * {VT} / {I_dc_calculated:.6f}")
    print(f"  rd = {rd_calculated:.3f} Ω")
    print()
    
    # Analyse AC avec la vraie rd
    V1_ac = 3.0  # V (amplitude AC)
    
    # Diviseur de tension AC: V3_ac = V1_ac * R2 / (R1 + rd + R2)
    R_total = R1 + rd_calculated + R2
    V3_ac = V1_ac * R2 / R_total
    V2_ac = V1_ac * (rd_calculated + R2) / R_total
    
    print("Analyse AC:")
    print(f"  V1_ac = {V1_ac} V")
    print(f"  R_total = R1 + rd + R2 = {R1} + {rd_calculated:.3f} + {R2} = {R_total:.3f} Ω")
    print(f"  V2_ac = {V1_ac} * ({rd_calculated:.3f} + {R2}) / {R_total:.3f} = {V2_ac:.6f} V")
    print(f"  V3_ac = {V1_ac} * {R2} / {R_total:.3f} = {V3_ac:.6f} V")
    print()
    
    # Comparaison avec l'ancienne méthode (rd = 10Ω)
    rd_old = 10.0
    R_total_old = R1 + rd_old + R2
    V3_ac_old = V1_ac * R2 / R_total_old
    
    print("Comparaison avec l'ancienne méthode (rd = 10Ω):")
    print(f"  V3_ac_old = {V1_ac} * {R2} / {R_total_old} = {V3_ac_old:.6f} V")
    print(f"  Différence = {abs(V3_ac - V3_ac_old):.6f} V")
    print(f"  Atténuation améliorée: {V3_ac < V3_ac_old}")
    print()
    
    print("✓ La résistance dynamique est maintenant calculée physiquement")
    print("✓ rd dépend du vrai courant DC de la diode")
    print("✓ Plus le courant est élevé, plus rd est faible")
    print("✓ Comportement AC plus réaliste")

def test_different_currents():
    print("\n=== Test avec différents courants ===")
    
    n = 1.0
    VT = 0.026
    
    currents = [0.001, 0.01, 0.028, 0.1]  # A
    
    print("Courant DC (A) | rd (Ω)    | Atténuation")
    print("---------------|-----------|------------")
    
    for I_dc in currents:
        rd = (n * VT) / I_dc
        # Pour V1_ac = 3V, R1 = R2 = 100Ω
        V3_ac = 3.0 * 100 / (100 + rd + 100)
        attenuation = V3_ac / 3.0
        print(f"{I_dc:13.3f} | {rd:8.3f} | {attenuation:.3f}")
    
    print()
    print("Observation: Plus le courant DC est élevé, plus rd est faible,")
    print("donc moins d'atténuation du signal AC.")

if __name__ == "__main__":
    test_rd_calculation()
    test_different_currents()
