#!/usr/bin/env python3
"""
Script pour tester la cohérence avec le fichier vue.txt existant
en utilisant le script bode_vue.py modifié
"""
import subprocess
import os
import re

def update_bode_vue_script():
    """Met à jour le script bode_vue.py pour utiliser la même logique que test.py corrigé"""
    
    print("=== Mise à jour du script bode_vue.py ===")
    
    # Nouvelle fonction create_scipy_transfer_function améliorée
    new_function = '''def create_scipy_transfer_function(tf_expr, parameters=None, is_numeric=False):
    """Crée une fonction de transfert scipy à partir d'une expression avec logique cohérente"""
    
    print(f"\\n=== Création de la fonction de transfert ({'numérique' if is_numeric else 'symbolique'}) ===")
    
    # Parser l'expression
    num_expr, den_expr = parse_transfer_function(tf_expr)
    
    if num_expr is None or den_expr is None:
        print("Erreur: Impossible de parser la fonction de transfert")
        return None
    
    try:
        s = sp.Symbol('s')
        
        if is_numeric:
            # Pour la version numérique, les coefficients sont déjà numériques
            print("Traitement de la fonction de transfert numérique...")
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
        else:
            # Pour la version symbolique, substituer les paramètres avec la même logique que test.py
            if parameters is None:
                parameters = {}
            
            print("Substitution des paramètres dans la fonction symbolique...")
            
            # Identifier les résistances de compensation BJT (même logique que test.py)
            bjt_compensation_resistors = set()
            for var, val in parameters.items():
                if var.startswith('comp_BJT_res_') and str(val) == 'true':
                    resistor_name = var.replace('comp_BJT_res_', '')
                    bjt_compensation_resistors.add(resistor_name)
            
            # Parser les expressions symboliques
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
            
            # Substitution manuelle identique à test.py
            for var, val in parameters.items():
                if var != 'k' and not var.startswith('comp_BJT_'):
                    # Forcer la substitution numérique pour les résistances de compensation BJT
                    if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                        try:
                            if var in str(num_sympy):
                                num_sympy = num_sympy.subs(sp.Symbol(var), val)
                            if var in str(den_sympy):
                                den_sympy = den_sympy.subs(sp.Symbol(var), val)
                        except:
                            continue
            
            # Substitutions finales
            num_sympy = num_sympy.subs(sp.Symbol('k'), 1000)
            den_sympy = den_sympy.subs(sp.Symbol('k'), 1000)
            num_sympy = num_sympy.subs(sp.Symbol('pi'), sp.pi)
            den_sympy = den_sympy.subs(sp.Symbol('pi'), sp.pi)
        
        # Extraire les coefficients du polynôme
        num_coeffs = extract_polynomial_coefficients(num_sympy, s)
        den_coeffs = extract_polynomial_coefficients(den_sympy, s)
        
        # Créer la fonction de transfert scipy
        tf = signal.TransferFunction(num_coeffs, den_coeffs)
        
        return tf
        
    except Exception as e:
        print(f"Erreur lors de la création de la fonction de transfert: {e}")
        return None'''
    
    # Lire le fichier bode_vue.py
    try:
        with open('bode_vue.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacer la fonction create_scipy_transfer_function
        pattern = r'def create_scipy_transfer_function\(tf_expr, parameters=None, is_numeric=False\):.*?(?=\ndef |$)'
        
        match = re.search(pattern, content, re.DOTALL)
        if match:
            new_content = content[:match.start()] + new_function + content[match.end():]
            
            # Sauvegarder le fichier modifié
            with open('bode_vue.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✓ Script bode_vue.py mis à jour avec la logique cohérente")
            return True
        else:
            print("✗ Impossible de trouver la fonction à remplacer")
            return False
            
    except Exception as e:
        print(f"✗ Erreur lors de la mise à jour: {e}")
        return False

def test_vue_consistency():
    """Teste la cohérence avec le fichier vue.txt"""
    
    print("\\n=== Test de cohérence avec vue.txt ===")
    
    if not os.path.exists('vue.txt'):
        print("✗ Fichier vue.txt non trouvé")
        return False
    
    if not os.path.exists('bode_vue.py'):
        print("✗ Fichier bode_vue.py non trouvé")
        return False
    
    try:
        # Exécuter le script bode_vue.py
        print("Exécution du script bode_vue.py...")
        result = subprocess.run(['python', 'bode_vue.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ Script exécuté avec succès")
            
            # Analyser la sortie
            output = result.stdout
            print("\\nSortie du script:")
            print("-" * 40)
            print(output)
            print("-" * 40)
            
            # Chercher les gains DC
            symbolic_gain_match = re.search(r'Gain DC symbolique: ([+-]?\\d+\\.\\d+) dB', output)
            numeric_gain_match = re.search(r'Gain DC numérique: ([+-]?\\d+\\.\\d+) dB', output)
            
            if symbolic_gain_match and numeric_gain_match:
                symbolic_gain = float(symbolic_gain_match.group(1))
                numeric_gain = float(numeric_gain_match.group(1))
                
                difference = abs(symbolic_gain - numeric_gain)
                
                print(f"\\n=== Analyse de cohérence ===")
                print(f"Gain DC symbolique: {symbolic_gain:.1f} dB")
                print(f"Gain DC numérique:  {numeric_gain:.1f} dB")
                print(f"Différence:         {difference:.1f} dB")
                
                if difference < 1.0:
                    print("🎉 EXCELLENT: Différence < 1 dB")
                    return True
                elif difference < 5.0:
                    print("✓ BON: Différence < 5 dB")
                    return True
                elif difference < 20.0:
                    print("⚠ ACCEPTABLE: Différence < 20 dB")
                    return True
                else:
                    print("✗ PROBLÉMATIQUE: Différence > 20 dB")
                    return False
            else:
                print("✗ Impossible d'extraire les gains DC")
                return False
                
        else:
            print("✗ Erreur lors de l'exécution")
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Script interrompu (timeout)")
        return False
    except Exception as e:
        print(f"✗ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("Test de cohérence avec vue.txt")
    print("=" * 50)
    
    # Mettre à jour le script bode_vue.py
    if update_bode_vue_script():
        # Tester la cohérence
        success = test_vue_consistency()
        
        if success:
            print("\\n🎉 Test de cohérence réussi!")
        else:
            print("\\n⚠ Test de cohérence échoué")
    else:
        print("\\n✗ Impossible de mettre à jour le script")

if __name__ == "__main__":
    main()
