#!/usr/bin/env python3
"""
Script de test pour la fonctionnalité d'ajustement manuel
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer bode_H1_final
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_manual_adjustment():
    """Test de la fonctionnalité d'ajustement manuel"""
    
    # Créer un fichier FTC.txt de test
    test_expression = "1/(R1*C1*s + 1)"
    
    with open("FTC.txt", "w") as f:
        f.write(test_expression)
    
    print("Fichier FTC.txt créé avec l'expression de test:", test_expression)
    print("\nPour tester la fonctionnalité d'ajustement manuel:")
    print("1. Lancez l'application: python bode_H1_final.py")
    print("2. Configurez les paramètres des composants")
    print("3. Cliquez sur 'Ajustement manuel' (bouton orange en dessous d'Analyse de sensibilité)")
    print("4. Modifiez l'expression dans la zone de texte")
    print("5. Cliquez sur 'Valider et afficher'")
    print("6. Vérifiez que la courbe verte pointillée apparaît")
    print("7. Bougez les sliders pour vérifier la réactivité")
    print("\nExemples d'expressions à tester:")
    print("- Expression simple: 1/(R1*C1*s + 1)")
    print("- Avec gain: 10/(R1*C1*s + 1)")
    print("- Passe-haut: s/(R1*C1*s + 1)")
    print("- Passe-bande: s/(R1*C1*s^2 + s + 1/(R2*C2))")

if __name__ == "__main__":
    test_manual_adjustment()
