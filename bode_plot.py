#!/usr/bin/env python3
"""
Script pour tracer le diagramme de Bode des fonctions de transfert H1(s) = V7/V1
à partir des fichiers Results_Simulation avec comparaison symbolique/numérique
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re
import os

def parse_simulation_file(filename):
    """Parse le fichier de simulation et extrait les fonctions de transfert H1(s) = V7/V1"""

    if not os.path.exists(filename):
        print(f"Erreur: Le fichier {filename} n'existe pas.")
        return None

    print(f"=== Lecture du fichier {filename} ===")

    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()

    # Pour le fichier fcs.txt, la structure est différente
    if filename == 'fcs.txt':
        return parse_fcs_file(lines)

    # Code original pour les autres fichiers
    content = ''.join(lines)

    # Extraire la fonction de transfert symbolique H1(s) = V7/V1
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert numérique|$)', content, re.DOTALL)

    if not h1_symbolic_match:
        print("Erreur: Fonction de transfert symbolique H1(s) = V7/V1 non trouvée")
        return None

    h1_symbolic = h1_symbolic_match.group(1).strip()
    print(f"Fonction de transfert symbolique H1(s) trouvée: {len(h1_symbolic)} caractères")

    # Extraire la fonction de transfert numérique
    h1_numeric_match = re.search(r'Fonction de transfert numérique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)

    if not h1_numeric_match:
        print("Erreur: Fonction de transfert numérique non trouvée")
        return None

    h1_numeric = h1_numeric_match.group(1).strip()
    print(f"Fonction de transfert numérique trouvée: {len(h1_numeric)} caractères")

    return {
        'symbolic': h1_symbolic,
        'numeric': h1_numeric
    }

def parse_fcs_file(lines):
    """Parse spécifiquement le fichier fcs.txt"""

    print("=== Parsing du fichier fcs.txt ===")

    # Ligne 1: Fonction symbolique
    if len(lines) < 1:
        print("Erreur: Fichier fcs.txt vide")
        return None

    symbolic_line = lines[0].strip()
    if not symbolic_line.startswith('H1(s) = V7/V1 = '):
        print("Erreur: Format de fonction symbolique incorrect")
        return None

    h1_symbolic = symbolic_line[len('H1(s) = V7/V1 = '):]
    print(f"Fonction de transfert symbolique trouvée: {len(h1_symbolic)} caractères")

    # Ligne 3: Fonction numérique (ligne 2 est vide)
    if len(lines) < 3:
        print("Erreur: Fonction numérique manquante dans fcs.txt")
        return None

    numeric_line = lines[2].strip()
    if not numeric_line.startswith('Fonction de transfert finale: '):
        print("Erreur: Format de fonction numérique incorrect")
        return None

    h1_numeric = numeric_line[len('Fonction de transfert finale: '):]
    print(f"Fonction de transfert numérique trouvée: {len(h1_numeric)} caractères")

    return {
        'symbolic': h1_symbolic,
        'numeric': h1_numeric
    }

def extract_circuit_parameters(filename):
    """Extrait les paramètres du circuit du fichier de simulation"""

    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Pour le fichier fcs.txt, extraire les paramètres de la section substitutions
    if filename == 'fcs.txt':
        return extract_fcs_parameters(content)

    # Code original pour les autres fichiers
    # Extraire la netlist originale
    netlist_match = re.search(r'Netlist originale:(.*?)(?=Vbe_on_Q1|Temps de calcul|$)', content, re.DOTALL)

    if not netlist_match:
        print("Erreur: Netlist originale non trouvée")
        return {}

    netlist = netlist_match.group(1)

    # Extraire les valeurs des composants
    parameters = {}

    # Résistances
    r_matches = re.findall(r'R(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[kmMG]?)', netlist)
    for r_num, r_val in r_matches:
        # Convertir les unités
        val = parse_value(r_val)
        parameters[f'R{r_num}'] = val
        print(f"R{r_num} = {val} Ω")

    # Capacités
    c_matches = re.findall(r'C(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[pnumkMG]?[Ff]?)', netlist)
    for c_num, c_val in c_matches:
        val = parse_value(c_val)
        parameters[f'C{c_num}'] = val
        print(f"C{c_num} = {val} F")

    # Extraire les paramètres BJT directement du contenu
    param_patterns = {
        'Vbe_on_Q1': r'Vbe_on_Q1 = ([0-9.e-]+)',
        'beta_Q1': r'beta_Q1 = ([0-9.e-]+)',
        'Cbc_Q1': r'Cbc_Q1 = ([0-9.e-]+)',
        'ro_Q1': r'ro_Q1 = ([0-9.e-]+[kmMG]?)',
        'rpi_Q1': r'rpi_Q1 = ([0-9.e-]+)',
        'Cbe_Q1': r'Cbe_Q1 = ([0-9.e-]+)',
        'gm_Q1': r'gm_Q1 = ([0-9.e-]+)'
    }

    for param_name, pattern in param_patterns.items():
        match = re.search(pattern, content)
        if match:
            val_str = match.group(1)
            if 'k' in val_str.lower():
                val = float(val_str.replace('k', '').replace('K', '')) * 1000
            elif 'M' in val_str:
                val = float(val_str.replace('M', '')) * 1e6
            elif 'G' in val_str:
                val = float(val_str.replace('G', '')) * 1e9
            else:
                val = float(val_str)
            parameters[param_name] = val
            print(f"{param_name} = {val}")

    # Calculer gm_Q1 si pas trouvé (gm = beta / rpi)
    if 'gm_Q1' not in parameters and 'beta_Q1' in parameters and 'rpi_Q1' in parameters:
        gm_val = parameters['beta_Q1'] / parameters['rpi_Q1']
        parameters['gm_Q1'] = gm_val
        print(f"gm_Q1 = {gm_val} (calculé)")

    return parameters

def extract_fcs_parameters(content):
    """Extrait les paramètres du fichier fcs.txt"""

    print("=== Extraction des paramètres de fcs.txt ===")

    parameters = {}

    # Chercher la section "Substitutions numeriques appliquees:"
    lines = content.split('\n')
    in_substitutions = False

    for line in lines:
        line = line.strip()

        if 'Substitutions numeriques appliquees:' in line:
            in_substitutions = True
            continue

        if in_substitutions and line and not line.startswith('='):
            # Format: "  param = valeur"
            if '=' in line:
                parts = line.split('=')
                if len(parts) == 2:
                    param_name = parts[0].strip()
                    param_value = parts[1].strip()

                    try:
                        # Convertir en float
                        val = float(param_value)
                        parameters[param_name] = val
                        print(f"{param_name} = {val}")
                    except ValueError:
                        print(f"Attention: Impossible de convertir {param_name} = {param_value}")

    print(f"Total: {len(parameters)} paramètres extraits")
    return parameters

def parse_value(value_str):
    """Parse une valeur avec unité (k, M, G, p, n, u, m)"""
    value_str = value_str.strip().upper()

    # Supprimer les unités de base (F, H, Ω)
    value_str = re.sub(r'[FHΩ]$', '', value_str)

    multipliers = {
        'P': 1e-12, 'N': 1e-9, 'U': 1e-6, 'M': 1e-3,
        'K': 1e3, 'MEG': 1e6, 'G': 1e9
    }

    # Vérifier les multiplicateurs
    for mult, factor in multipliers.items():
        if value_str.endswith(mult):
            base_val = float(value_str[:-len(mult)])
            return base_val * factor

    return float(value_str)

def parse_transfer_function(tf_expr):
    """Parse une expression de fonction de transfert sous forme de fraction"""

    # Nettoyer l'expression
    tf_expr = tf_expr.strip()

    # Chercher le pattern (numérateur)/(dénominateur)
    if ')/(' in tf_expr:
        # Format: (num)/(den)
        parts = tf_expr.split(')/(')
        if len(parts) == 2:
            num_expr = parts[0].strip('(')
            den_expr = parts[1].strip(')')
            return num_expr, den_expr

    # Pour les expressions très complexes comme dans fcs.txt
    # Chercher la structure générale avec parenthèses équilibrées
    if tf_expr.startswith('(') and ')/' in tf_expr:
        # Trouver la parenthèse fermante qui correspond à l'ouverture
        paren_count = 0
        split_pos = -1

        for i, char in enumerate(tf_expr):
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
                if paren_count == 0 and i + 1 < len(tf_expr) and tf_expr[i + 1] == '/':
                    split_pos = i
                    break

        if split_pos > 0:
            num_expr = tf_expr[1:split_pos]  # Enlever la première parenthèse
            den_expr = tf_expr[split_pos + 3:]  # Enlever ')/' et la dernière parenthèse
            if den_expr.endswith(')'):
                den_expr = den_expr[:-1]
            return num_expr, den_expr

    # Si pas de parenthèses, chercher le premier '/' qui sépare num et den
    # Mais attention aux divisions dans les termes
    # Pour simplifier, on va essayer de détecter la structure

    print("Format de fonction de transfert non reconnu, tentative d'analyse...")
    print(f"Expression: {tf_expr[:200]}...")

    return None, None

def create_transfer_function_from_expressions(tf_expr, parameters, is_numeric=False):
    """Crée une fonction de transfert à partir d'une expression symbolique ou numérique"""

    print(f"\n=== Création de la fonction de transfert ({'numérique' if is_numeric else 'symbolique'}) ===")

    # Parser l'expression pour séparer numérateur et dénominateur
    num_expr, den_expr = parse_transfer_function(tf_expr)

    if num_expr is None or den_expr is None:
        print("Erreur: Impossible de parser la fonction de transfert")
        return None

    # Créer le symbole s
    s = sp.Symbol('s')

    try:
        if is_numeric:
            # Pour la version numérique, les coefficients sont déjà numériques
            print("Traitement de la fonction de transfert numérique...")
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
        else:
            # Pour la version symbolique, substituer les paramètres
            print("Substitution des paramètres dans la fonction symbolique...")

            # Créer un dictionnaire de substitution
            subs_dict = {}
            for param, value in parameters.items():
                subs_dict[sp.Symbol(param)] = value

            # Ajouter des constantes communes
            subs_dict[sp.Symbol('pi')] = np.pi

            # Parser les expressions symboliques
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)

            # Substituer les valeurs
            num_sympy = num_sympy.subs(subs_dict)
            den_sympy = den_sympy.subs(subs_dict)

        # Extraire les coefficients du polynôme
        num_coeffs = extract_polynomial_coefficients(num_sympy, s)
        den_coeffs = extract_polynomial_coefficients(den_sympy, s)

        # Créer la fonction de transfert scipy
        tf = signal.TransferFunction(num_coeffs, den_coeffs)

        return tf

    except Exception as e:
        print(f"Erreur lors de la création de la fonction de transfert: {e}")
        import traceback
        traceback.print_exc()
        return None

def extract_polynomial_coefficients(poly_expr, s_symbol):
    """Extrait les coefficients du polynôme"""

    # Développer l'expression
    expanded = sp.expand(poly_expr)

    # Déterminer le degré maximum
    degree = sp.degree(expanded, s_symbol)

    # Extraire les coefficients (ordre décroissant)
    coeffs = []
    for power in range(degree, -1, -1):
        coeff = expanded.coeff(s_symbol, power)
        if coeff is None:
            coeff = 0
        coeffs.append(float(coeff))

    print(f"Polynôme de degré {degree}")
    for i, coeff in enumerate(coeffs):
        power = degree - i
        if power > 0:
            print(f"s^{power}: {coeff:.2e}")
        else:
            print(f"s^0 (constant): {coeff:.2e}")

    return coeffs

def simplify_expression(expr_str):
    """Simplifie une expression symbolique complexe"""

    # Remplacer les notations scientifiques par des symboles
    expr_str = re.sub(r'(\d+(?:\.\d+)?)e\+(\d+)', r'\1*10**\2', expr_str)
    expr_str = re.sub(r'(\d+(?:\.\d+)?)e-(\d+)', r'\1*10**(-\2)', expr_str)

    # Remplacer les variables communes
    replacements = {
        'gm_Q1': 'gm_Q1',
        'rpi_Q1': 'rpi_Q1',
        'ro_Q1': 'ro_Q1',
        'Cbe_Q1': 'Cbe_Q1',
        'Cbc_Q1': 'Cbc_Q1'
    }

    for old, new in replacements.items():
        expr_str = expr_str.replace(old, new)

    return expr_str

def plot_bode_comparison(tf_symbolic, tf_numeric, filename_suffix=""):
    """Trace les diagrammes de Bode en comparaison (symbolique vs numérique)"""

    # Gamme de fréquences (1 Hz à 1 THz)
    frequencies = np.logspace(0, 12, 10000)  # 1 Hz à 1 THz
    omega = 2 * np.pi * frequencies

    # Création du graphique
    _, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    magnitude_db_sym = None
    magnitude_db_num = None
    phase_deg_sym = None
    phase_deg_num = None

    # Tracer la fonction symbolique si disponible
    if tf_symbolic is not None:
        try:
            _, h_sym = signal.freqresp(tf_symbolic, omega)
            magnitude_db_sym = 20 * np.log10(np.abs(h_sym))
            phase_deg_sym = np.angle(h_sym) * 180 / np.pi

            ax1.semilogx(frequencies, magnitude_db_sym, 'b-', linewidth=2.5, label='Fonction symbolique avec substitutions', alpha=0.8)
            ax2.semilogx(frequencies, phase_deg_sym, 'b-', linewidth=2.5, label='Fonction symbolique avec substitutions', alpha=0.8)

            print(f"Gain DC symbolique: {magnitude_db_sym[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé symbolique: {e}")

    # Tracer la fonction numérique si disponible
    if tf_numeric is not None:
        try:
            _, h_num = signal.freqresp(tf_numeric, omega)
            magnitude_db_num = 20 * np.log10(np.abs(h_num))
            phase_deg_num = np.angle(h_num) * 180 / np.pi

            ax1.semilogx(frequencies, magnitude_db_num, 'r--', linewidth=2, label='Fonction numérique directe', alpha=0.9)
            ax2.semilogx(frequencies, phase_deg_num, 'r--', linewidth=2, label='Fonction numérique directe', alpha=0.9)

            print(f"Gain DC numérique: {magnitude_db_num[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé numérique: {e}")

    # Calculer et afficher les différences si les deux courbes existent
    if magnitude_db_sym is not None and magnitude_db_num is not None:
        diff_magnitude = np.abs(magnitude_db_sym - magnitude_db_num)
        diff_phase = np.abs(phase_deg_sym - phase_deg_num)

        max_diff_mag = np.max(diff_magnitude)
        max_diff_phase = np.max(diff_phase)

        print(f"\n=== Analyse des différences ===")
        print(f"Différence maximale en magnitude: {max_diff_mag:.2f} dB")
        print(f"Différence maximale en phase: {max_diff_phase:.2f}°")

        # Ajouter les différences comme texte sur le graphique
        ax1.text(0.02, 0.98, f'Diff. max: {max_diff_mag:.2f} dB',
                transform=ax1.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # Configuration des graphiques
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - H1(s) = V7/V1 - Comparaison Symbolique vs Numérique', fontsize=14, fontweight='bold')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(1, 1e12)
    ax1.legend(loc='upper right')

    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(1, 1e12)
    ax2.legend(loc='upper right')

    plt.tight_layout()

    # Nom du fichier de sortie
    output_filename = f'bode_H1_V7_V1_comparison{filename_suffix}.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()

    print(f"\nDiagramme de comparaison sauvegardé: {output_filename}")

def plot_bode_diagram(tf, transfer_name="H(s)", filename_suffix="", color='b'):
    """Trace le diagramme de Bode d'une seule fonction de transfert"""

    if tf is None:
        print("Erreur: Fonction de transfert invalide")
        return

    # Gamme de fréquences (1 Hz à 1 THz)
    frequencies = np.logspace(0, 12, 10000)  # 1 Hz à 1 THz
    omega = 2 * np.pi * frequencies

    # Calcul de la réponse fréquentielle
    _, h = signal.freqresp(tf, omega)

    # Conversion en dB et phase en degrés
    magnitude_db = 20 * np.log10(np.abs(h))
    phase_deg = np.angle(h) * 180 / np.pi

    # Création du graphique
    _, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # Graphique de magnitude
    ax1.semilogx(frequencies, magnitude_db, f'{color}-', linewidth=2)
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title(f'Diagramme de Bode - {transfer_name}')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(1, 1e12)

    # Graphique de phase
    ax2.semilogx(frequencies, phase_deg, f'{color}-', linewidth=2)
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(1, 1e12)

    plt.tight_layout()

    # Nom du fichier de sortie
    output_filename = f'bode_diagram{filename_suffix}.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()

    # Affichage des informations importantes
    print(f"\n=== Analyse du diagramme de Bode ===")
    print(f"Gain DC: {magnitude_db[0]:.1f} dB")

    print(f"\nDiagramme sauvegardé: {output_filename}")

def main():
    """Fonction principale"""

    print("=== Analyse de la fonction de transfert H1(s) = V7/V1 ===")

    # Utiliser le fichier fcs.txt par défaut
    filename = "fcs.txt"

    print(f"Lecture du fichier: {filename}")

    # Vérifier si le fichier existe
    if not os.path.exists(filename):
        print(f"Erreur: Le fichier {filename} n'existe pas dans le répertoire courant.")
        # Essayer vue.txt comme alternative
        filename = "vue.txt"
        if not os.path.exists(filename):
            # Demander le nom du fichier
            filename = input("Entrez le nom du fichier (ex: fcs.txt, vue.txt): ")
            if not filename:
                filename = "fcs.txt"  # Fichier par défaut

    # Parser le fichier de simulation
    transfer_functions = parse_simulation_file(filename)

    if not transfer_functions:
        print("Erreur: Impossible de parser le fichier de simulation")
        return

    # Extraire les paramètres du circuit
    parameters = extract_circuit_parameters(filename)

    if not parameters:
        print("Attention: Aucun paramètre de circuit trouvé")
        parameters = {}

    print(f"\n=== Paramètres extraits ===")
    for param, value in parameters.items():
        print(f"{param} = {value}")

    # Créer les fonctions de transfert
    tf_symbolic = None
    tf_numeric = None

    # Fonction de transfert symbolique avec substitution des paramètres
    if 'symbolic' in transfer_functions:
        print(f"\n=== Traitement de la fonction symbolique ===")
        tf_symbolic = create_transfer_function_from_expressions(
            transfer_functions['symbolic'], parameters, is_numeric=False)

    # Fonction de transfert numérique
    if 'numeric' in transfer_functions:
        print(f"\n=== Traitement de la fonction numérique ===")
        tf_numeric = create_transfer_function_from_expressions(
            transfer_functions['numeric'], {}, is_numeric=True)

    # Tracer les diagrammes de Bode
    if tf_symbolic is not None or tf_numeric is not None:
        print(f"\n=== Génération des diagrammes de Bode ===")

        # Diagramme de comparaison
        plot_bode_comparison(tf_symbolic, tf_numeric, "_H1_V7_V1")

        # Diagrammes individuels si demandé
        if tf_symbolic is not None:
            plot_bode_diagram(tf_symbolic, "H1(s) = V7/V1 (Symbolique)", "_symbolic", 'b')

        if tf_numeric is not None:
            plot_bode_diagram(tf_numeric, "H1(s) = V7/V1 (Numérique)", "_numeric", 'r')
    else:
        print("Erreur: Aucune fonction de transfert n'a pu être créée")

def create_simplified_transfer_function(parameters):
    """Crée une fonction de transfert simplifiée basée sur les paramètres du circuit"""

    # Valeurs par défaut si les paramètres ne sont pas trouvés
    R1 = parameters.get('R1', 100)
    R2 = parameters.get('R2', 10000)
    R3 = parameters.get('R3', 10000)
    C1 = parameters.get('C1', 100e-9)
    C2 = parameters.get('C2', 10e-6)
    C3 = parameters.get('C3', 100e-9)

    print(f"Paramètres utilisés:")
    print(f"R1 = {R1} Ω, R2 = {R2} Ω, R3 = {R3} Ω")
    print(f"C1 = {C1*1e9:.0f} nF, C2 = {C2*1e6:.0f} µF, C3 = {C3*1e9:.0f} nF")

    # Créer une fonction de transfert simple du premier ordre
    # H(s) = K / (1 + s*RC)

    # Fréquence de coupure approximative
    fc = 1 / (2 * np.pi * R2 * C1)

    # Gain DC approximatif
    K = R2 / (R1 + R2)

    print(f"Gain DC approximatif: {K:.3f} ({20*np.log10(K):.1f} dB)")
    print(f"Fréquence de coupure approximative: {fc/1e3:.1f} kHz")

    # Créer la fonction de transfert
    num = [K]
    den = [1/(2*np.pi*fc), 1]

    tf = signal.TransferFunction(num, den)

    return tf

if __name__ == "__main__":
    main()
