#!/usr/bin/env python3
"""
Test rapide pour vérifier que le problème d'encodage est résolu
"""

def test_encoding():
    """Test simple d'encodage"""
    
    print("=== Test d'encodage ===")
    
    # Simuler la fonction verify_symbolic_numeric_consistency
    def mock_verify_consistency():
        # Simuler un cas de succès
        return (True, "0.123", "0.123", None)
    
    def mock_verify_consistency_fail():
        # Simuler un cas d'échec
        return (False, "0.123", "0.456", "Difference: 0.333")
    
    # Test avec succès
    print("Test 1 - Succès:")
    consistency_check = mock_verify_consistency()
    if consistency_check[0]:
        print("OK Coherence symbolique/numerique verifiee")
    else:
        print(f"ATTENTION Difference detectee: {consistency_check[3]}")
        print(f"  Symbolique evalue: {consistency_check[1]}")
        print(f"  Numerique: {consistency_check[2]}")
    
    # Test avec échec
    print("\nTest 2 - Échec:")
    consistency_check = mock_verify_consistency_fail()
    if consistency_check[0]:
        print("OK Coherence symbolique/numerique verifiee")
    else:
        print(f"ATTENTION Difference detectee: {consistency_check[3]}")
        print(f"  Symbolique evalue: {consistency_check[1]}")
        print(f"  Numerique: {consistency_check[2]}")
    
    print("\n✓ Test d'encodage réussi - Aucun caractère Unicode problématique")

if __name__ == "__main__":
    test_encoding()
