--- Simulation Results - 2025-06-14 16:23:37 ---

Netlist originale:
V1 1 0 DC 1 AC 1
R1 1 2 1k
X1 2 0 3 100k 100k 100G 100G 10G
R2 2 3 2k

Temps de calcul parallele: 13.495s

===== DC Analysis (Symbolic) =====

V1 = 1

V2 = R2/(Av_X1*R1+R1+R2)

V3 = -Av_X1*R2/(Av_X1*R1+R1+R2)

I_V1 = (-Av_X1-1)/(Av_X1*R1+R1+R2)

I_R1 = (Av_X1+1)/(Av_X1*R1+R1+R2)

I_R2 = (Av_X1+1)/(Av_X1*R1+R1+R2)

===== DC Analysis (Numerical) =====

V1 = 1.0

V2 = 1.9999400018e-05

V3 = -1.9999400018

I_V1 = -0.0009999800006

I_R1 = 0.0009999800006

I_R2 = 0.0009999800006

===== AC Analysis (Symbolic) =====

V1 = 1

V2 = (1.6e+28*R2*pi^4+8e+22*R2*pi^3*s+8e+16*R2*pi^3*s+400000000000*R2*pi^2*s^2+8e+16*R2*pi^3*s+400000000000*R2*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R2*pi*s^3+8e+27*R2*pi^3*s+4e+22*R2*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R2*pi*s^3+4e+16*R2*pi^2*s^2+200000000000*R2*pi*s^3+200000.0*R2*pi*s^3+R2*s^4)/(16*Av_X1*100000000000*100000000000*100000.0*R1*pi^4+1.6e+28*R1*pi^4+1.6e+28*R2*pi^4+8e+22*R1*pi^3*s+8e+22*R2*pi^3*s+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+400000.0*R1*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R1*pi*s^3+2.0*R2*pi*s^3+8e+27*R1*pi^3*s+8e+27*R2*pi^3*s+4e+22*R1*pi^2*s^2+4e+22*R2*pi^2*s^2+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+200000.0*R1*pi*s^3+200000.0*R2*pi*s^3+R1*s^4+R2*s^4)

V3 = -16*Av_X1*100000000000*100000000000*100000.0*R2*pi^4/(16*Av_X1*100000000000*100000000000*100000.0*R1*pi^4+1.6e+28*R1*pi^4+1.6e+28*R2*pi^4+8e+22*R1*pi^3*s+8e+22*R2*pi^3*s+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+400000.0*R1*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R1*pi*s^3+2.0*R2*pi*s^3+8e+27*R1*pi^3*s+8e+27*R2*pi^3*s+4e+22*R1*pi^2*s^2+4e+22*R2*pi^2*s^2+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+200000.0*R1*pi*s^3+200000.0*R2*pi*s^3+R1*s^4+R2*s^4)

I_V1 = (-16*Av_X1*100000000000*100000000000*100000.0*pi^4-1.55854545654e+30-2.48050213442e+24*s-2.48050213442e+18*s-3.94784176044e+12*s^2-2.48050213442e+18*s-3.94784176044e+12*s^2-3947841.76044*s^2-6.28318530718*s^3-2.48050213442e+29*s-3.94784176044e+23*s^2-3.94784176044e+17*s^2-628318530718*s^3-3.94784176044e+17*s^2-628318530718*s^3-628318.530718*s^3-s^4)/(16*Av_X1*100000000000*100000000000*100000.0*R1*pi^4+1.6e+28*R1*pi^4+1.6e+28*R2*pi^4+8e+22*R1*pi^3*s+8e+22*R2*pi^3*s+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+400000.0*R1*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R1*pi*s^3+2.0*R2*pi*s^3+8e+27*R1*pi^3*s+8e+27*R2*pi^3*s+4e+22*R1*pi^2*s^2+4e+22*R2*pi^2*s^2+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+200000.0*R1*pi*s^3+200000.0*R2*pi*s^3+R1*s^4+R2*s^4)

I_R1 = (16*Av_X1*100000000000*100000000000*100000.0*pi^4+1.55854545654e+30+2.48050213442e+24*s+2.48050213442e+18*s+3.94784176044e+12*s^2+2.48050213442e+18*s+3.94784176044e+12*s^2+3947841.76044*s^2+6.28318530718*s^3+2.48050213442e+29*s+3.94784176044e+23*s^2+3.94784176044e+17*s^2+628318530718*s^3+3.94784176044e+17*s^2+628318530718*s^3+628318.530718*s^3+s^4)/(16*Av_X1*100000000000*100000000000*100000.0*R1*pi^4+1.6e+28*R1*pi^4+1.6e+28*R2*pi^4+8e+22*R1*pi^3*s+8e+22*R2*pi^3*s+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+400000.0*R1*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R1*pi*s^3+2.0*R2*pi*s^3+8e+27*R1*pi^3*s+8e+27*R2*pi^3*s+4e+22*R1*pi^2*s^2+4e+22*R2*pi^2*s^2+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+200000.0*R1*pi*s^3+200000.0*R2*pi*s^3+R1*s^4+R2*s^4)

I_R2 = (16*Av_X1*100000000000*100000000000*100000.0*pi^4+1.55854545654e+30+2.48050213442e+24*s+2.48050213442e+18*s+3.94784176044e+12*s^2+2.48050213442e+18*s+3.94784176044e+12*s^2+3947841.76044*s^2+6.28318530718*s^3+2.48050213442e+29*s+3.94784176044e+23*s^2+3.94784176044e+17*s^2+628318530718*s^3+3.94784176044e+17*s^2+628318530718*s^3+628318.530718*s^3+s^4)/(16*Av_X1*100000000000*100000000000*100000.0*R1*pi^4+1.6e+28*R1*pi^4+1.6e+28*R2*pi^4+8e+22*R1*pi^3*s+8e+22*R2*pi^3*s+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+400000.0*R1*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R1*pi*s^3+2.0*R2*pi*s^3+8e+27*R1*pi^3*s+8e+27*R2*pi^3*s+4e+22*R1*pi^2*s^2+4e+22*R2*pi^2*s^2+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+200000.0*R1*pi*s^3+200000.0*R2*pi*s^3+R1*s^4+R2*s^4)

===== AC Analysis (Numerical) =====

V1 = 1.0

V2 = (3.11709091309e+33+4.96100426885e+27*s+4.96100426885e+21*s+7.89568352087e+15*s^2+4.96100426885e+21*s+7.89568352087e+15*s^2+7895683520.87*s^2+12566.3706144*s^3+4.96100426885e+32*s+7.89568352087e+26*s^2+7.89568352087e+20*s^2+1.25663706144e+15*s^3+7.89568352087e+20*s^2+1.25663706144e+15*s^3+1256637061.44*s^3+2000.0*s^4)/(1.55859221291e+38+2.48050213442e+27*s+4.96100426885e+27*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+3947841760.44*s^2+7895683520.87*s^2+6283.18530718*s^3+12566.3706144*s^3+2.48050213442e+32*s+4.96100426885e+32*s+3.94784176044e+26*s^2+7.89568352087e+26*s^2+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+628318530.718*s^3+1256637061.44*s^3+1000.0*s^4+2000.0*s^4)

V3 = -3.11709091309e+38/(1.55859221291e+38+2.48050213442e+27*s+4.96100426885e+27*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+3947841760.44*s^2+7895683520.87*s^2+6283.18530718*s^3+12566.3706144*s^3+2.48050213442e+32*s+4.96100426885e+32*s+3.94784176044e+26*s^2+7.89568352087e+26*s^2+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+628318530.718*s^3+1256637061.44*s^3+1000.0*s^4+2000.0*s^4)

I_V1 = (-1.558561042e+35-2.48050213442e+24*s-2.48050213442e+18*s-3.94784176044e+12*s^2-2.48050213442e+18*s-3.94784176044e+12*s^2-3947841.76044*s^2-6.28318530718*s^3-2.48050213442e+29*s-3.94784176044e+23*s^2-3.94784176044e+17*s^2-628318530718*s^3-3.94784176044e+17*s^2-628318530718*s^3-628318.530718*s^3-s^4)/(1.55859221291e+38+2.48050213442e+27*s+4.96100426885e+27*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+3947841760.44*s^2+7895683520.87*s^2+6283.18530718*s^3+12566.3706144*s^3+2.48050213442e+32*s+4.96100426885e+32*s+3.94784176044e+26*s^2+7.89568352087e+26*s^2+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+628318530.718*s^3+1256637061.44*s^3+1000.0*s^4+2000.0*s^4)

I_R1 = (1.558561042e+35+2.48050213442e+24*s+2.48050213442e+18*s+3.94784176044e+12*s^2+2.48050213442e+18*s+3.94784176044e+12*s^2+3947841.76044*s^2+6.28318530718*s^3+2.48050213442e+29*s+3.94784176044e+23*s^2+3.94784176044e+17*s^2+628318530718*s^3+3.94784176044e+17*s^2+628318530718*s^3+628318.530718*s^3+s^4)/(1.55859221291e+38+2.48050213442e+27*s+4.96100426885e+27*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+3947841760.44*s^2+7895683520.87*s^2+6283.18530718*s^3+12566.3706144*s^3+2.48050213442e+32*s+4.96100426885e+32*s+3.94784176044e+26*s^2+7.89568352087e+26*s^2+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+628318530.718*s^3+1256637061.44*s^3+1000.0*s^4+2000.0*s^4)

I_R2 = (1.558561042e+35+2.48050213442e+24*s+2.48050213442e+18*s+3.94784176044e+12*s^2+2.48050213442e+18*s+3.94784176044e+12*s^2+3947841.76044*s^2+6.28318530718*s^3+2.48050213442e+29*s+3.94784176044e+23*s^2+3.94784176044e+17*s^2+628318530718*s^3+3.94784176044e+17*s^2+628318530718*s^3+628318.530718*s^3+s^4)/(1.55859221291e+38+2.48050213442e+27*s+4.96100426885e+27*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+3947841760.44*s^2+7895683520.87*s^2+6283.18530718*s^3+12566.3706144*s^3+2.48050213442e+32*s+4.96100426885e+32*s+3.94784176044e+26*s^2+7.89568352087e+26*s^2+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+628318530.718*s^3+1256637061.44*s^3+1000.0*s^4+2000.0*s^4)

===== AC Analysis (Temporal) - f = 1.0 Hz =====

v1(t) = 1.0*cos(6.283185307179586*t + 0.0)

v2(t) = 2.82834227419e-05*cos(6.283185307179586*t + 0.785378164017)

v3(t) = 1.9999400015*cos(6.283185307179586*t + 3.14156265419)

i_V1(t) = 0.000999980000399*cos(6.283185307179586*t + 3.14157265419)

i_R1(t) = 0.000999980000399*cos(6.283185307179586*t + -1.99994000177e-05)

i_R2(t) = 0.000999980000399*cos(6.283185307179586*t + -1.99994000177e-05)

===== Fonction de Transfert 1 =====

H1(s) = V3/V1 = -16*Av_X1*100000000000*100000000000*100000.0*R2*pi^4/(16*Av_X1*100000000000*100000000000*100000.0*R1*pi^4+1.6e+28*R1*pi^4+1.6e+28*R2*pi^4+8e+22*R1*pi^3*s+8e+22*R2*pi^3*s+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+8e+16*R1*pi^3*s+8e+16*R2*pi^3*s+400000000000*R1*pi^2*s^2+400000000000*R2*pi^2*s^2+400000.0*R1*pi^2*s^2+400000.0*R2*pi^2*s^2+2.0*R1*pi*s^3+2.0*R2*pi*s^3+8e+27*R1*pi^3*s+8e+27*R2*pi^3*s+4e+22*R1*pi^2*s^2+4e+22*R2*pi^2*s^2+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+4e+16*R1*pi^2*s^2+4e+16*R2*pi^2*s^2+200000000000*R1*pi*s^3+200000000000*R2*pi*s^3+200000.0*R1*pi*s^3+200000.0*R2*pi*s^3+R1*s^4+R2*s^4)

Fonction symbolique pure (sans substitutions): -16*Av_X1*Fc1_X1*Fc2_X1*Fc3_X1*Fc4_X1*R2*pi^4/(16*Av_X1*Fc1_X1*Fc2_X1*Fc3_X1*Fc4_X1*R1*pi^4+16*Fc1_X1*Fc2_X1*Fc3_X1*Fc4_X1*R1*pi^4+16*Fc1_X1*Fc2_X1*Fc3_X1*Fc4_X1*R2*pi^4+8*Fc1_X1*Fc2_X1*Fc3_X1*R1*pi^3*s+8*Fc1_X1*Fc2_X1*Fc3_X1*R2*pi^3*s+8*Fc1_X1*Fc2_X1*Fc4_X1*R1*pi^3*s+8*Fc1_X1*Fc2_X1*Fc4_X1*R2*pi^3*s+4*Fc1_X1*Fc2_X1*R1*pi^2*s^2+4*Fc1_X1*Fc2_X1*R2*pi^2*s^2+8*Fc1_X1*Fc3_X1*Fc4_X1*R1*pi^3*s+8*Fc1_X1*Fc3_X1*Fc4_X1*R2*pi^3*s+4*Fc1_X1*Fc3_X1*R1*pi^2*s^2+4*Fc1_X1*Fc3_X1*R2*pi^2*s^2+4*Fc1_X1*Fc4_X1*R1*pi^2*s^2+4*Fc1_X1*Fc4_X1*R2*pi^2*s^2+2*Fc1_X1*R1*pi*s^3+2*Fc1_X1*R2*pi*s^3+8*Fc2_X1*Fc3_X1*Fc4_X1*R1*pi^3*s+8*Fc2_X1*Fc3_X1*Fc4_X1*R2*pi^3*s+4*Fc2_X1*Fc3_X1*R1*pi^2*s^2+4*Fc2_X1*Fc3_X1*R2*pi^2*s^2+4*Fc2_X1*Fc4_X1*R1*pi^2*s^2+4*Fc2_X1*Fc4_X1*R2*pi^2*s^2+2*Fc2_X1*R1*pi*s^3+2*Fc2_X1*R2*pi*s^3+4*Fc3_X1*Fc4_X1*R1*pi^2*s^2+4*Fc3_X1*Fc4_X1*R2*pi^2*s^2+2*Fc3_X1*R1*pi*s^3+2*Fc3_X1*R2*pi*s^3+2*Fc4_X1*R1*pi*s^3+2*Fc4_X1*R2*pi*s^3+R1*s^4+R2*s^4)

Fonction de transfert finale: -3.11709091309e+38/(1.55859221291e+38+2.48050213442e+27*s+4.96100426885e+27*s+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+2.48050213442e+21*s+4.96100426885e+21*s+3.94784176044e+15*s^2+7.89568352087e+15*s^2+3947841760.44*s^2+7895683520.87*s^2+6283.18530718*s^3+12566.3706144*s^3+2.48050213442e+32*s+4.96100426885e+32*s+3.94784176044e+26*s^2+7.89568352087e+26*s^2+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+3.94784176044e+20*s^2+7.89568352087e+20*s^2+6.28318530718e+14*s^3+1.25663706144e+15*s^3+628318530.718*s^3+1256637061.44*s^3+1000.0*s^4+2000.0*s^4)

Substitutions coherentes appliquees

Diagramme de Bode affiche

