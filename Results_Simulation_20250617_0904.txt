--- Simulation Results - 2025-06-17 09:04:52 ---

Netlist originale:
V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 1u
R2 2 3 1k
C2 2 3 1u
R3 3 0 1k
L1 3 4 1m
C3 3 4 1u
R4 3 4 1k
R5 4 0 1k

===== DC Analysis (Symbolic) =====

V1 = 1

V2 = (R2*R3+R2*R5+R3*R5)/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

V3 = R3*R5/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

V4 = R3*R5/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

I_V1 = (-R3-R5)/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

I_R1 = (R3+R5)/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

I_C1 = 0

I_R2 = (R3+R5)/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

I_C2 = 0

I_R3 = R5/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

I_L1 = R3/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

I_C3 = 0

I_R4 = 0

I_R5 = R3/(R1*R3+R1*R5+R2*R3+R2*R5+R3*R5)

===== DC Analysis (Numerical) =====

V1 = 1.0

V2 = 0.6

V3 = 0.2

V4 = 0.2

I_V1 = -0.0004

I_R1 = 0.0004

I_C1 = 0.0

I_R2 = 0.0004

I_C2 = 0.0

I_R3 = 0.0002

I_L1 = 0.0002

I_C3 = 0.0

I_R4 = 0.0

I_R5 = 0.0002

===== AC Analysis (Symbolic) =====

V1 = 1

V2 = (C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R2*R3*R4*R5*s+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R2*R3*R4+R2*R4*R5+R3*R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

V3 = (C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R2*R3*R4*R5*s+C3*L1*R3*R4*R5*s^2+L1*R3*R4*s+L1*R3*R5*s+R3*R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

V4 = (C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R2*R3*R5*s^2+C2*R2*R3*R4*R5*s+C3*L1*R3*R4*R5*s^2+L1*R3*R5*s+R3*R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_V1 = (-C1*C2*C3*L1*R2*R3*R4*R5*s^4-C1*C2*L1*R2*R3*R4*s^3-C1*C2*L1*R2*R3*R5*s^3-C1*C2*R2*R3*R4*R5*s^2-C1*C3*L1*R2*R3*R4*s^3-C1*C3*L1*R2*R4*R5*s^3-C1*C3*L1*R3*R4*R5*s^3-C1*L1*R2*R3*s^2-C1*L1*R2*R4*s^2-C1*L1*R2*R5*s^2-C1*L1*R3*R4*s^2-C1*L1*R3*R5*s^2-C1*R2*R3*R4*s-C1*R2*R4*R5*s-C1*R3*R4*R5*s-C2*C3*L1*R2*R3*R4*s^3-C2*C3*L1*R2*R4*R5*s^3-C2*L1*R2*R3*s^2-C2*L1*R2*R4*s^2-C2*L1*R2*R5*s^2-C2*R2*R3*R4*s-C2*R2*R4*R5*s-C3*L1*R3*R4*s^2-C3*L1*R4*R5*s^2-L1*R3*s-L1*R4*s-L1*R5*s-R3*R4-R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_R1 = (C1*C2*C3*L1*R2*R3*R4*R5*s^4+C1*C2*L1*R2*R3*R4*s^3+C1*C2*L1*R2*R3*R5*s^3+C1*C2*R2*R3*R4*R5*s^2+C1*C3*L1*R2*R3*R4*s^3+C1*C3*L1*R2*R4*R5*s^3+C1*C3*L1*R3*R4*R5*s^3+C1*L1*R2*R3*s^2+C1*L1*R2*R4*s^2+C1*L1*R2*R5*s^2+C1*L1*R3*R4*s^2+C1*L1*R3*R5*s^2+C1*R2*R3*R4*s+C1*R2*R4*R5*s+C1*R3*R4*R5*s+C2*C3*L1*R2*R3*R4*s^3+C2*C3*L1*R2*R4*R5*s^3+C2*L1*R2*R3*s^2+C2*L1*R2*R4*s^2+C2*L1*R2*R5*s^2+C2*R2*R3*R4*s+C2*R2*R4*R5*s+C3*L1*R3*R4*s^2+C3*L1*R4*R5*s^2+L1*R3*s+L1*R4*s+L1*R5*s+R3*R4+R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_C1 = (C1*C2*C3*L1*R2*R3*R4*R5*s^4+C1*C2*L1*R2*R3*R4*s^3+C1*C2*L1*R2*R3*R5*s^3+C1*C2*R2*R3*R4*R5*s^2+C1*C3*L1*R2*R3*R4*s^3+C1*C3*L1*R2*R4*R5*s^3+C1*C3*L1*R3*R4*R5*s^3+C1*L1*R2*R3*s^2+C1*L1*R2*R4*s^2+C1*L1*R2*R5*s^2+C1*L1*R3*R4*s^2+C1*L1*R3*R5*s^2+C1*R2*R3*R4*s+C1*R2*R4*R5*s+C1*R3*R4*R5*s)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_R2 = (C3*L1*R3*R4*s^2+C3*L1*R4*R5*s^2+L1*R3*s+L1*R4*s+L1*R5*s+R3*R4+R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_C2 = (C2*C3*L1*R2*R3*R4*s^3+C2*C3*L1*R2*R4*R5*s^3+C2*L1*R2*R3*s^2+C2*L1*R2*R4*s^2+C2*L1*R2*R5*s^2+C2*R2*R3*R4*s+C2*R2*R4*R5*s)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_R3 = (C2*C3*L1*R2*R4*R5*s^3+C2*L1*R2*R4*s^2+C2*L1*R2*R5*s^2+C2*R2*R4*R5*s+C3*L1*R4*R5*s^2+L1*R4*s+L1*R5*s+R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_L1 = (C2*R2*R3*R4*s+R3*R4)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_C3 = (C2*C3*L1*R2*R3*R4*s^3+C3*L1*R3*R4*s^2)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_R4 = (C2*L1*R2*R3*s^2+L1*R3*s)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

I_R5 = (C2*C3*L1*R2*R3*R4*s^3+C2*L1*R2*R3*s^2+C2*R2*R3*R4*s+C3*L1*R3*R4*s^2+L1*R3*s+R3*R4)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

===== AC Analysis (Numerical) =====

V1 = 1.0

V2 = (0.001*s^3+s^2+s^2+1000000.0*s+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+3000000000)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

V3 = (0.001*s^3+s^2+s^2+1000000.0*s+s^2+1000.0*s+1000.0*s+1000000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

V4 = (0.001*s^3+s^2+1000000.0*s+s^2+1000.0*s+1000000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_V1 = (-1e-09*s^4-1e-06*s^3-1e-06*s^3-s^2-1e-06*s^3-1e-06*s^3-1e-06*s^3-0.001*s^2-0.001*s^2-0.001*s^2-0.001*s^2-0.001*s^2-1000.0*s-1000.0*s-1000.0*s-1e-06*s^3-1e-06*s^3-0.001*s^2-0.001*s^2-0.001*s^2-1000.0*s-1000.0*s-0.001*s^2-0.001*s^2-s-s-s-2000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_R1 = (1e-09*s^4+1e-06*s^3+1e-06*s^3+s^2+1e-06*s^3+1e-06*s^3+1e-06*s^3+0.001*s^2+0.001*s^2+0.001*s^2+0.001*s^2+0.001*s^2+1000.0*s+1000.0*s+1000.0*s+1e-06*s^3+1e-06*s^3+0.001*s^2+0.001*s^2+0.001*s^2+1000.0*s+1000.0*s+0.001*s^2+0.001*s^2+s+s+s+2000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_C1 = (1e-09*s^4+1e-06*s^3+1e-06*s^3+s^2+1e-06*s^3+1e-06*s^3+1e-06*s^3+0.001*s^2+0.001*s^2+0.001*s^2+0.001*s^2+0.001*s^2+1000.0*s+1000.0*s+1000.0*s)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_R2 = (0.001*s^2+0.001*s^2+s+s+s+2000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_C2 = (1e-06*s^3+1e-06*s^3+0.001*s^2+0.001*s^2+0.001*s^2+1000.0*s+1000.0*s)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_R3 = (1e-06*s^3+0.001*s^2+0.001*s^2+1000.0*s+0.001*s^2+s+s+1000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_L1 = (1000.0*s+1000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_C3 = (1e-06*s^3+0.001*s^2)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_R4 = (0.001*s^2+s)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

I_R5 = (1e-06*s^3+0.001*s^2+1000.0*s+0.001*s^2+s+1000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

===== AC Analysis (Temporal) - f = 1.0 Hz =====

v1(t) = 1.0*cos(6.283185307179586*t + 0.0)

v2(t) = 0.599988998279*cos(6.283185307179586*t + -0.00544492810138)

v3(t) = 0.199999842724*cos(6.283185307179586*t + -0.00125412312975)

v4(t) = 0.199999842712*cos(6.283185307179586*t + -0.0012604063153)

i_V1(t) = 0.000400033235461*cos(6.283185307179586*t + -3.13342603932)

i_R1(t) = 0.000400033235461*cos(6.283185307179586*t + 0.00816661426822)

i_C1(t) = 3.76984205845e-06*cos(6.283185307179586*t + 1.56535139869)

i_R2(t) = 0.00039999178999*cos(6.283185307179586*t + -0.00754036734826)

i_C2(t) = 2.51322253786e-06*cos(6.283185307179586*t + 1.56325595945)

i_R3(t) = 0.000199999842724*cos(6.283185307179586*t + -0.00125412312975)

i_L1(t) = 0.000199999850604*cos(6.283185307179586*t + -0.00126668950086)

i_C3(t) = 7.89567762294e-12*cos(6.283185307179586*t + 3.14032596409)

i_R4(t) = 1.25663612275e-09*cos(6.283185307179586*t + 1.56952963729)

i_R5(t) = 0.000199999842712*cos(6.283185307179586*t + -0.0012604063153)

===== Fonction de Transfert 1 =====

H1(s) = V4/V1 = (C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R2*R3*R5*s^2+C2*R2*R3*R4*R5*s+C3*L1*R3*R4*R5*s^2+L1*R3*R5*s+R3*R4*R5)/(C1*C2*C3*L1*R1*R2*R3*R4*R5*s^4+C1*C2*L1*R1*R2*R3*R4*s^3+C1*C2*L1*R1*R2*R3*R5*s^3+C1*C2*R1*R2*R3*R4*R5*s^2+C1*C3*L1*R1*R2*R3*R4*s^3+C1*C3*L1*R1*R2*R4*R5*s^3+C1*C3*L1*R1*R3*R4*R5*s^3+C1*L1*R1*R2*R3*s^2+C1*L1*R1*R2*R4*s^2+C1*L1*R1*R2*R5*s^2+C1*L1*R1*R3*R4*s^2+C1*L1*R1*R3*R5*s^2+C1*R1*R2*R3*R4*s+C1*R1*R2*R4*R5*s+C1*R1*R3*R4*R5*s+C2*C3*L1*R1*R2*R3*R4*s^3+C2*C3*L1*R1*R2*R4*R5*s^3+C2*C3*L1*R2*R3*R4*R5*s^3+C2*L1*R1*R2*R3*s^2+C2*L1*R1*R2*R4*s^2+C2*L1*R1*R2*R5*s^2+C2*L1*R2*R3*R4*s^2+C2*L1*R2*R3*R5*s^2+C2*R1*R2*R3*R4*s+C2*R1*R2*R4*R5*s+C2*R2*R3*R4*R5*s+C3*L1*R1*R3*R4*s^2+C3*L1*R1*R4*R5*s^2+C3*L1*R2*R3*R4*s^2+C3*L1*R2*R4*R5*s^2+C3*L1*R3*R4*R5*s^2+L1*R1*R3*s+L1*R1*R4*s+L1*R1*R5*s+L1*R2*R3*s+L1*R2*R4*s+L1*R2*R5*s+L1*R3*R4*s+L1*R3*R5*s+R1*R3*R4+R1*R4*R5+R2*R3*R4+R2*R4*R5+R3*R4*R5)

Fonction de transfert finale: (0.001*s^3+s^2+1000000.0*s+s^2+1000.0*s+1000000000.0)/(1e-06*s^4+0.001*s^3+0.001*s^3+1000.0*s^2+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+0.001*s^3+0.001*s^3+0.001*s^3+s^2+s^2+s^2+s^2+s^2+1000000.0*s+1000000.0*s+1000000.0*s+s^2+s^2+s^2+s^2+s^2+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+1000.0*s+5000000000)

Substitutions numeriques appliquees:

  R1 = 1000.0

  C1 = 1e-06

  R2 = 1000.0

  C2 = 1e-06

  R3 = 1000.0

  L1 = 0.001

  C3 = 1e-06

  R4 = 1000.0

  R5 = 1000.0

