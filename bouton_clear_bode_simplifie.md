# Bouton "Clear - Enlever courbes ajoutées"

## Fonctionnalité ajoutée

J'ai ajouté un bouton **"Clear - Enlever courbes ajoutées"** qui permet de supprimer l'affichage de **toutes les courbes supplémentaires** du graphique de Bode :
- Courbes de l'expression simplifiée (rouge pointillées)
- Courbes de l'ajustement manuel (vertes pointillées)

## Problème résolu

**Avant :** Une fois les expressions simplifiées ou manuelles affichées, il n'y avait aucun moyen de les enlever du graphique sans fermer l'application.

**Après :** L'utilisateur peut maintenant facilement enlever toutes les courbes supplémentaires avec un simple clic.

## Modifications apportées

### 1. Nouveau bouton dans l'interface

```python
# Ajouter le bouton Clear pour enlever toutes les courbes supplémentaires
self.clear_all_btn = QPushButton("Clear - Enlever courbes ajoutées")
self.clear_all_btn.setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; padding: 10px; }")
self.clear_all_btn.clicked.connect(self.clear_all_additional_curves)
self.clear_all_btn.setEnabled(False)  # Désactivé au début
sliders_layout.addWidget(self.clear_all_btn)
```

**Caractéristiques du bouton :**
- **Couleur rouge** (#F44336) pour indiquer une action de suppression
- **Texte blanc** en gras pour la visibilité
- **Désactivé par défaut** (quand aucune courbe supplémentaire n'est affichée)
- **Activé automatiquement** quand des courbes simplifiées OU manuelles sont ajoutées

### 2. Nouvelle méthode `clear_all_additional_curves()`

```python
def clear_all_additional_curves(self):
    """Enlève toutes les courbes supplémentaires (simplifiées et manuelles) du graphique"""
    try:
        curves_removed = []

        # Supprimer les courbes simplifiées si elles existent
        if self.magnitude_curve_simplified:
            self.magnitude_plot.removeItem(self.magnitude_curve_simplified)
            self.magnitude_curve_simplified = None
            curves_removed.append("magnitude simplifiée")

        if self.phase_curve_simplified:
            self.phase_plot.removeItem(self.phase_curve_simplified)
            self.phase_curve_simplified = None
            curves_removed.append("phase simplifiée")

        # Supprimer les courbes manuelles si elles existent
        if self.magnitude_curve_manual:
            self.magnitude_plot.removeItem(self.magnitude_curve_manual)
            self.magnitude_curve_manual = None
            curves_removed.append("magnitude manuelle")

        if self.phase_curve_manual:
            self.phase_plot.removeItem(self.phase_curve_manual)
            self.phase_curve_manual = None
            curves_removed.append("phase manuelle")

        # Réinitialiser les expressions
        self.simplified_expression = None
        self.manual_expression = None

        # Remettre les titres originaux
        self.magnitude_plot.setTitle("Magnitude (dB)")
        self.phase_plot.setTitle("Phase (degrés)")

        # Désactiver le bouton Clear
        self.clear_all_btn.setEnabled(False)

        if curves_removed:
            print(f"Courbes supprimées: {', '.join(curves_removed)}")
            print("Toutes les courbes supplémentaires ont été supprimées avec succès")
        else:
            print("Aucune courbe supplémentaire à supprimer")

    except Exception as e:
        print(f"Erreur lors de la suppression des courbes: {e}")
```

### 3. Activation/désactivation automatique du bouton

**Dans `add_simplified_curves()` et `add_manual_curves()` :**
```python
# Activer le bouton Clear
self.clear_all_btn.setEnabled(True)
```

**Dans `clear_all_additional_curves()` :**
```python
# Désactiver le bouton Clear
self.clear_all_btn.setEnabled(False)
```

## Workflow utilisateur

### Avant (problématique)
1. Simplifier la fonction de transfert
2. Les courbes simplifiées s'affichent automatiquement
3. **Impossible de les enlever** ❌
4. **Obligation de fermer l'application** pour avoir un graphique propre ❌

### Après (solution)
1. Simplifier la fonction de transfert
2. Les courbes simplifiées s'affichent automatiquement
3. **Le bouton "Clear" devient actif** ✅
4. **Clic sur "Clear"** → courbes simplifiées supprimées ✅
5. **Graphique redevient propre** avec seulement la courbe originale ✅

## Actions effectuées par le bouton Clear

Quand l'utilisateur clique sur "Clear - Enlever Bode simplifié" :

1. **Supprime les courbes graphiques :**
   - Courbe de magnitude simplifiée (rouge pointillée)
   - Courbe de phase simplifiée (rouge pointillée)

2. **Nettoie les variables :**
   - `self.magnitude_curve_simplified = None`
   - `self.phase_curve_simplified = None`
   - `self.simplified_expression = None`

3. **Remet les titres originaux :**
   - "Magnitude (dB)" au lieu de "Magnitude (dB) - Original vs Simplifié"
   - "Phase (degrés)" au lieu de "Phase (degrés) - Original vs Simplifié"

4. **Désactive le bouton :**
   - Le bouton devient gris et non-cliquable
   - Évite les clics inutiles quand aucune courbe simplifiée n'est affichée

5. **Messages informatifs :**
   - Affiche des messages dans la console confirmant les suppressions

## États du bouton

| État | Condition | Apparence | Action |
|------|-----------|-----------|--------|
| **Désactivé** | Aucune courbe simplifiée affichée | Gris, non-cliquable | Aucune |
| **Activé** | Courbes simplifiées présentes | Rouge, cliquable | Supprime les courbes |

## Avantages

✅ **Contrôle utilisateur** : L'utilisateur décide quand enlever les courbes
✅ **Interface propre** : Possibilité de revenir à un graphique simple
✅ **Feedback visuel** : Le bouton indique clairement son état
✅ **Sécurité** : Impossible de cliquer quand aucune courbe n'est présente
✅ **Réversible** : L'utilisateur peut re-simplifier après avoir effacé

## Messages informatifs

Le système affiche dans la console :

- `"Courbe de magnitude simplifiée supprimée"`
- `"Courbe de phase simplifiée supprimée"`
- `"Courbes simplifiées supprimées avec succès"`

## Cas d'usage typiques

### Cas 1 : Comparaison temporaire
1. Simplifier pour voir la différence
2. Analyser les courbes
3. Cliquer "Clear" pour revenir au graphique original

### Cas 2 : Simplifications multiples
1. Simplifier avec un seuil
2. Cliquer "Clear"
3. Re-simplifier avec un autre seuil
4. Comparer les résultats

### Cas 3 : Présentation propre
1. Faire des analyses avec simplification
2. Cliquer "Clear" avant de présenter
3. Graphique propre avec seulement la courbe originale

Cette fonctionnalité rend l'outil beaucoup plus flexible et agréable à utiliser !
