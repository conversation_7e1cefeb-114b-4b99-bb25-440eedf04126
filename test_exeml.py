#!/usr/bin/env python3
"""
Test du code exeml avec une expression simple
"""

from exeml import (
    read_symbolic_expression,
    extract_variables,
    define_component_values,
    analyze_variable_sensitivity_parallel,
    simplify_transfer_function_symbolic,
    plot_original_vs_simplified
)

def test_with_simple_expression():
    """Test avec une expression simple"""
    
    # Utiliser l'expression de test
    file_path = "test_expression.txt"
    print(f"Test avec le fichier : {file_path}")
    
    expression = read_symbolic_expression(file_path)
    if expression is None:
        print("Erreur : impossible de lire le fichier")
        return
    
    print(f"Expression de test :")
    print(f"{expression}")
    print(f"Longueur : {len(expression)} caractères")
    
    # Extraire les variables
    variables = extract_variables(expression)
    component_values = define_component_values(variables)
    
    print(f"\nVariables trouvées : {variables}")
    print(f"Valeurs des composants :")
    for var, val in component_values.items():
        print(f"  {var} = {val:.3e}")
    
    # Analyse de sensibilité rapide
    print(f"\nAnalyse de sensibilité...")
    sensitivity_results = analyze_variable_sensitivity_parallel(expression, component_values, num_processes=2)
    
    if sensitivity_results is None:
        print("Erreur dans l'analyse de sensibilité")
        return
    
    print(f"\nRésultats de sensibilité :")
    for result in sensitivity_results:
        print(f"  {result['variable']}: {result['impact_score']:.3f} dB")
    
    # Test de simplification
    print(f"\n{'='*60}")
    print("TEST DE SIMPLIFICATION")
    print(f"{'='*60}")
    
    simplified_expr, removed_vars = simplify_transfer_function_symbolic(
        expression, component_values, sensitivity_results, max_error_db=2.0
    )
    
    if simplified_expr:
        print(f"\n{'='*60}")
        print("RÉSULTAT FINAL")
        print(f"{'='*60}")
        print(f"Expression originale :")
        print(f"{expression}")
        print(f"Longueur : {len(expression)} caractères")
        print(f"\nExpression simplifiée :")
        print(f"{simplified_expr}")
        print(f"Longueur : {len(simplified_expr)} caractères")

        reduction = ((len(expression) - len(simplified_expr)) / len(expression)) * 100
        print(f"\nRéduction de taille : {reduction:.1f}%")

        if removed_vars:
            print(f"Variables supprimées : {[var[0] for var in removed_vars]}")

            # Afficher la comparaison Bode
            print(f"\nAffichage de la comparaison Bode...")
            plot_original_vs_simplified(expression, simplified_expr, component_values)
    else:
        print("Aucune simplification possible")

if __name__ == "__main__":
    test_with_simple_expression()
