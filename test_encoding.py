#!/usr/bin/env python3
"""
Test pour vérifier que la correction d'encodage fonctionne
"""

def test_encoding():
    print("=== Test d'encodage pour les diodes ===")
    
    # Test des messages qui causaient l'erreur
    name_d = "D1"
    Id_dc_numeric = 0.028
    rd_calculated = 0.929
    
    # Messages corrigés (sans caractère Ω)
    print(f"Diode {name_d}: Id_dc = {Id_dc_numeric:.6f} A, rd = {rd_calculated:.3f} Ohm (calculee)")
    print(f"Diode {name_d}: Diode bloquee, rd = 1MOhm (par defaut)")
    print(f"Diode {name_d}: Erreur calcul rd, utilisation valeur par defaut 10Ohm")
    
    # Test des paramètres
    Vth_val = "2.2"
    Vt_val = "0.026"
    rd_val = "0.929"
    Id_dc_val = "0.028"
    
    print(f"\n===== Diode {name_d} parameters =====")
    print(f"  Vth_{name_d} (Tension de seuil) = {Vth_val} V")
    print(f"  Vt_{name_d} (Tension thermique) = {Vt_val} V")
    print(f"  rd_{name_d} (Resistance dynamique) = {rd_val} Ohm")
    print(f"  Id_dc_{name_d} (Courant DC) = {Id_dc_val} A")
    print(f"  Note: rd = Vt / |Id_dc| = {Vt_val} / |{Id_dc_val}| = {rd_val} Ohm")
    
    print("\n✓ Tous les messages s'affichent correctement sans erreur d'encodage")
    print("✓ Le caractère Ω a été remplacé par 'Ohm'")
    print("✓ Les caractères accentués ont été supprimés")

if __name__ == "__main__":
    test_encoding()
