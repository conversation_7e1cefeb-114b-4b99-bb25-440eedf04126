#!/usr/bin/env python3

# Test de fonction de transfert avec amplificateur opérationnel

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

# Amplificateur non-inverseur avec fonction de transfert
netlist_example = """V1 1 0 DC 0 AC 1
R1 1 2 1k
OP1 0 2 3 100000
R2 3 0 9k
R3 3 4 1k
R4 4 0 1k"""

print("Test de fonction de transfert avec amplificateur opérationnel:")
print("Configuration: Amplificateur non-inverseur avec gain = 1 + R2/R1 = 1 + 9k/1k = 10")
print(netlist_example)
print("\n" + "="*50 + "\n")

# Test avec fonction de transfert
try:
    solve_circuit(netlist_example, 
                 frequency_hz=1000.0, 
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='4', 
                 freq_min=1.0, 
                 freq_max=100000.0, 
                 laplace_domain=True)
    print("Test avec fonction de transfert réussi!")
except Exception as e:
    print(f"Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
