#!/usr/bin/env python3
"""
Script de test pour vérifier la syntaxe de bode_H1_final.py
"""

import sys
import ast

def check_syntax(filename):
    """Vérifie la syntaxe d'un fichier Python"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Compiler le code pour vérifier la syntaxe
        ast.parse(source)
        print(f"✓ Syntaxe correcte pour {filename}")
        return True
        
    except SyntaxError as e:
        print(f"✗ Erreur de syntaxe dans {filename}:")
        print(f"  Ligne {e.lineno}: {e.text}")
        print(f"  {' ' * (e.offset - 1)}^")
        print(f"  {e.msg}")
        return False
        
    except Exception as e:
        print(f"✗ Erreur lors de la lecture de {filename}: {e}")
        return False

if __name__ == "__main__":
    filename = "bode_H1_final.py"
    if check_syntax(filename):
        print("Le fichier peut être importé sans erreurs de syntaxe.")
    else:
        print("Le fichier contient des erreurs de syntaxe.")
        sys.exit(1)
