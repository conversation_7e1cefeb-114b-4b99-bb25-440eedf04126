#!/usr/bin/env python3

# Test final de l'amplificateur opérationnel avec réponse fréquentielle corrigée

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import solve_circuit

print("=== Test final de l'amplificateur opérationnel ===")
print("Configuration: Amplificateur inverseur")
print("Gain théorique = -R2/R1 = -2k/1k = -2")
print("fc1 = 1MHz, fc2 = 10MHz")
print()

# Votre netlist exacte
netlist_final = """V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 2 0 3 100k 1M 10M
R2 2 3 2k"""

print("Netlist:")
print(netlist_final)
print()

try:
    solve_circuit(netlist_final, 
                 frequency_hz=1000.0,     # Test à 1kHz (bien en dessous de fc1)
                 do_transfer_function=True, 
                 input_node='1', 
                 output_node='3', 
                 freq_min=100.0,          # 100Hz à 1GHz pour voir toute la réponse
                 freq_max=1000000000.0, 
                 laplace_domain=True)
    
    print("✓ Test terminé avec succès!")
    print()
    print("Vérifications attendues dans le diagramme de Bode :")
    print("1. Gain constant ≈ -2 (≈ 6dB) jusqu'à 1MHz")
    print("2. Décroissance -20dB/décade entre 1MHz et 10MHz")
    print("3. Décroissance -40dB/décade au-delà de 10MHz")
    print("4. Pas de pic de résonance anormal")
    
except Exception as e:
    print(f"✗ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
