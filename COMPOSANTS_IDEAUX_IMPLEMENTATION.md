# Implémentation du mode composants actifs idéaux

## Objectif
Permettre à l'utilisateur de choisir entre des composants actifs idéaux ou réalistes. En mode idéal :
- Les capacités parasites des BJT sont ignorées (Cbe = 0, Cbc = 0)
- Les fréquences de coupure des amplificateurs opérationnels sont ignorées (gain constant)

## Modifications apportées

### 1. Fonction `get_analysis_parameters()` (ligne 697)
**Ajout de la question sur les composants idéaux :**
```python
# Question pour les composants actifs idéaux
ideal_components_choice = input("Souhaitez-vous considerer les composants actifs comme ideaux? (o/n) [defaut: n]: ").strip().lower()
ideal_components = ideal_components_choice in ['o', 'oui', 'y', 'yes']
```

**Modification du retour :**
```python
return frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max, ideal_components
```

### 2. Fonction `solve_circuit()` (ligne 917)
**Modification de la signature :**
```python
def solve_circuit(netlist_str, frequency_hz=1.0, do_transfer_function=False, input_node=None, output_node=None, freq_min=None, freq_max=None, laplace_domain=True, ideal_components=False):
```

**Traitement des BJT en mode idéal (ligne 965) :**
```python
# En mode idéal, les capacités parasites sont nulles
if ideal_components:
    values[f"Cbe_{comp_name}"] = "0"
    values[f"Cbc_{comp_name}"] = "0"
else:
    values[f"Cbe_{comp_name}"] = parse_value(comp[6]) if len(comp) > 6 else "0"
    values[f"Cbc_{comp_name}"] = parse_value(comp[7]) if len(comp) > 7 else "0"
```

**Traitement des amplificateurs opérationnels en mode idéal (ligne 977) :**
```python
# En mode idéal, pas de fréquences de coupure (gain constant)
if ideal_components:
    # Fréquences de coupure très élevées pour simuler un comportement idéal
    values[f"Fc1_{comp_name}"] = "1e15"  # Très haute fréquence
    values[f"Fc2_{comp_name}"] = "1e15"
    values[f"Fc3_{comp_name}"] = "1e15"
    values[f"Fc4_{comp_name}"] = "1e15"
else:
    # Mode réaliste avec fréquences de coupure
    [calculs des fréquences de coupure existants]
```

### 3. Fonction `ac_analysis()` (ligne 330)
**Modification de la signature :**
```python
def ac_analysis(netlist, values, nodes, laplace_domain, frequency_hz=None, ideal_components=False):
```

**Traitement des BJT en mode idéal (ligne 407) :**
```python
# En mode idéal, ignorer les capacités parasites
if ideal_components:
    cbe_sym, cbc_sym = '0', '0'
else:
    cbe_sym, cbc_sym = (f"Cbe_{name_q}" if len(comp) > 6 else '0'), (f"Cbc_{name_q}" if len(comp) > 7 else '0')
```

**Traitement des amplificateurs opérationnels en mode idéal (ligne 461) :**
```python
if laplace_domain and not ideal_components:
    # Mode réaliste avec fréquences de coupure
    den_fc1, den_fc2, den_fc3, den_fc4 = f"(1 + s / (2 * pi * {fc1_sym}))", f"(1 + s / (2 * pi * {fc2_sym}))", f"(1 + s / (2 * pi * {fc3_sym}))", f"(1 + s / (2 * pi * {fc4_sym}))"
    gain_s = f"({av_sym}) / (({den_fc1}) * ({den_fc2}) * ({den_fc3}) * ({den_fc4}))"
else: 
    # Mode idéal ou temporel : gain constant
    gain_s = av_sym
```

### 4. Appel à `ac_analysis()` (ligne 1058)
**Modification :**
```python
voltages, currents, currents_for_bjt_ac_estimation_symbolic = ac_analysis(netlist, values, all_unique_nodes, laplace_domain, frequency_hz=1.0, ideal_components=ideal_components)
```

### 5. Affichage informatif des paramètres BJT (ligne 1102)
**Ajout d'indication du mode :**
```python
if ideal_components:
    file_print(f"  Cbe_{name_q} (Capacite Base-Émetteur) = {values[f'Cbe_{name_q}']} F (IGNOREE - Mode ideal)")
    file_print(f"  Cbc_{name_q} (Capacite Base-Collecteur) = {values[f'Cbc_{name_q}']} F (IGNOREE - Mode ideal)")
else:
    file_print(f"  Cbe_{name_q} (Capacite Base-Émetteur) = {values[f'Cbe_{name_q}']} F")
    file_print(f"  Cbc_{name_q} (Capacite Base-Collecteur) = {values[f'Cbc_{name_q}']} F")
```

### 6. Nouvelle section d'affichage des amplificateurs opérationnels (ligne 1116)
**Ajout d'une section complète :**
```python
# Affichage des paramètres des amplificateurs opérationnels
op_components_info = [comp for comp in netlist if comp[0][0].upper() == 'X']
for comp in op_components_info:
    name_op = comp[0]
    file_print(f"===== Amplificateur operationnel {name_op} parameters =====")
    file_print(f"  Av_{name_op} (Gain en boucle ouverte) = {values[f'Av_{name_op}']}")
    if ideal_components:
        file_print(f"  Mode IDEAL active - Frequences de coupure ignorees")
        file_print(f"  Gain constant sur toute la bande de frequence")
    else:
        file_print(f"  Mode REALISTE - Reponse frequentielle active")
        file_print(f"  Fc1_{name_op} = {values[f'Fc1_{name_op}']} Hz")
        file_print(f"  Fc2_{name_op} = {values[f'Fc2_{name_op}']} Hz") 
        file_print(f"  Fc3_{name_op} = {values[f'Fc3_{name_op}']} Hz")
        file_print(f"  Fc4_{name_op} = {values[f'Fc4_{name_op}']} Hz")
        file_print(f"  Fonction de transfert: Av / ((1+s/(2*pi*Fc1))*(1+s/(2*pi*Fc2))*(1+s/(2*pi*Fc3))*(1+s/(2*pi*Fc4)))")
    file_print("")
```

### 7. Main function (ligne 1295)
**Modification de l'appel :**
```python
frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max, ideal_components = get_analysis_parameters()
print("\nDebut des calculs...")
if ideal_components:
    print("Mode composants actifs ideaux active")
else:
    print("Mode composants actifs realistes active")
solve_circuit(netlist_str, frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max, laplace_domain=True, ideal_components=ideal_components)
```

## Comportement du mode idéal

### Pour les BJT :
- **Capacités parasites ignorées** : Cbe = 0, Cbc = 0 dans tous les calculs
- **Modèle simplifié** : Seules les résistances rpi, ro et la transconductance gm sont considérées
- **Réponse fréquentielle** : Pas de limitation haute fréquence due aux capacités

### Pour les amplificateurs opérationnels :
- **Gain constant** : Av reste constant sur toute la bande de fréquence
- **Pas de rolloff** : Aucune atténuation en fonction de la fréquence
- **Fréquences de coupure ignorées** : Fc1-Fc4 fixées à 1e15 Hz (très haute fréquence)

## Interface utilisateur

### Question posée :
```
Souhaitez-vous considerer les composants actifs comme ideaux? (o/n) [defaut: n]:
```

### Réponses acceptées :
- **Mode idéal** : 'o', 'oui', 'y', 'yes'
- **Mode réaliste** : 'n', 'non', ou toute autre réponse (défaut)

### Confirmation affichée :
```
Mode composants actifs ideaux active
```
ou
```
Mode composants actifs realistes active
```

## Avantages

1. **Simplicité d'analyse** : Permet d'étudier le comportement de base sans effets parasites
2. **Comparaison** : Facilite la comparaison entre comportement idéal et réaliste
3. **Pédagogie** : Utile pour l'enseignement des concepts de base
4. **Débogage** : Aide à identifier si les problèmes viennent des effets parasites

## Compatibilité

- **Rétrocompatible** : Le mode par défaut reste le mode réaliste
- **Transparent** : Aucun impact sur les circuits sans composants actifs
- **Flexible** : Choix fait au début de chaque simulation
