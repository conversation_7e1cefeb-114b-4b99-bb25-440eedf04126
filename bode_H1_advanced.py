#!/usr/bin/env python3
"""
Script avancé pour tracer le diagramme de Bode de H1(s) = V7/V1
à partir du fichier Results_Simulation_20250614_1139.txt
avec traitement des expressions symboliques et numériques réelles
"""
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import sympy as sp
import re

def extract_h1_functions(filename):
    """Extrait les fonctions de transfert H1(s) = V7/V1 du fichier"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extraire la fonction symbolique
    h1_symbolic_match = re.search(r'H1\(s\) = V7/V1 = (.+?)(?=Fonction de transfert num.*?rique|$)', content, re.DOTALL)
    
    if not h1_symbolic_match:
        print("Erreur: Fonction de transfert symbolique H1(s) non trouvée")
        return None, None
    
    h1_symbolic = h1_symbolic_match.group(1).strip()
    print(f"Fonction symbolique trouvée: {len(h1_symbolic)} caractères")
    
    # Extraire la fonction numérique
    h1_numeric_match = re.search(r'Fonction de transfert num.*?rique:\s*(.+?)(?=\n\n|$)', content, re.DOTALL)
    
    if not h1_numeric_match:
        print("Erreur: Fonction de transfert numérique non trouvée")
        return h1_symbolic, None
    
    h1_numeric = h1_numeric_match.group(1).strip()
    print(f"Fonction numérique trouvée: {len(h1_numeric)} caractères")
    
    return h1_symbolic, h1_numeric

def extract_parameters(filename):
    """Extrait les paramètres numériques du circuit"""
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    parameters = {}
    
    # Extraire la netlist
    netlist_match = re.search(r'Netlist originale:(.*?)(?=Temps de calcul|$)', content, re.DOTALL)
    if netlist_match:
        netlist = netlist_match.group(1)
        
        # Résistances
        r_matches = re.findall(r'R(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[kmMG]?)', netlist)
        for r_num, r_val in r_matches:
            val = parse_value(r_val)
            parameters[f'R{r_num}'] = val
        
        # Capacités
        c_matches = re.findall(r'C(\d+)\s+\d+\s+\d+\s+(\d+(?:\.\d+)?[pnumkMG]?[Ff]?)', netlist)
        for c_num, c_val in c_matches:
            val = parse_value(c_val)
            parameters[f'C{c_num}'] = val
    
    # Paramètres BJT
    bjt_match = re.search(r'--- Valeurs numériques retenues pour Q1 ---(.*?)(?====|$)', content, re.DOTALL)
    if bjt_match:
        bjt_section = bjt_match.group(1)
        
        param_patterns = {
            'Vbe_on_Q1': r'Vbe_on_Q1 = ([0-9.e-]+)',
            'beta_Q1': r'beta_Q1 = ([0-9.e-]+)',
            'Cbc_Q1': r'Cbc_Q1 = ([0-9.e-]+)',
            'ro_Q1': r'ro_Q1 = ([0-9.e-]+[kmMG]?)',
            'rpi_Q1': r'rpi_Q1 = ([0-9.e-]+)',
            'Cbe_Q1': r'Cbe_Q1 = ([0-9.e-]+)',
            'gm_Q1': r'gm_Q1 = ([0-9.e-]+)'
        }
        
        for param_name, pattern in param_patterns.items():
            match = re.search(pattern, bjt_section)
            if match:
                val_str = match.group(1)
                if 'k' in val_str.lower():
                    val = float(val_str.replace('k', '').replace('K', '')) * 1000
                elif 'M' in val_str:
                    val = float(val_str.replace('M', '')) * 1e6
                elif 'G' in val_str:
                    val = float(val_str.replace('G', '')) * 1e9
                else:
                    val = float(val_str)
                parameters[param_name] = val
    
    return parameters

def parse_value(value_str):
    """Parse une valeur avec unité"""
    value_str = value_str.strip().upper()
    value_str = re.sub(r'[FHΩ]$', '', value_str)
    
    multipliers = {
        'P': 1e-12, 'N': 1e-9, 'U': 1e-6, 'M': 1e-3,
        'K': 1e3, 'MEG': 1e6, 'G': 1e9
    }
    
    for mult, factor in multipliers.items():
        if value_str.endswith(mult):
            base_val = float(value_str[:-len(mult)])
            return base_val * factor
    
    return float(value_str)

def parse_transfer_function_expression(tf_expr):
    """Parse une expression de fonction de transfert complexe"""
    
    # Nettoyer l'expression
    tf_expr = tf_expr.strip()
    
    # Chercher le pattern (numérateur)/(dénominateur)
    # L'expression est de la forme: (très long numérateur)/(très long dénominateur)
    
    # Compter les parenthèses pour trouver la séparation
    paren_count = 0
    split_pos = -1
    
    for i, char in enumerate(tf_expr):
        if char == '(':
            paren_count += 1
        elif char == ')':
            paren_count -= 1
            if paren_count == 0:
                # Vérifier si le caractère suivant est '/'
                if i + 1 < len(tf_expr) and tf_expr[i + 1] == '/':
                    split_pos = i + 1
                    break
    
    if split_pos > 0:
        num_expr = tf_expr[1:split_pos-1]  # Enlever les parenthèses extérieures
        den_expr = tf_expr[split_pos+2:-1]  # Enlever '/' et parenthèses
        return num_expr, den_expr
    
    print("Format de fonction de transfert non reconnu")
    return None, None

def create_transfer_function_from_complex_expression(tf_expr, parameters=None, is_numeric=False):
    """Crée une fonction de transfert à partir d'une expression complexe"""
    
    print(f"\n=== Traitement de l'expression ({'numérique' if is_numeric else 'symbolique'}) ===")
    
    # Parser l'expression
    num_expr, den_expr = parse_transfer_function_expression(tf_expr)
    
    if num_expr is None or den_expr is None:
        print("Erreur: Impossible de parser l'expression")
        return None
    
    print(f"Numérateur: {len(num_expr)} caractères")
    print(f"Dénominateur: {len(den_expr)} caractères")
    
    try:
        # Créer le symbole s
        s = sp.Symbol('s')
        
        if is_numeric:
            # Pour la version numérique, les coefficients sont déjà numériques
            print("Conversion de l'expression numérique...")
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
        else:
            # Pour la version symbolique, substituer les paramètres
            print("Substitution des paramètres dans l'expression symbolique...")
            
            if parameters is None:
                parameters = {}
            
            # Créer un dictionnaire de substitution
            subs_dict = {}
            for param, value in parameters.items():
                subs_dict[sp.Symbol(param)] = value
            
            # Ajouter des constantes communes
            subs_dict[sp.Symbol('pi')] = np.pi
            
            # Parser les expressions symboliques
            num_sympy = sp.sympify(num_expr)
            den_sympy = sp.sympify(den_expr)
            
            # Substituer les valeurs
            print("Substitution en cours...")
            num_sympy = num_sympy.subs(subs_dict)
            den_sympy = den_sympy.subs(subs_dict)
        
        # Extraire les coefficients du polynôme
        print("Extraction des coefficients...")
        num_poly = sp.Poly(num_sympy, s)
        den_poly = sp.Poly(den_sympy, s)
        
        # Obtenir les coefficients (du degré le plus élevé au plus bas)
        num_coeffs = [float(c) for c in num_poly.all_coeffs()]
        den_coeffs = [float(c) for c in den_poly.all_coeffs()]
        
        print(f"Numérateur: polynôme de degré {len(num_coeffs)-1}")
        print(f"Dénominateur: polynôme de degré {len(den_coeffs)-1}")
        
        # Créer la fonction de transfert scipy
        tf = signal.TransferFunction(num_coeffs, den_coeffs)
        
        return tf
        
    except Exception as e:
        print(f"Erreur lors du traitement: {e}")
        import traceback
        traceback.print_exc()
        return None

def plot_bode_comparison(tf_symbolic, tf_numeric, filename_suffix=""):
    """Trace les diagrammes de Bode en comparaison"""
    
    # Gamme de fréquences
    frequencies = np.logspace(-1, 9, 10000)  # 0.1 Hz à 1 GHz
    omega = 2 * np.pi * frequencies
    
    # Création du graphique
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    
    # Tracer la fonction symbolique si disponible
    if tf_symbolic is not None:
        try:
            _, h_sym = signal.freqresp(tf_symbolic, omega)
            magnitude_db_sym = 20 * np.log10(np.abs(h_sym))
            phase_deg_sym = np.angle(h_sym) * 180 / np.pi
            
            ax1.semilogx(frequencies, magnitude_db_sym, 'b-', linewidth=2, label='Symbolique (avec substitution)')
            ax2.semilogx(frequencies, phase_deg_sym, 'b-', linewidth=2, label='Symbolique (avec substitution)')
            
            print(f"Gain DC symbolique: {magnitude_db_sym[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé symbolique: {e}")
    
    # Tracer la fonction numérique si disponible
    if tf_numeric is not None:
        try:
            _, h_num = signal.freqresp(tf_numeric, omega)
            magnitude_db_num = 20 * np.log10(np.abs(h_num))
            phase_deg_num = np.angle(h_num) * 180 / np.pi
            
            ax1.semilogx(frequencies, magnitude_db_num, 'r--', linewidth=2, label='Numérique directe')
            ax2.semilogx(frequencies, phase_deg_num, 'r--', linewidth=2, label='Numérique directe')
            
            print(f"Gain DC numérique: {magnitude_db_num[0]:.1f} dB")
        except Exception as e:
            print(f"Erreur lors du tracé numérique: {e}")
    
    # Configuration des graphiques
    ax1.set_xlabel('Fréquence (Hz)')
    ax1.set_ylabel('Magnitude (dB)')
    ax1.set_title('Diagramme de Bode - H1(s) = V7/V1 - Comparaison Symbolique vs Numérique')
    ax1.grid(True, which="both", ls="-", alpha=0.3)
    ax1.set_xlim(0.1, 1e9)
    ax1.legend()
    
    ax2.set_xlabel('Fréquence (Hz)')
    ax2.set_ylabel('Phase (degrés)')
    ax2.grid(True, which="both", ls="-", alpha=0.3)
    ax2.set_xlim(0.1, 1e9)
    ax2.legend()
    
    plt.tight_layout()
    
    # Nom du fichier de sortie
    output_filename = f'bode_H1_V7_V1_comparison_advanced{filename_suffix}.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\nDiagramme de comparaison sauvegardé: {output_filename}")

def main():
    """Fonction principale"""
    
    print("=== Analyse avancée de H1(s) = V7/V1 ===")
    
    filename = "Results_Simulation_20250614_1139.txt"
    
    # Vérifier que le fichier existe
    import os
    if not os.path.exists(filename):
        print(f"Erreur: Le fichier {filename} n'existe pas.")
        return
    
    # Extraire les fonctions de transfert
    h1_symbolic, h1_numeric = extract_h1_functions(filename)
    
    if h1_symbolic is None and h1_numeric is None:
        print("Erreur: Aucune fonction de transfert trouvée")
        return
    
    # Extraire les paramètres
    parameters = extract_parameters(filename)
    
    print(f"\n=== Paramètres extraits ===")
    for param, value in parameters.items():
        print(f"{param} = {value}")
    
    # Créer les fonctions de transfert
    tf_symbolic = None
    tf_numeric = None
    
    # Traitement de la fonction symbolique
    if h1_symbolic:
        tf_symbolic = create_transfer_function_from_complex_expression(
            h1_symbolic, parameters, is_numeric=False)
    
    # Traitement de la fonction numérique
    if h1_numeric:
        tf_numeric = create_transfer_function_from_complex_expression(
            h1_numeric, None, is_numeric=True)
    
    # Tracer les diagrammes de Bode
    if tf_symbolic is not None or tf_numeric is not None:
        print(f"\n=== Génération du diagramme de Bode comparatif ===")
        plot_bode_comparison(tf_symbolic, tf_numeric)
    else:
        print("Erreur: Aucune fonction de transfert n'a pu être créée")

if __name__ == "__main__":
    main()
