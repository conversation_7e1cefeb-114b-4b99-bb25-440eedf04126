--- Simulation Results - 2025-06-14 16:09:52 ---

Netlist originale:
V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n

Temps de calcul parallele: 6.188s

===== DC Analysis (Symbolic) =====

V1 = 1

V2 = 1

I_V1 = 0

I_R1 = 0

I_C1 = 0

===== DC Analysis (Numerical) =====

V1 = 1.0

V2 = 1.0

I_V1 = 0.0

I_R1 = 0.0

I_C1 = 0.0

===== AC Analysis (Symbolic) =====

V1 = 1

V2 = 1/(C1*R1*s+1)

I_V1 = -C1*s/(C1*R1*s+1)

I_R1 = C1*s/(C1*R1*s+1)

I_C1 = C1*s/(C1*R1*s+1)

===== AC Analysis (Numerical) =====

V1 = 1.0

V2 = 1/(0.0001*s+1.0)

I_V1 = -1e-07*s/(0.0001*s+1.0)

I_R1 = 1e-07*s/(0.0001*s+1.0)

I_C1 = 1e-07*s/(0.0001*s+1.0)

===== AC Analysis (Temporal) - f = 1000.0 Hz =====

v1(t) = 1.0*cos(6283.185307179586*t + 0.0)

v2(t) = 0.846733015965*cos(6283.185307179586*t + -0.560982116109)

i_V1(t) = 0.000532018044501*cos(6283.185307179586*t + -2.1317784429)

i_R1(t) = 0.000532018044501*cos(6283.185307179586*t + 1.00981421069)

i_C1(t) = 0.000532018044501*cos(6283.185307179586*t + 1.00981421069)

===== Fonction de Transfert 1 =====

H1(s) = V2/V1 = 1/(C1*R1*s+1)

Fonction symbolique pure (sans substitutions): 1/(C1*R1*s+1)

Fonction de transfert finale: 1/(0.0001*s+1.0)

Substitutions coherentes appliquees

Diagramme de Bode affiche

