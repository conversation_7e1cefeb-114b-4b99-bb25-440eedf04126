#!/usr/bin/env python3
"""
Test simple pour vérifier l'affichage de la fonction de transfert
"""
import subprocess
import sys
import os

def test_simple_circuit():
    """Teste avec un circuit très simple"""
    
    print("=== TEST SIMPLE POUR VÉRIFIER L'AFFICHAGE ===")
    
    # Circuit très simple
    simple_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n"""
    
    print("Circuit simple:")
    print(simple_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{simple_circuit}"""
    
    print("=== Test simple ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "2", 1.0, 1e6, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    with open('run_simple_display_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_simple_display_test.py")
    print("Exécution du test (timeout: 30s)...")
    print()
    
    try:
        result = subprocess.run(['python', 'run_simple_display_test.py'], 
                              capture_output=True, text=True, timeout=30)
        
        output = result.stdout
        error = result.stderr
        
        print("=== RÉSULTATS ===")
        
        if result.returncode == 0:
            print("✅ Test exécuté avec succès")
            
            # Chercher les affichages
            if "H1(s) = V2/V1 =" in output:
                print("✅ H1(s) affiché")
            else:
                print("❌ H1(s) non affiché")
            
            if "Fonction symbolique pure" in output:
                print("✅ Fonction symbolique pure affichée")
            else:
                print("❌ Fonction symbolique pure non affichée")
            
            if "Fonction de transfert finale:" in output:
                print("✅ Fonction de transfert finale affichée")
            else:
                print("❌ Fonction de transfert finale NON AFFICHÉE")
            
            if "Substitutions coherentes appliquees" in output:
                print("✅ Message de cohérence affiché")
            else:
                print("❌ Message de cohérence non affiché")
            
            # Afficher la sortie pour debug
            print("\n=== SORTIE COMPLÈTE ===")
            print(output)
            
            return True
        else:
            print("❌ Erreur lors de l'exécution")
            print("STDOUT:", output)
            print("STDERR:", error)
            return False
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        # Nettoyer
        if os.path.exists('run_simple_display_test.py'):
            os.remove('run_simple_display_test.py')

def main():
    """Fonction principale"""
    
    print("TEST SIMPLE DE L'AFFICHAGE")
    print("=" * 40)
    
    success = test_simple_circuit()
    
    if not success:
        print("\n❌ Le test simple a échoué")
        print("Il y a probablement un problème dans le code")

if __name__ == "__main__":
    main()
