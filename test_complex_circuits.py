#!/usr/bin/env python3
"""
Tests plus complexes pour identifier les problèmes de cohérence
entre expressions symboliques et numériques.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test import *

def test_complex_bjt_circuit():
    """Test avec un circuit BJT plus complexe (amplificateur à émetteur commun)"""
    print("=== Test Circuit BJT Complexe (Amplificateur EC) ===")
    
    netlist_str = """
V1 1 0 DC 0 AC 1
R1 1 2 10k
R2 4 2 47k
R3 4 0 10k
Q1 3 2 5 150 NPN
R4 4 3 2k
R5 5 0 1k
C1 3 6 10u
R6 6 0 10k
"""
    
    return analyze_and_verify_circuit(netlist_str, "BJT Complexe")

def test_complex_opamp_circuit():
    """Test avec un circuit OpAmp plus complexe (amplificateur inverseur avec gain)"""
    print("\n=== Test Circuit OpAmp Complexe (Amplificateur Inverseur) ===")
    
    netlist_str = """
V1 1 0 DC 0 AC 1
R1 1 2 1k
X1 2 0 3 100k 1M 100M
R2 2 3 10k
R3 3 0 1k
"""
    
    return analyze_and_verify_circuit(netlist_str, "OpAmp Complexe")

def test_mixed_circuit():
    """Test avec un circuit mixte BJT + OpAmp"""
    print("\n=== Test Circuit Mixte (BJT + OpAmp) ===")
    
    netlist_str = """
V1 1 0 DC 5 AC 1
R1 1 2 1k
Q1 3 2 0 100 NPN
R2 3 4 2k
X1 0 4 5 100k 1G 100G
R3 5 0 1k
"""
    
    return analyze_and_verify_circuit(netlist_str, "Mixte BJT+OpAmp")

def analyze_and_verify_circuit(netlist_str, circuit_name):
    """Fonction utilitaire pour analyser et vérifier un circuit"""
    
    # Analyser le circuit
    lines = [line.strip() for line in netlist_str.strip().split('\n') if line.strip()]
    original_netlist = [line.split() for line in lines]
    original_nodes = sorted(list(set(comp[i] for comp in original_netlist for i in [1,2]) - {'0'}))
    
    netlist, initial_values, _ = add_bjt_compensation_components(original_netlist, {})
    values = initial_values.copy()
    
    # Ajouter les valeurs des composants
    for comp in netlist:
        comp_type, comp_name = comp[0][0].upper(), comp[0]
        if comp_type in ['R', 'L', 'C']:
            if len(comp) > 3: 
                values[comp_name] = parse_value(comp[3])
        elif comp_type == 'V':
            dc_val_str = ac_val_str = None
            for i_parse in range(3, len(comp), 2):
                if i_parse + 1 < len(comp):
                    param_name, param_val = comp[i_parse].upper(), comp[i_parse+1]
                    if param_name == 'DC': dc_val_str = param_val
                    elif param_name == 'AC': ac_val_str = param_val
            if dc_val_str: values[f"Vdc_{comp_name}"] = parse_value(dc_val_str)
            if ac_val_str: values[f"Vac_{comp_name}"] = parse_value(ac_val_str)
        elif comp_type == 'Q':
            values[f"Vbe_on_{comp_name}"] = "0.7"
            values[f"Vt_{comp_name}"] = "0.025"
            values[f"VA_{comp_name}"] = "100"
            values[f"beta_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100"
            values[f"Cbe_{comp_name}"] = parse_value(comp[6]) if len(comp) > 6 else "0"
            values[f"Cbc_{comp_name}"] = parse_value(comp[7]) if len(comp) > 7 else "0"
        elif comp_type == 'X':
            # Amplificateur opérationnel
            values[f"Av_{comp_name}"] = parse_value(comp[4]) if len(comp) > 4 else "100000"
            fc2_user = parse_value(comp[5]) if len(comp) > 5 else "1000000000"
            fc4_user = parse_value(comp[6]) if len(comp) > 6 else "100000000000"
            
            values[f"Fc2_{comp_name}"] = fc2_user
            values[f"Fc4_{comp_name}"] = fc4_user
            
            fc2_val = float(fc2_user)
            fc4_val = float(fc4_user)
            av_val = float(values[f"Av_{comp_name}"])
            
            if fc2_val < 1e9:
                fc1_calc = fc2_val * 1e-5
                fc4_calc = fc2_val
            else:
                fc1_calc = av_val
                fc4_calc = fc2_val * 2
            
            fc2_calc = fc4_val
            fc3_calc = fc4_val
            
            values[f"Fc1_{comp_name}"] = str(fc1_calc)
            values[f"Fc2_{comp_name}"] = str(fc2_calc)
            values[f"Fc3_{comp_name}"] = str(fc3_calc)
            values[f"Fc4_{comp_name}"] = str(fc4_calc)
    
    all_unique_nodes = sorted(list(set(comp[i] for comp in netlist for i in [1,2]) - {'0'}))
    
    # Analyse DC
    print("Analyse DC...")
    voltages_dc, currents_dc = dc_analysis(netlist, values, all_unique_nodes)
    dc_voltages_num = substitute_values(voltages_dc, values)
    dc_currents_num = substitute_values(currents_dc, values)
    
    # Analyse AC
    print("Analyse AC...")
    voltages_ac, currents_ac, _ = ac_analysis(netlist, values, all_unique_nodes, True)
    ac_voltages_num = substitute_values(voltages_ac, values)
    ac_currents_num = substitute_values(currents_ac, values)
    
    # Vérification de cohérence DC
    print(f"\n=== Vérification de cohérence DC ({circuit_name}) ===")
    dc_inconsistencies = 0
    
    for node in original_nodes:
        if node in voltages_dc and node in dc_voltages_num:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_dc[node], dc_voltages_num[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_DC:")
                print(f"   Symbolique: {voltages_dc[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                dc_inconsistencies += 1
            else:
                print(f"✅ V{node}_DC cohérent")
    
    # Vérifier les courants des composants actifs
    for comp in netlist:
        if comp[0][0].upper() == 'Q':
            for suffix in ['_c', '_b', '_e']:
                key = f"{comp[0]}{suffix}"
                if key in currents_dc and key in dc_currents_num:
                    consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                        currents_dc[key], dc_currents_num[key], values
                    )
                    if not consistent:
                        print(f"❌ INCOHÉRENCE I{key}_DC:")
                        print(f"   Symbolique: {currents_dc[key]}")
                        print(f"   Substitution manuelle: {manual}")
                        print(f"   Substitution automatique: {numeric}")
                        print(f"   Erreur: {error}")
                        dc_inconsistencies += 1
                    else:
                        print(f"✅ I{key}_DC cohérent")
        elif comp[0][0].upper() == 'X':
            key = f"{comp[0]}_out"
            if key in currents_dc and key in dc_currents_num:
                consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                    currents_dc[key], dc_currents_num[key], values
                )
                if not consistent:
                    print(f"❌ INCOHÉRENCE I{key}_DC:")
                    print(f"   Symbolique: {currents_dc[key]}")
                    print(f"   Substitution manuelle: {manual}")
                    print(f"   Substitution automatique: {numeric}")
                    print(f"   Erreur: {error}")
                    dc_inconsistencies += 1
                else:
                    print(f"✅ I{key}_DC cohérent")
    
    print(f"\nRésultat DC: {dc_inconsistencies} incohérences trouvées")
    
    # Vérification de cohérence AC
    print(f"\n=== Vérification de cohérence AC ({circuit_name}) ===")
    ac_inconsistencies = 0
    
    for node in original_nodes:
        if node in voltages_ac and node in ac_voltages_num:
            consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                voltages_ac[node], ac_voltages_num[node], values
            )
            if not consistent:
                print(f"❌ INCOHÉRENCE V{node}_AC:")
                print(f"   Symbolique: {voltages_ac[node]}")
                print(f"   Substitution manuelle: {manual}")
                print(f"   Substitution automatique: {numeric}")
                print(f"   Erreur: {error}")
                ac_inconsistencies += 1
            else:
                print(f"✅ V{node}_AC cohérent")
    
    # Vérifier les courants des composants actifs
    for comp in netlist:
        if comp[0][0].upper() == 'Q':
            for suffix in ['_c', '_b', '_e']:
                key = f"{comp[0]}{suffix}"
                if key in currents_ac and key in ac_currents_num:
                    consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                        currents_ac[key], ac_currents_num[key], values
                    )
                    if not consistent:
                        print(f"❌ INCOHÉRENCE I{key}_AC:")
                        print(f"   Symbolique: {currents_ac[key]}")
                        print(f"   Substitution manuelle: {manual}")
                        print(f"   Substitution automatique: {numeric}")
                        print(f"   Erreur: {error}")
                        ac_inconsistencies += 1
                    else:
                        print(f"✅ I{key}_AC cohérent")
        elif comp[0][0].upper() == 'X':
            key = f"{comp[0]}_out"
            if key in currents_ac and key in ac_currents_num:
                consistent, manual, numeric, error = verify_symbolic_numeric_consistency(
                    currents_ac[key], ac_currents_num[key], values
                )
                if not consistent:
                    print(f"❌ INCOHÉRENCE I{key}_AC:")
                    print(f"   Symbolique: {currents_ac[key]}")
                    print(f"   Substitution manuelle: {manual}")
                    print(f"   Substitution automatique: {numeric}")
                    print(f"   Erreur: {error}")
                    ac_inconsistencies += 1
                else:
                    print(f"✅ I{key}_AC cohérent")
    
    print(f"\nRésultat AC: {ac_inconsistencies} incohérences trouvées")
    
    return dc_inconsistencies == 0 and ac_inconsistencies == 0

if __name__ == "__main__":
    print("Tests complexes de cohérence entre expressions symboliques et numériques")
    print("=" * 80)
    
    bjt_success = test_complex_bjt_circuit()
    opamp_success = test_complex_opamp_circuit()
    mixed_success = test_mixed_circuit()
    
    print("\n" + "=" * 80)
    print("RÉSUMÉ DES TESTS COMPLEXES:")
    print(f"Circuit BJT Complexe: {'✅ SUCCÈS' if bjt_success else '❌ ÉCHEC'}")
    print(f"Circuit OpAmp Complexe: {'✅ SUCCÈS' if opamp_success else '❌ ÉCHEC'}")
    print(f"Circuit Mixte: {'✅ SUCCÈS' if mixed_success else '❌ ÉCHEC'}")
    
    if bjt_success and opamp_success and mixed_success:
        print("\n🎉 Tous les tests complexes sont passés avec succès!")
    else:
        print("\n⚠️  Des incohérences ont été détectées dans les circuits complexes.")
