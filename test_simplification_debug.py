#!/usr/bin/env python3
"""
Test de debug pour la simplification
"""

import sys
import os
import numpy as np
import sympy

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_calculate_bode():
    """Test de la fonction calculate_bode_full_range"""
    from bode_H1_final import calculate_bode_full_range
    
    # Expression simple de test
    expression = "R1/(R1*C1*s + 1)"
    component_values = {'R1': 1000.0, 'C1': 1e-6}
    freq_range = np.array([10, 100, 1000, 10000])
    
    print("Test de calculate_bode_full_range...")
    print(f"Expression: {expression}")
    print(f"Composants: {component_values}")
    
    try:
        mag, phase = calculate_bode_full_range(expression, component_values, freq_range)
        if mag is not None:
            print("✓ Calcul réussi")
            print(f"Magnitude: {mag}")
            print(f"Phase: {phase}")
            return True
        else:
            print("✗ Calcul échoué")
            return False
    except Exception as e:
        print(f"✗ Erreur: {e}")
        return False

def test_sensitivity_analysis():
    """Test de l'analyse de sensibilité"""
    from bode_H1_final import analyze_variable_sensitivity_simple
    
    # Expression simple de test
    expression = "R1*R2/(R1*R2*C1*s + R1 + R2)"
    component_values = {'R1': 1000.0, 'R2': 2000.0, 'C1': 1e-6}
    
    print("\nTest de l'analyse de sensibilité...")
    print(f"Expression: {expression}")
    print(f"Composants: {component_values}")
    
    try:
        results = analyze_variable_sensitivity_simple(expression, component_values)
        if results:
            print("✓ Analyse réussie")
            for result in results:
                print(f"  {result['variable']}: {result['impact_score']:.3f} dB")
            return True
        else:
            print("✗ Aucun résultat")
            return False
    except Exception as e:
        print(f"✗ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simplification():
    """Test de la simplification"""
    from bode_H1_final import simplify_transfer_function_simple, analyze_variable_sensitivity_simple
    
    # Expression simple de test
    expression = "R1*R2*C2*s + R1 + R2 + R3*C1*s"
    component_values = {'R1': 1000.0, 'R2': 2000.0, 'R3': 100.0, 'C1': 1e-9, 'C2': 1e-6}
    
    print("\nTest de la simplification...")
    print(f"Expression: {expression}")
    print(f"Composants: {component_values}")
    
    try:
        # Analyse de sensibilité
        sensitivity_results = analyze_variable_sensitivity_simple(expression, component_values)
        if not sensitivity_results:
            print("✗ Pas de résultats de sensibilité")
            return False
        
        # Simplification
        simplified, removed = simplify_transfer_function_simple(
            expression, component_values, sensitivity_results, max_error_db=5.0
        )
        
        print("✓ Simplification réussie")
        print(f"Expression originale: {expression}")
        print(f"Expression simplifiée: {simplified}")
        print(f"Variables supprimées: {[var[0] for var in removed]}")
        return True
        
    except Exception as e:
        print(f"✗ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test principal"""
    print("=== TEST DE DEBUG DE LA SIMPLIFICATION ===")
    
    # Test 1: Calcul de Bode
    test1 = test_calculate_bode()
    
    # Test 2: Analyse de sensibilité
    test2 = test_sensitivity_analysis()
    
    # Test 3: Simplification complète
    test3 = test_simplification()
    
    print(f"\n=== RÉSULTATS ===")
    print(f"Calcul Bode: {'✓' if test1 else '✗'}")
    print(f"Analyse sensibilité: {'✓' if test2 else '✗'}")
    print(f"Simplification: {'✓' if test3 else '✗'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 Tous les tests sont passés ! La simplification devrait fonctionner.")
    else:
        print("\n⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")

if __name__ == "__main__":
    main()
