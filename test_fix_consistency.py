#!/usr/bin/env python3
"""
Script de test pour vérifier la cohérence des fonctions de transfert
après les corrections appliquées à test.py
"""
import subprocess
import os
import re

def create_test_netlist():
    """Crée une netlist de test simple"""
    return """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""

def run_test_simulation():
    """Exécute une simulation de test avec le circuit modifié"""
    
    print("=== Test de cohérence symbolique/numérique ===")
    print()
    
    # Créer un fichier de test temporaire
    test_netlist = create_test_netlist()
    
    print("Circuit de test:")
    print(test_netlist)
    print()
    
    # Créer un script de test automatisé
    test_script = f'''
import sys
sys.path.append('.')
from test import solve_circuit

# Netlist de test
netlist_str = """{test_netlist}"""

# Paramètres de test
frequency_hz = 1.0
do_transfer_function = True
input_node = "1"
output_node = "7"
freq_min = 1.0
freq_max = 1e12

print("Exécution de la simulation de test...")
solve_circuit(netlist_str, frequency_hz, do_transfer_function, input_node, output_node, freq_min, freq_max, laplace_domain=True)
'''
    
    # Sauvegarder le script de test
    with open('run_test_simulation.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("Script de test créé: run_test_simulation.py")
    print("Exécution du test...")
    print()
    
    try:
        # Exécuter le test
        result = subprocess.run(['python', 'run_test_simulation.py'], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✓ Test exécuté avec succès")
            
            # Analyser les résultats
            analyze_test_results()
            
        else:
            print("✗ Erreur lors de l'exécution du test")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            
    except subprocess.TimeoutExpired:
        print("✗ Test interrompu (timeout)")
    except Exception as e:
        print(f"✗ Erreur: {e}")
    
    # Nettoyer
    if os.path.exists('run_test_simulation.py'):
        os.remove('run_test_simulation.py')

def analyze_test_results():
    """Analyse les résultats du test dans le fichier de simulation le plus récent"""
    
    print("=== Analyse des résultats ===")
    
    # Trouver le fichier de résultats le plus récent
    result_files = [f for f in os.listdir('.') if f.startswith('Results_Simulation_') and f.endswith('.txt')]
    
    if not result_files:
        print("✗ Aucun fichier de résultats trouvé")
        return
    
    # Prendre le plus récent
    latest_file = max(result_files, key=lambda f: os.path.getctime(f))
    print(f"Analyse du fichier: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Chercher les fonctions de transfert
        symbolic_match = re.search(r'Fonction de transfert symbolique:\s*(.+?)(?=Fonction de transfert numérique|$)', content, re.DOTALL)
        numeric_match = re.search(r'Fonction de transfert numérique:\s*(.+?)(?=\n\n|✓|⚠|$)', content, re.DOTALL)
        
        # Chercher les messages de cohérence
        consistency_match = re.search(r'(✓ Cohérence symbolique/numérique vérifiée|⚠ Différence détectée:.+?)(?=\n|$)', content)
        
        print()
        if symbolic_match:
            symbolic_expr = symbolic_match.group(1).strip()
            print(f"✓ Fonction symbolique trouvée ({len(symbolic_expr)} caractères)")
        else:
            print("✗ Fonction symbolique non trouvée")
        
        if numeric_match:
            numeric_expr = numeric_match.group(1).strip()
            print(f"✓ Fonction numérique trouvée ({len(numeric_expr)} caractères)")
        else:
            print("✗ Fonction numérique non trouvée")
        
        if consistency_match:
            consistency_msg = consistency_match.group(1).strip()
            print(f"✓ Message de cohérence: {consistency_msg}")
            
            if "Cohérence symbolique/numérique vérifiée" in consistency_msg:
                print("🎉 SUCCÈS: Les fonctions sont cohérentes!")
            else:
                print("⚠ ATTENTION: Différences détectées")
        else:
            print("✗ Aucun message de cohérence trouvé")
        
        # Analyser les ordres de grandeur
        if symbolic_match and numeric_match:
            analyze_expressions_similarity(symbolic_expr, numeric_expr)
            
    except Exception as e:
        print(f"✗ Erreur lors de l'analyse: {e}")

def analyze_expressions_similarity(symbolic_expr, numeric_expr):
    """Analyse la similarité entre les expressions symbolique et numérique"""
    
    print()
    print("=== Analyse de similarité ===")
    
    # Compter les termes en s^
    symbolic_s_terms = len(re.findall(r's\^\d+', symbolic_expr))
    numeric_s_terms = len(re.findall(r's\^\d+', numeric_expr))
    
    print(f"Termes en s^ - Symbolique: {symbolic_s_terms}, Numérique: {numeric_s_terms}")
    
    # Extraire les coefficients numériques
    symbolic_numbers = re.findall(r'(\d+(?:\.\d+)?(?:e[+-]?\d+)?)', symbolic_expr)
    numeric_numbers = re.findall(r'(\d+(?:\.\d+)?(?:e[+-]?\d+)?)', numeric_expr)
    
    if symbolic_numbers and numeric_numbers:
        # Comparer les premiers coefficients
        try:
            sym_first = float(symbolic_numbers[0])
            num_first = float(numeric_numbers[0])
            
            ratio = sym_first / num_first if num_first != 0 else float('inf')
            
            print(f"Premier coefficient - Symbolique: {sym_first:.2e}, Numérique: {num_first:.2e}")
            print(f"Ratio: {ratio:.2e}")
            
            if 0.9 <= ratio <= 1.1:
                print("✓ Coefficients très similaires")
            elif 0.1 <= ratio <= 10:
                print("⚠ Coefficients modérément différents")
            else:
                print("✗ Coefficients très différents")
                
        except (ValueError, IndexError):
            print("✗ Impossible de comparer les coefficients")
    
    # Longueurs des expressions
    print(f"Longueur - Symbolique: {len(symbolic_expr)}, Numérique: {len(numeric_expr)}")
    
    length_ratio = len(symbolic_expr) / len(numeric_expr) if len(numeric_expr) > 0 else float('inf')
    print(f"Ratio de longueur: {length_ratio:.2f}")

def main():
    """Fonction principale"""
    
    print("Test de cohérence des corrections appliquées à test.py")
    print("=" * 60)
    print()
    
    # Vérifier que test.py existe
    if not os.path.exists('test.py'):
        print("✗ Fichier test.py non trouvé")
        return
    
    # Vérifier que la sauvegarde existe
    if os.path.exists('test.py.backup'):
        print("✓ Sauvegarde test.py.backup trouvée")
    else:
        print("⚠ Aucune sauvegarde trouvée")
    
    print()
    
    # Exécuter le test
    run_test_simulation()

if __name__ == "__main__":
    main()
