# Fonctionnalité Train - Merge Intelligent d'Expressions

## Vue d'ensemble

La fonctionnalité **Train** utilise un **merge intelligent** pour optimiser les expressions de transfert simplifiées. Au lieu de remplacer complètement l'expression, elle combine intelligemment l'expression globale (bonne partout) avec une optimisation locale (bonne à la position actuelle du slider) pour créer une expression hybride performante sur toute la plage.

## Principe du Merge Intelligent

### Problème Résolu
- **Expression simplifiée initiale** : Bonne globalement mais peut diverger localement
- **Optimisation locale** : Excellente à une position mais mauvaise ailleurs
- **Solution** : Merge pondéré qui conserve les avantages des deux approches

### Stratégie
1. **Conserver l'expression globale** comme base de performance générale
2. **Créer une optimisation locale** pour la position actuelle du slider
3. **Merger intelligemment** avec pondération gaussienne centrée sur le point de focus
4. **Valider globalement** sur toute la plage de la variable

## Fonctionnalités

### 1. Boutons Train par Variable

- **Emplacement** : À droite de chaque slider de variable
- **Apparence** : Bouton orange avec le texte "Train"
- **État initial** : Désactivé jusqu'à ce qu'une expression simplifiée soit disponible
- **Compteur d'utilisation** : Affiche le nombre d'utilisations (ex: "Train (2)")

### 2. Processus de Training Intelligent

Quand un bouton Train est cliqué :

1. **Détection du point de divergence** : Évalue l'erreur à la position actuelle du slider
2. **Vérification de nécessité** : Skip si l'erreur est déjà acceptable (< 3 dB)
3. **Optimisation locale** : Crée une expression optimisée pour ±20% autour de la position
4. **Merge intelligent** : Combine avec pondération gaussienne les expressions globale et locale
5. **Validation globale** : Teste l'amélioration sur toute la plage de la variable
6. **Application conditionnelle** : Ne remplace que si amélioration significative (>10% global, >40% des points)
7. **Sauvegarde** : Enregistrement avec métadonnées complètes

### 3. Métriques de Validation

- **Amélioration globale** : Pourcentage d'amélioration de l'erreur moyenne sur toute la plage
- **Points améliorés** : Nombre/pourcentage de points de test qui montrent une amélioration
- **Réduction erreur max** : Diminution de l'erreur maximale en dB
- **Critères d'acceptation** : Minimum 10% d'amélioration globale ET 40% des points améliorés

### 4. Sauvegarde Intelligente

Chaque training génère un fichier avec métadonnées complètes :
- **Format** : `FTC_merged_{variable}_{compteur}.txt`
- **Contenu** : Expression mergée avec validation détaillée
- **Métadonnées** :
  - Position de focus (où l'utilisateur a cliqué Train)
  - Résultats de validation (amélioration, points améliorés)
  - Méthode utilisée (merge intelligent)
  - Erreurs avant/après

Exemple :
```
FTC_merged_R1_1.txt  # Premier merge pour R1
FTC_merged_C2_3.txt  # Troisième merge pour C2
```

### 5. Mise à Jour Dynamique

- **Affichage Bode** : Mise à jour automatique avec l'expression optimisée
- **Courbes** : Remplacement de la courbe simplifiée par la version entraînée
- **Sliders** : Réponse dynamique aux changements de valeurs

## Utilisation

### Étapes pour utiliser le Training

1. **Lancer l'analyse** : Ouvrir l'analyseur de Bode
2. **Analyse de sensibilité** : Cliquer sur "Analyse de sensibilité"
3. **Simplification** : Simplifier la fonction de transfert
4. **Affichage Bode** : Afficher le Bode de l'expression simplifiée
5. **Training** : Les boutons "Train" sont maintenant activés
6. **Optimisation** : Cliquer sur "Train" pour la variable à optimiser

### Workflow Recommandé

1. **Identifier les divergences** : Bouger les sliders pour trouver où l'expression simplifiée diverge le plus
2. **Positionner le slider** : Placer le slider de la variable problématique à l'endroit de divergence maximale
3. **Cliquer Train** : Lancer le merge intelligent pour cette position
4. **Vérifier globalement** : Tester que l'amélioration fonctionne sur toute la plage
5. **Répéter si nécessaire** : Traiter d'autres variables ou d'autres positions

### Avantages du Merge Intelligent

- **Préservation globale** : L'expression reste bonne partout où elle l'était déjà
- **Amélioration locale** : Correction ciblée des zones problématiques
- **Stabilité** : Pas de dégradation dans les zones non problématiques
- **Accumulation** : Chaque training améliore progressivement l'expression

## Implémentation Technique

### Classes Modifiées

#### `BodeAnalyzer`
- **Nouveaux attributs** :
  - `trained_expressions` : Dictionnaire des expressions entraînées
  - `train_buttons` : Dictionnaire des boutons Train
  - `train_counters` : Compteurs d'utilisation

- **Nouvelles méthodes** :
  - `train_variable()` : Méthode principale de training
  - `create_trained_expression()` : Création de l'expression optimisée
  - `save_trained_expression()` : Sauvegarde automatique
  - `enable_train_buttons()` : Activation des boutons

### Algorithme d'Optimisation

1. **Échantillonnage** : Génération de points de test (linéaire ou logarithmique)
2. **Évaluation** : Calcul des réponses pour chaque point
3. **Analyse d'erreur** : Mesure des différences en dB
4. **Correction** : Ajout de termes correctifs à l'expression
5. **Validation** : Vérification de l'amélioration

### Gestion des Erreurs

- **Expressions invalides** : Gestion des erreurs de calcul
- **Variables absentes** : Ajout de termes correctifs
- **Plages extrêmes** : Adaptation logarithmique/linéaire
- **Sauvegarde** : Gestion des erreurs d'écriture

## Exemples d'Utilisation

### Cas d'Usage Typique

```
Variable R1 : Impact élevé détecté
→ Clic sur "Train" pour R1
→ Test de 20 valeurs de 100Ω à 100kΩ
→ Erreur max trouvée : 8.5 dB
→ Expression optimisée créée
→ Sauvegarde dans FTC_trained_R1_1.txt
→ Affichage mis à jour
```

### Résultats Attendus

- **Réduction d'erreur** : Diminution des écarts entre original et simplifié
- **Meilleure représentation** : Comportement plus fidèle sur la plage de la variable
- **Conservation de simplicité** : Expression reste relativement simple
- **Traçabilité** : Historique complet des optimisations

## Limitations

- **Méthode simple** : Algorithme d'optimisation basique dans cette version
- **Une variable à la fois** : Pas d'optimisation multi-variables simultanée
- **Termes correctifs** : Ajouts modérés pour préserver la simplicité
- **Validation manuelle** : Vérification visuelle recommandée

## Évolutions Futures

- **Optimisation multi-variables** : Training simultané de plusieurs variables
- **Algorithmes avancés** : Méthodes d'optimisation plus sophistiquées
- **Validation automatique** : Critères d'acceptation automatiques
- **Interface améliorée** : Visualisation des améliorations
