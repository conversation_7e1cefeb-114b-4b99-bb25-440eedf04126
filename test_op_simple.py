#!/usr/bin/env python3

# Test simple de l'amplificateur opérationnel avec réponse fréquentielle

import sys
import os

# Ajouter le répertoire courant au path pour importer test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test simple avec netlist directe
print("=== Test simple de l'amplificateur opérationnel ===")
print("Configuration: Amplificateur inverseur")
print("Gain théorique = -R2/R1 = -2k/1k = -2")
print("fc1 = 500MHz (par défaut), fc2 = 10GHz (par défaut)")
print()

# Netlist simple avec amplificateur inverseur
netlist_simple = """V1 1 0 DC 1 AC 1
R1 1 2 1k
OP1 2 0 3
R2 2 3 2k"""

print("Netlist:")
print(netlist_simple)
print()

# Importer et tester
try:
    from test import solve_circuit
    
    print("Test à 1kHz (bien en dessous de fc1=500MHz)...")
    solve_circuit(netlist_simple, 
                 frequency_hz=1000.0,
                 do_transfer_function=False,
                 laplace_domain=True)
    
    print("\n" + "="*50)
    print("✓ Test terminé!")
    print()
    print("Vérifications attendues :")
    print("1. Le gain devrait être proche de -2 à 1kHz")
    print("2. fc1_OP1 = 500000000 (500MHz)")
    print("3. fc2_OP1 = 10000000000 (10GHz)")
    print("4. La fonction de transfert contient ces paramètres")
    
except Exception as e:
    print(f"✗ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
