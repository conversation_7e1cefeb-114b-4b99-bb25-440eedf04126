#!/usr/bin/env python3
"""
Script de test pour vérifier la fonctionnalité de sélection de courbe pour le curseur
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from bode_H1_final import *
    print("✓ Import du module bode_H1_final réussi")
except ImportError as e:
    print(f"✗ Erreur d'import: {e}")
    sys.exit(1)

def test_basic_functionality():
    """Test basique de la fonctionnalité"""
    print("\n=== Test de base ===")
    
    # Créer une application Qt
    app = QApplication(sys.argv)
    
    # Créer une expression de test simple
    transfer_function = "1/(1+s*R1*C1)"
    variables = ['R1', 'C1']
    values = {'R1': 1000.0, 'C1': 1e-6}
    frequency_params = {'f_min': 1.0, 'f_max': 1e6, 'num_points': 100}
    slider_ranges = {'R1': (100.0, 10000.0), 'C1': (1e-9, 1e-3)}
    
    try:
        # Créer l'analyseur de Bode
        analyzer = BodeAnalyzer(transfer_function, variables, values, frequency_params, slider_ranges)
        print("✓ Création de BodeAnalyzer réussie")
        
        # Vérifier que les boutons radio existent
        assert hasattr(analyzer, 'cursor_original_btn'), "Bouton original manquant"
        assert hasattr(analyzer, 'cursor_simplified_btn'), "Bouton simplifié manquant"
        assert hasattr(analyzer, 'cursor_manual_btn'), "Bouton manuel manquant"
        assert hasattr(analyzer, 'cursor_button_group'), "Groupe de boutons manquant"
        print("✓ Tous les boutons radio sont présents")
        
        # Vérifier l'état initial
        assert analyzer.cursor_original_btn.isChecked(), "Bouton original devrait être sélectionné par défaut"
        assert not analyzer.cursor_simplified_btn.isEnabled(), "Bouton simplifié devrait être désactivé au début"
        assert not analyzer.cursor_manual_btn.isEnabled(), "Bouton manuel devrait être désactivé au début"
        assert analyzer.cursor_tracking_mode == "original", "Mode de suivi devrait être 'original' par défaut"
        print("✓ État initial correct")
        
        # Tester le changement de mode
        analyzer.set_cursor_tracking_mode("simplified")
        assert analyzer.cursor_tracking_mode == "simplified", "Mode de suivi devrait être 'simplified'"
        print("✓ Changement de mode fonctionne")
        
        # Tester la méthode get_interpolated_values
        mag, phase = analyzer.get_interpolated_values(3.0)  # 10^3 = 1000 Hz
        assert isinstance(mag, (int, float)), "Magnitude devrait être un nombre"
        assert isinstance(phase, (int, float)), "Phase devrait être un nombre"
        print("✓ get_interpolated_values fonctionne")
        
        print("✓ Tous les tests de base réussis!")
        
        # Fermer l'application
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        app.quit()
        return False

def test_cursor_functionality():
    """Test de la fonctionnalité du curseur avec différentes courbes"""
    print("\n=== Test de fonctionnalité du curseur ===")
    
    app = QApplication(sys.argv)
    
    transfer_function = "1/(1+s*R1*C1)"
    variables = ['R1', 'C1']
    values = {'R1': 1000.0, 'C1': 1e-6}
    frequency_params = {'f_min': 1.0, 'f_max': 1e6, 'num_points': 100}
    slider_ranges = {'R1': (100.0, 10000.0), 'C1': (1e-9, 1e-3)}
    
    try:
        analyzer = BodeAnalyzer(transfer_function, variables, values, frequency_params, slider_ranges)
        
        # Test avec expression simplifiée
        simplified_expr = "1/(1+s*1000*1e-6)"  # Expression simplifiée
        analyzer.add_simplified_curves(simplified_expr, values)
        
        # Vérifier que le bouton simplifié est maintenant activé
        assert analyzer.cursor_simplified_btn.isEnabled(), "Bouton simplifié devrait être activé après ajout de courbe"
        print("✓ Bouton simplifié activé après ajout de courbe")
        
        # Tester le suivi de la courbe simplifiée
        analyzer.cursor_simplified_btn.setChecked(True)
        assert analyzer.cursor_tracking_mode == "simplified", "Mode devrait être 'simplified'"
        
        # Tester get_interpolated_values avec courbe simplifiée
        mag_simplified, phase_simplified = analyzer.get_interpolated_values(3.0)
        assert isinstance(mag_simplified, (int, float)), "Magnitude simplifiée devrait être un nombre"
        print("✓ Suivi de courbe simplifiée fonctionne")
        
        # Test avec expression manuelle
        manual_expr = "0.8/(1+s*1200*1e-6)"  # Expression manuelle différente
        analyzer.add_manual_curves(manual_expr, values)
        
        # Vérifier que le bouton manuel est maintenant activé
        assert analyzer.cursor_manual_btn.isEnabled(), "Bouton manuel devrait être activé après ajout de courbe"
        print("✓ Bouton manuel activé après ajout de courbe")
        
        # Tester le suivi de la courbe manuelle
        analyzer.cursor_manual_btn.setChecked(True)
        assert analyzer.cursor_tracking_mode == "manual", "Mode devrait être 'manual'"
        
        # Tester get_interpolated_values avec courbe manuelle
        mag_manual, phase_manual = analyzer.get_interpolated_values(3.0)
        assert isinstance(mag_manual, (int, float)), "Magnitude manuelle devrait être un nombre"
        print("✓ Suivi de courbe manuelle fonctionne")
        
        # Test de suppression de toutes les courbes
        analyzer.clear_all_additional_curves()
        
        # Vérifier que les boutons sont désactivés et le mode revient à original
        assert not analyzer.cursor_simplified_btn.isEnabled(), "Bouton simplifié devrait être désactivé"
        assert not analyzer.cursor_manual_btn.isEnabled(), "Bouton manuel devrait être désactivé"
        assert analyzer.cursor_original_btn.isChecked(), "Bouton original devrait être sélectionné"
        assert analyzer.cursor_tracking_mode == "original", "Mode devrait revenir à 'original'"
        print("✓ Suppression des courbes remet l'état initial")
        
        print("✓ Tous les tests de fonctionnalité du curseur réussis!")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test de curseur: {e}")
        import traceback
        traceback.print_exc()
        app.quit()
        return False

if __name__ == "__main__":
    print("=== Test de la fonctionnalité de sélection de courbe pour le curseur ===")
    
    success = True
    
    # Test de base
    if not test_basic_functionality():
        success = False
    
    # Test de fonctionnalité du curseur
    if not test_cursor_functionality():
        success = False
    
    if success:
        print("\n🎉 Tous les tests sont réussis! La fonctionnalité de sélection de courbe pour le curseur fonctionne correctement.")
    else:
        print("\n❌ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        sys.exit(1)
