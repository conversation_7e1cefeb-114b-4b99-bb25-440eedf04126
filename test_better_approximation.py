#!/usr/bin/env python3
"""
Test d'une méthode d'approximation plus intelligente
qui préserve mieux la structure de la fonction originale
"""

import numpy as np
import sympy
import time

def read_transfer_function(filename):
    """Lit la fonction de transfert depuis un fichier"""
    try:
        with open(filename, 'r') as f:
            content = f.read().strip()
        return content.replace('^', '**')
    except FileNotFoundError:
        print(f"Fichier {filename} non trouvé")
        return None

def extract_variables_from_expression(expr_str):
    """Extrait les variables d'une expression"""
    import re
    variables = set()
    pattern = r'\b([A-Za-z][A-Za-z0-9_]*)\b'
    matches = re.findall(pattern, expr_str)
    
    for match in matches:
        if match not in ['s', 'j', 'pi', 'e', 'I', 'E', 'sin', 'cos', 'tan', 'log', 'exp']:
            variables.add(match)
    
    return sorted(list(variables))

def count_terms_in_expression(expr_str):
    """Compte le nombre de termes dans une expression"""
    try:
        expr = sympy.sympify(expr_str)
        if expr.is_rational_function():
            num, den = sympy.fraction(expr)
            num_terms = len(sympy.Add.make_args(sympy.expand(num))) if sympy.expand(num).is_Add else 1
            den_terms = len(sympy.Add.make_args(sympy.expand(den))) if sympy.expand(den).is_Add else 1
            return num_terms + den_terms
        else:
            expanded = sympy.expand(expr)
            return len(sympy.Add.make_args(expanded)) if expanded.is_Add else 1
    except:
        return expr_str.count('+') + expr_str.count('-') + 1

def smart_approximation_method(original_expr, variables, target_reduction=0.6):
    """Méthode d'approximation intelligente qui préserve la structure"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s

        expr = sympy.sympify(original_expr, locals=symbols_dict)

        if not expr.is_rational_function():
            return str(expr)

        num, den = sympy.fraction(expr)

        # Analyser la structure originale
        print(f"Numérateur: {len(sympy.Add.make_args(sympy.expand(num)) if sympy.expand(num).is_Add else [num])} termes")
        print(f"Dénominateur: {len(sympy.Add.make_args(sympy.expand(den)) if sympy.expand(den).is_Add else [den])} termes")

        # Nouvelle stratégie: garder le numérateur complet et simplifier seulement le dénominateur
        # car le numérateur a moins de termes et est plus important pour la forme de la réponse

        num_expanded = sympy.expand(num)
        den_expanded = sympy.expand(den)

        num_terms = sympy.Add.make_args(num_expanded) if num_expanded.is_Add else [num_expanded]
        den_terms = sympy.Add.make_args(den_expanded) if den_expanded.is_Add else [den_expanded]

        # Calculer la réduction cible pour le dénominateur seulement
        total_terms = len(num_terms) + len(den_terms)
        target_total = max(len(num_terms) + 2, int(total_terms * (1 - target_reduction)))
        target_den = target_total - len(num_terms)

        print(f"Stratégie: garder tous les {len(num_terms)} termes du numérateur")
        print(f"Réduire le dénominateur de {len(den_terms)} à {target_den} termes")

        # Sélectionner les termes les plus importants du dénominateur
        def select_important_den_terms(terms_list, max_terms):
            if len(terms_list) <= max_terms:
                return terms_list

            # Analyser chaque terme du dénominateur
            term_analysis = []
            for term in terms_list:
                # Calculer le degré en s
                s_degree = sympy.degree(term, s) if s in term.free_symbols else 0

                # Compter le nombre de variables
                var_count = len([v for v in variables if v in str(term)])

                # Score d'importance pour le dénominateur
                # Privilégier: termes constants (s_degree=0), puis degré 1, puis degré 2, etc.
                # et termes avec moins de variables
                if s_degree == 0:
                    importance_score = 0 + var_count * 0.1  # Termes constants très importants
                elif s_degree == 1:
                    importance_score = 10 + var_count * 0.1  # Termes en s importants
                elif s_degree == 2:
                    importance_score = 20 + var_count * 0.1  # Termes en s^2 moyennement importants
                else:
                    importance_score = 30 + s_degree * 10 + var_count * 0.1  # Termes de haut degré moins importants

                term_analysis.append((importance_score, term))

            # Trier par importance (score croissant)
            term_analysis.sort(key=lambda x: x[0])

            # Sélectionner les termes les plus importants
            selected = [term for _, term in term_analysis[:max_terms]]

            print(f"Termes dénominateur sélectionnés: {[str(t) for t in selected[:5]]}{'...' if len(selected) > 5 else ''}")
            return selected

        # Garder le numérateur complet
        new_num = num

        # Sélectionner les termes importants du dénominateur
        selected_den_terms = select_important_den_terms(den_terms, target_den)
        new_den = sum(selected_den_terms) if selected_den_terms else 1

        result = new_num / new_den

        print(f"Approximation générée")

        return str(result)

    except Exception as e:
        print(f"Erreur approximation intelligente: {e}")
        import traceback
        traceback.print_exc()
        return str(expr)

def evaluate_transfer_function(expr_str, variables, values, frequencies):
    """Évalue la fonction de transfert pour un set de valeurs"""
    try:
        s = sympy.Symbol('s')
        symbols_dict = {var: sympy.Symbol(var) for var in variables}
        symbols_dict['s'] = s
        
        expr = sympy.sympify(expr_str, locals=symbols_dict)
        
        # Créer une fonction rapide
        func_args = [s] + [symbols_dict[var] for var in variables]
        fast_func = sympy.lambdify(func_args, expr, 'numpy')
        
        responses = []
        for freq in frequencies:
            s_val = 2j * np.pi * freq
            param_values = [values[var] for var in variables]
            
            try:
                response = fast_func(s_val, *param_values)
                responses.append(complex(response))
            except:
                responses.append(0.0 + 0.0j)
        
        return np.array(responses)
        
    except Exception as e:
        print(f"Erreur évaluation: {e}")
        return np.zeros(len(frequencies), dtype=complex)

def calculate_db_error(original_responses, approx_responses):
    """Calcule l'erreur en dB entre deux réponses"""
    original_mag = np.abs(original_responses)
    approx_mag = np.abs(approx_responses)
    
    # Éviter les divisions par zéro
    original_mag = np.where(original_mag > 1e-15, original_mag, 1e-15)
    approx_mag = np.where(approx_mag > 1e-15, approx_mag, 1e-15)
    
    # Calculer en dB
    original_db = 20 * np.log10(original_mag)
    approx_db = 20 * np.log10(approx_mag)
    
    # Erreur RMS en dB
    error_db = np.sqrt(np.mean((original_db - approx_db)**2))
    
    # Erreur maximale en dB
    max_error_db = np.max(np.abs(original_db - approx_db))
    
    return error_db, max_error_db

def main():
    print("=== Test de la méthode d'approximation intelligente ===")
    
    # Lire la fonction de transfert
    transfer_function = read_transfer_function("FTC.txt")
    if not transfer_function:
        return
    
    print(f"Fonction de transfert originale:")
    print(transfer_function)
    print()
    
    # Extraire les variables
    variables = extract_variables_from_expression(transfer_function)
    print(f"Variables: {variables}")
    
    # Compter les termes originaux
    original_terms = count_terms_in_expression(transfer_function)
    print(f"Termes originaux: {original_terms}")
    print()
    
    # Tester différents niveaux de réduction
    for reduction in [0.3, 0.5, 0.7]:
        print(f"=== Test avec {reduction*100:.0f}% de réduction ===")
        
        # Générer l'approximation
        start_time = time.time()
        approximation = smart_approximation_method(transfer_function, variables, reduction)
        approx_time = time.time() - start_time
        
        approx_terms = count_terms_in_expression(approximation)
        actual_reduction = (original_terms - approx_terms) / original_terms * 100
        
        print(f"Temps: {approx_time:.3f}s")
        print(f"Termes approximés: {approx_terms} (réduction: {actual_reduction:.1f}%)")
        print(f"Expression approximée: {approximation}")
        print()
        
        # Tester avec quelques sets de valeurs
        test_values = {
            'C1': 1e-7, 'C2': 1e-5, 'C3': 1e-7,
            'L1': 1e-3,
            'R1': 100, 'R2': 10000, 'R3': 10000, 'R4': 1000, 'R5': 1000
        }
        
        frequencies = np.logspace(0, 6, 50)  # 1 Hz à 1 MHz
        
        # Évaluer l'original et l'approximation
        original_responses = evaluate_transfer_function(transfer_function, variables, test_values, frequencies)
        approx_responses = evaluate_transfer_function(approximation, variables, test_values, frequencies)
        
        # Calculer l'erreur
        error_rms, error_max = calculate_db_error(original_responses, approx_responses)
        
        print(f"Erreur RMS: {error_rms:.2f} dB")
        print(f"Erreur Max: {error_max:.2f} dB")
        print("-" * 60)
        print()

if __name__ == "__main__":
    main()
