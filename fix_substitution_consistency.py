#!/usr/bin/env python3
"""
Correction du problème de cohérence des substitutions
"""

def fix_plot_bode_substitutions():
    """Corrige la fonction plot_bode pour appliquer les mêmes substitutions aux deux expressions"""
    
    print("=== CORRECTION DES SUBSTITUTIONS DANS plot_bode ===")
    
    # <PERSON><PERSON> le fichier
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Nouvelle fonction plot_bode avec substitutions cohérentes
    new_plot_bode = '''def plot_bode(transfer_function, values, freq_range, **_):
    tf_symbolic = transfer_function
    tf_numeric = transfer_function
    
    # Fonction de substitution cohérente pour les deux expressions
    def apply_consistent_substitution(expression, values):
        """Applique exactement les mêmes substitutions pour symbolique et numérique"""
        
        result = expression
        
        # 1. Appliquer force_fc_substitution
        result = force_fc_substitution(result, values)
        
        # 2. Variables prioritaires
        priority_vars = [k for k in values.keys() if k.startswith('ro_') or k.startswith('beta_') or k.startswith('Ic_ac_') or k.startswith('Av_') or k.startswith('Fc1_') or k.startswith('Fc2_') or k.startswith('Fc3_') or k.startswith('Fc4_')]
        
        for var in priority_vars:
            comp_value = values.get(var, '1000')
            try:
                comp_value_num = xcas(f"evalf({comp_value})")
                float(comp_value_num)
                result = xcas(f"subst({result},{var},{comp_value_num})")
            except:
                if var.startswith('ro_'): default_val = '1000'
                elif var.startswith('beta_'): default_val = '100'
                elif var.startswith('Ic_ac_'): default_val = '0.001'
                elif var.startswith('Av_'): default_val = '100000'
                elif var.startswith('Fc1_'): default_val = '100000000'
                elif var.startswith('Fc2_'): default_val = '1000000000'
                elif var.startswith('Fc3_'): default_val = '10000000000'
                elif var.startswith('Fc4_'): default_val = '100000000000'
                else: default_val = '1000'
                result = xcas(f"subst({result},{var},{default_val})")
        
        # 3. Identifier les résistances de compensation BJT
        bjt_compensation_resistors = set()
        for var, val in values.items():
            if var.startswith('comp_BJT_res_') and val == 'true':
                resistor_name = var.replace('comp_BJT_res_', '')
                bjt_compensation_resistors.add(resistor_name)
        
        # 4. Substitution des autres composants
        for var, val in values.items():
            if var not in priority_vars and var != 'k' and not var.startswith('comp_BJT_'):
                if var in bjt_compensation_resistors or not var.startswith('comp_BJT_'):
                    try:
                        comp_value_num = xcas(f"evalf({val})")
                        float(comp_value_num)
                        if var in str(result):
                            result = xcas(f"subst({result},{var},{comp_value_num})")
                    except: 
                        continue
        
        # 5. Substitutions finales
        result = xcas(f"subst({result},k,1000)")
        result = xcas(f"subst({result},pi,3.14159265359)")
        result = xcas(f"evalf(simplify({result}))")
        
        return result
    
    # Appliquer exactement les mêmes substitutions aux deux expressions
    tf_symbolic_final = apply_consistent_substitution(tf_symbolic, values)
    tf_numeric_final = apply_consistent_substitution(tf_numeric, values)
    
    # Afficher les résultats
    file_print(f"Fonction de transfert symbolique: {tf_symbolic_final}")
    file_print(f"Fonction de transfert numerique: {tf_numeric_final}")
    
    # Vérification de cohérence
    try:
        consistency_check = verify_symbolic_numeric_consistency(tf_symbolic_final, tf_numeric_final, values)
        if consistency_check[0]:
            file_print("OK Coherence symbolique/numerique verifiee")
        else:
            file_print(f"ATTENTION Difference detectee: {consistency_check[3]}")
            file_print(f"  Symbolique evalue: {consistency_check[1]}")
            file_print(f"  Numerique: {consistency_check[2]}")
    except Exception as e:
        file_print(f"Erreur lors de la verification de coherence: {e}")
    
    # Tracer le diagramme de Bode avec la fonction numérique
    try:
        num_coeffs, den_coeffs = parse_transfer_function(tf_numeric_final)
        if all(c == 0 for c in num_coeffs) or all(c == 0 for c in den_coeffs):
            file_print("Erreur: Coefficients nuls")
            return
        system = signal.TransferFunction(num_coeffs, den_coeffs)
        freqs = np.logspace(np.log10(freq_range[0]), np.log10(freq_range[1]), 1000)
        w = 2 * np.pi * freqs
        w, h = signal.freqresp(system, w)
        freqs = w / (2 * np.pi)
        magnitude_db = 20 * np.log10(np.abs(h))
        phase_deg = np.angle(h) * 180 / np.pi
        _, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        ax1.semilogx(freqs, magnitude_db)
        ax1.set_ylabel('Magnitude (dB)')
        ax1.set_xlabel('Frequence (Hz)')
        ax1.grid(True, which="both", ls="-", alpha=0.3)
        ax1.set_title('Diagramme de Bode')
        ax2.semilogx(freqs, phase_deg)
        ax2.set_xlabel('Frequence (Hz)')
        ax2.set_ylabel('Phase (degres)')
        ax2.grid(True, which="both", ls="-", alpha=0.3)
        plt.tight_layout()
        plt.show()
    except Exception as e:
        file_print(f"Erreur lors du trace du diagramme de Bode: {e}")
        import traceback
        traceback.print_exc()'''
    
    # Trouver et remplacer la fonction plot_bode existante
    import re
    
    pattern = r'def plot_bode\(transfer_function, values, freq_range, \*\*_\):.*?(?=\ndef |\nif __name__|$)'
    
    match = re.search(pattern, content, re.DOTALL)
    if match:
        # Remplacer la fonction
        new_content = content[:match.start()] + new_plot_bode + content[match.end():]
        
        # Sauvegarder le fichier modifié
        with open('test.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✓ Fonction plot_bode corrigée avec substitutions cohérentes")
        return True
    else:
        print("❌ Impossible de trouver la fonction plot_bode")
        return False

def test_fix():
    """Teste la correction avec un circuit simple"""
    
    print("\n=== TEST DE LA CORRECTION ===")
    
    # Circuit de test simple
    test_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n"""
    
    print("Circuit de test:")
    print(test_circuit)
    print()
    
    # Créer un script de test
    test_script = f'''
import sys
sys.path.append('.')

try:
    from test import solve_circuit
    
    netlist_str = """{test_circuit}"""
    
    print("=== Test de la correction ===")
    solve_circuit(netlist_str, 1000.0, True, "1", "2", 1.0, 1e6, laplace_domain=True)
    
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
'''
    
    # Sauvegarder et exécuter le script
    with open('test_correction.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        import subprocess
        result = subprocess.run(['python', 'test_correction.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            output = result.stdout
            print("Sortie du test:")
            print("-" * 40)
            print(output)
            print("-" * 40)
            
            if "OK Coherence symbolique/numerique verifiee" in output:
                print("🎉 SUCCÈS: Correction validée!")
                return True
            elif "ATTENTION Difference detectee" in output:
                print("⚠ Différence encore présente")
                return False
            else:
                print("? Résultat incertain")
                return False
        else:
            print("❌ Erreur lors du test")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False
    finally:
        # Nettoyer
        import os
        if os.path.exists('test_correction.py'):
            os.remove('test_correction.py')

def main():
    """Fonction principale"""
    
    print("CORRECTION DU PROBLÈME DE COHÉRENCE DES SUBSTITUTIONS")
    print("=" * 65)
    
    # Faire une sauvegarde
    import shutil
    shutil.copy('test.py', 'test.py.substitution_backup')
    print("✓ Sauvegarde créée: test.py.substitution_backup")
    
    # Appliquer la correction
    success = fix_plot_bode_substitutions()
    
    if success:
        print("\n✓ Correction appliquée")
        
        # Tester la correction
        test_success = test_fix()
        
        if test_success:
            print("\n🎉 CORRECTION RÉUSSIE!")
            print("\nLe problème de cohérence symbolique/numérique est maintenant résolu.")
            print("Les deux expressions utilisent exactement les mêmes substitutions.")
        else:
            print("\n⚠ Correction partielle - Tests supplémentaires nécessaires")
    else:
        print("\n❌ Échec de la correction")

if __name__ == "__main__":
    main()
