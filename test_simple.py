#!/usr/bin/env python3
"""
Test simple de la simplification symbolique
"""

import sympy as sp
from sympy import symbols

# Test simple avec une expression plus petite
def test_simple():
    # Expression de test simple
    expression_str = "R1*R2*C1*s + R1 + R2"
    
    print(f"Expression de test : {expression_str}")
    
    # Convertir en expression symbolique
    expr = sp.sympify(expression_str)
    print(f"Expression symbolique : {expr}")
    
    # Test de substitution
    R1, R2, C1, s = symbols('R1 R2 C1 s')
    
    # Supprimer C1 en la mettant à 0
    expr_simplified = expr.subs(C1, 0)
    print(f"Sans C1 : {expr_simplified}")
    
    # Supprimer R2 en le mettant à l'infini
    expr_simplified2 = expr.limit(R2, sp.oo)
    print(f"R2 -> infini : {expr_simplified2}")
    
    # Simplifier
    expr_final = sp.simplify(expr_simplified)
    print(f"Simplifié : {expr_final}")

if __name__ == "__main__":
    test_simple()
