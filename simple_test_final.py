#!/usr/bin/env python3
"""
Test simple et direct pour vérifier la correction
"""
import sys
import os

def test_simple_circuit():
    """Test avec un circuit très simple"""
    
    print("=== TEST SIMPLE DIRECT ===")
    
    try:
        sys.path.append('.')
        
        # Import direct
        from test import solve_circuit
        
        # Circuit très simple
        simple_netlist = """V1 1 0 DC 1 AC 1
R1 1 2 1k"""
        
        print("Circuit de test:")
        print(simple_netlist)
        print()
        print("Début du calcul...")
        
        # Rediriger la sortie vers un fichier
        import io
        from contextlib import redirect_stdout
        
        output_buffer = io.StringIO()
        
        with redirect_stdout(output_buffer):
            try:
                solve_circuit(simple_netlist, 1000.0, True, "1", "2", 1.0, 1e6, laplace_domain=True)
            except Exception as e:
                print(f"Erreur pendant solve_circuit: {e}")
        
        # Récupérer la sortie
        output = output_buffer.getvalue()
        
        print("=== SORTIE CAPTURÉE ===")
        print(output)
        
        # Analyser la sortie
        if "OK Coherence" in output:
            print("\n🎉 SUCCÈS: Cohérence vérifiée!")
            return True
        elif "ATTENTION Difference" in output:
            print("\n⚠ ATTENTION: Différence détectée")
            return False
        else:
            print("\n? Pas de message de cohérence trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_function_exists():
    """Vérifie que les fonctions nécessaires existent"""
    
    print("=== VÉRIFICATION DES FONCTIONS ===")
    
    try:
        sys.path.append('.')
        
        # Vérifier les imports
        from test import verify_symbolic_numeric_consistency, plot_bode
        print("✓ Fonctions importées avec succès")
        
        # Test de la fonction de vérification
        result = verify_symbolic_numeric_consistency("1.0", "1.0", {})
        print(f"✓ verify_symbolic_numeric_consistency fonctionne: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def analyze_plot_bode_again():
    """Analyse à nouveau la fonction plot_bode pour vérifier la correction"""
    
    print("\n=== ANALYSE POST-CORRECTION ===")
    
    with open('test.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher la fonction plot_bode
    import re
    plot_bode_match = re.search(r'def plot_bode\(.*?\n(?=def |\nif __name__|$)', content, re.DOTALL)
    
    if plot_bode_match:
        plot_bode_code = plot_bode_match.group(0)
        
        # Vérifier les éléments clés
        checks = [
            ("apply_consistent_substitution", "apply_consistent_substitution" in plot_bode_code),
            ("tf_symbolic_final", "tf_symbolic_final" in plot_bode_code),
            ("tf_numeric_final", "tf_numeric_final" in plot_bode_code),
            ("verify_symbolic_numeric_consistency", "verify_symbolic_numeric_consistency" in plot_bode_code),
            ("OK Coherence", "OK Coherence" in plot_bode_code)
        ]
        
        print("Vérifications dans plot_bode:")
        for check_name, check_result in checks:
            status = "✓" if check_result else "❌"
            print(f"  {status} {check_name}")
        
        # Compter les substitutions
        symbolic_substitutions = plot_bode_code.count("tf_symbolic_final")
        numeric_substitutions = plot_bode_code.count("tf_numeric_final")
        
        print(f"\nUtilisation des variables finales:")
        print(f"  tf_symbolic_final: {symbolic_substitutions} occurrences")
        print(f"  tf_numeric_final: {numeric_substitutions} occurrences")
        
        if symbolic_substitutions > 0 and numeric_substitutions > 0:
            print("✓ Les deux expressions utilisent la fonction cohérente")
            return True
        else:
            print("❌ Problème dans l'utilisation des expressions")
            return False
    else:
        print("❌ Fonction plot_bode non trouvée")
        return False

def main():
    """Fonction principale"""
    
    print("TEST FINAL DE VÉRIFICATION DE LA CORRECTION")
    print("=" * 55)
    
    # Vérifier que les fonctions existent
    functions_ok = check_function_exists()
    
    # Analyser la correction
    correction_ok = analyze_plot_bode_again()
    
    # Tester avec un circuit simple
    if functions_ok and correction_ok:
        test_ok = test_simple_circuit()
    else:
        test_ok = False
    
    print("\n" + "=" * 55)
    print("RÉSUMÉ FINAL")
    print("=" * 55)
    
    print(f"Fonctions disponibles: {'✓' if functions_ok else '❌'}")
    print(f"Correction appliquée: {'✓' if correction_ok else '❌'}")
    print(f"Test fonctionnel: {'✓' if test_ok else '❌'}")
    
    if functions_ok and correction_ok and test_ok:
        print("\n🎉 SUCCÈS COMPLET!")
        print("La correction de cohérence symbolique/numérique est opérationnelle.")
    elif functions_ok and correction_ok:
        print("\n✓ CORRECTION APPLIQUÉE")
        print("La correction est en place, testez avec votre circuit complet.")
    else:
        print("\n❌ PROBLÈME PERSISTANT")
        print("Investigation supplémentaire nécessaire.")
    
    print("\n📝 Pour tester avec le circuit original:")
    print("1. Lancez: python test.py")
    print("2. Entrez le circuit problématique")
    print("3. Demandez la fonction de transfert V1 -> V7")
    print("4. Vérifiez le message de cohérence")

if __name__ == "__main__":
    main()
