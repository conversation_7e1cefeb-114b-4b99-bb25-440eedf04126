#!/usr/bin/env python3
"""
Série de tests de debug pour résoudre le problème de cohérence
entre les expressions symbolique et numérique
"""
import sys
import os
import re
import subprocess
import tempfile

def debug_test_1_simple_circuit():
    """Test 1: Circuit très simple pour isoler le problème"""
    
    print("=== DEBUG TEST 1: Circuit très simple ===")
    
    simple_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 1k"""
    
    print("Circuit de test:")
    print(simple_circuit)
    print()
    
    return run_debug_test(simple_circuit, "1", "2", "Test1_Simple")

def debug_test_2_rc_circuit():
    """Test 2: Circuit RC simple"""
    
    print("=== DEBUG TEST 2: Circuit RC simple ===")
    
    rc_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 1k
C1 2 0 100n"""
    
    print("Circuit de test:")
    print(rc_circuit)
    print()
    
    return run_debug_test(rc_circuit, "1", "2", "Test2_RC")

def debug_test_3_original_without_bjt():
    """Test 3: Circuit original sans BJT"""
    
    print("=== DEBUG TEST 3: Circuit original sans BJT ===")
    
    no_bjt_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit de test:")
    print(no_bjt_circuit)
    print()
    
    return run_debug_test(no_bjt_circuit, "1", "7", "Test3_NoBJT")

def debug_test_4_original_full():
    """Test 4: Circuit original complet"""
    
    print("=== DEBUG TEST 4: Circuit original complet ===")
    
    full_circuit = """V1 1 0 DC 1 AC 1
R1 1 2 100
C1 2 3 100n
R2 3 4 10k
R3 3 0 10k
Q1 5 3 6 100 NPN 10p 2p
R4 5 4 1k
V2 4 0 DC 20 AC 0
R5 6 0 1k
C2 6 0 10u
C3 5 7 100n
R6 7 0 800"""
    
    print("Circuit de test:")
    print(full_circuit)
    print()
    
    return run_debug_test(full_circuit, "1", "7", "Test4_Full")

def run_debug_test(circuit, input_node, output_node, test_name):
    """Exécute un test de debug et analyse les résultats"""
    
    # Créer un script de test
    test_script = f'''
import sys
import os
sys.path.append('.')

# Rediriger la sortie vers un fichier
with open('{test_name}_output.txt', 'w', encoding='utf-8') as f:
    original_stdout = sys.stdout
    sys.stdout = f
    
    try:
        from test import solve_circuit
        
        netlist_str = """{circuit}"""
        
        print("=== {test_name} ===")
        print("Circuit:")
        print(netlist_str)
        print()
        
        # Paramètres de test
        frequency_hz = 1000.0
        do_transfer_function = True
        input_node = "{input_node}"
        output_node = "{output_node}"
        freq_min = 1.0
        freq_max = 1e6
        
        solve_circuit(netlist_str, frequency_hz, do_transfer_function, 
                     input_node, output_node, freq_min, freq_max, laplace_domain=True)
        
    except Exception as e:
        print(f"ERREUR: {{e}}")
        import traceback
        traceback.print_exc()
    finally:
        sys.stdout = original_stdout

print("{test_name} terminé")
'''
    
    # Sauvegarder et exécuter le script
    script_filename = f'{test_name}_script.py'
    with open(script_filename, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        result = subprocess.run(['python', script_filename], 
                              capture_output=True, text=True, timeout=120)
        
        # Analyser les résultats
        output_filename = f'{test_name}_output.txt'
        if os.path.exists(output_filename):
            with open(output_filename, 'r', encoding='utf-8') as f:
                output = f.read()
            
            print("Résultats du test:")
            print("-" * 40)
            
            # Extraire les informations importantes
            if "OK Coherence symbolique/numerique verifiee" in output:
                print("✓ SUCCÈS: Cohérence vérifiée")
                result_status = "SUCCESS"
            elif "ATTENTION Difference detectee" in output:
                print("⚠ ATTENTION: Différence détectée")
                # Extraire les détails de la différence
                diff_match = re.search(r'ATTENTION Difference detectee: (.+)', output)
                if diff_match:
                    print(f"  Détail: {diff_match.group(1)}")
                result_status = "DIFFERENCE"
            elif "Erreur lors de la verification de coherence" in output:
                print("❌ ERREUR: Problème de vérification")
                result_status = "ERROR"
            else:
                print("? INCONNU: Pas de message de cohérence trouvé")
                result_status = "UNKNOWN"
            
            # Extraire les fonctions de transfert si disponibles
            symbolic_match = re.search(r'Fonction de transfert symbolique: (.+)', output)
            numeric_match = re.search(r'Fonction de transfert numerique: (.+)', output)
            
            if symbolic_match and numeric_match:
                symbolic_expr = symbolic_match.group(1).strip()
                numeric_expr = numeric_match.group(1).strip()
                
                print(f"  Fonction symbolique: {len(symbolic_expr)} caractères")
                print(f"  Fonction numérique: {len(numeric_expr)} caractères")
                
                # Comparer les premières parties
                if len(symbolic_expr) > 50 and len(numeric_expr) > 50:
                    print(f"  Début symbolique: {symbolic_expr[:50]}...")
                    print(f"  Début numérique: {numeric_expr[:50]}...")
            
            print("-" * 40)
            print()
            
        else:
            print("❌ Fichier de sortie non trouvé")
            result_status = "NO_OUTPUT"
        
        return result_status
        
    except subprocess.TimeoutExpired:
        print("❌ Test interrompu (timeout)")
        return "TIMEOUT"
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return "EXECUTION_ERROR"
    finally:
        # Nettoyer les fichiers temporaires
        for file in [script_filename, f'{test_name}_output.txt']:
            if os.path.exists(file):
                os.remove(file)

def debug_test_5_manual_verification():
    """Test 5: Vérification manuelle de la logique de substitution"""
    
    print("=== DEBUG TEST 5: Vérification manuelle ===")
    
    try:
        sys.path.append('.')
        from test import verify_symbolic_numeric_consistency
        
        # Test avec expressions identiques
        print("Test 1 - Expressions identiques:")
        result1 = verify_symbolic_numeric_consistency("1.0", "1.0", {})
        print(f"  Résultat: {result1}")
        
        # Test avec expressions légèrement différentes
        print("Test 2 - Expressions légèrement différentes:")
        result2 = verify_symbolic_numeric_consistency("1.0", "1.1", {})
        print(f"  Résultat: {result2}")
        
        # Test avec expressions très différentes
        print("Test 3 - Expressions très différentes:")
        result3 = verify_symbolic_numeric_consistency("1.0", "2.0", {})
        print(f"  Résultat: {result3}")
        
        # Test avec expressions non numériques
        print("Test 4 - Expressions symboliques:")
        result4 = verify_symbolic_numeric_consistency("s+1", "s+1", {})
        print(f"  Résultat: {result4}")
        
        return "SUCCESS"
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification manuelle: {e}")
        import traceback
        traceback.print_exc()
        return "ERROR"

def debug_test_6_substitution_logic():
    """Test 6: Debug de la logique de substitution"""
    
    print("=== DEBUG TEST 6: Debug de la substitution ===")
    
    try:
        sys.path.append('.')
        
        # Simuler les valeurs du circuit problématique
        test_values = {
            'R1': '100',
            'R2': '10000', 
            'R3': '10000',
            'R4': '1000',
            'R5': '1000',
            'R6': '800',
            'C1': '1e-7',
            'C2': '1e-5',
            'C3': '1e-7',
            'Vbe_on_Q1': '0.7',
            'beta_Q1': '100',
            'Cbc_Q1': '2e-12',
            'ro_Q1': '100000',
            'rpi_Q1': '833.333333333',
            'Cbe_Q1': '1e-11'
        }
        
        print("Valeurs de test:")
        for key, value in test_values.items():
            print(f"  {key} = {value}")
        
        # Test de substitution simple
        test_expr = "R1*C1*s + 1"
        print(f"\nExpression de test: {test_expr}")
        
        # Simuler la substitution manuelle
        substituted = test_expr
        for var, val in test_values.items():
            if var in substituted:
                substituted = substituted.replace(var, val)
        
        print(f"Après substitution: {substituted}")
        
        # Évaluer l'expression
        try:
            # Remplacer s par une valeur pour tester
            test_s_value = "1000"  # s = j*2*pi*f avec f=159Hz
            final_expr = substituted.replace('s', test_s_value)
            print(f"Avec s={test_s_value}: {final_expr}")
            
            # Évaluer numériquement
            result = eval(final_expr)
            print(f"Résultat numérique: {result}")
            
        except Exception as e:
            print(f"Erreur d'évaluation: {e}")
        
        return "SUCCESS"
        
    except Exception as e:
        print(f"❌ Erreur lors du test de substitution: {e}")
        return "ERROR"

def main():
    """Fonction principale de debug"""
    
    print("SÉRIE DE TESTS DE DEBUG POUR LA COHÉRENCE SYMBOLIQUE/NUMÉRIQUE")
    print("=" * 80)
    print()
    
    # Exécuter tous les tests
    results = {}
    
    results["Test1_Simple"] = debug_test_1_simple_circuit()
    results["Test2_RC"] = debug_test_2_rc_circuit()
    results["Test3_NoBJT"] = debug_test_3_original_without_bjt()
    results["Test4_Full"] = debug_test_4_original_full()
    results["Test5_Manual"] = debug_test_5_manual_verification()
    results["Test6_Substitution"] = debug_test_6_substitution_logic()
    
    # Résumé des résultats
    print("=" * 80)
    print("RÉSUMÉ DES TESTS DE DEBUG")
    print("=" * 80)
    
    for test_name, result in results.items():
        status_icon = {
            "SUCCESS": "✓",
            "DIFFERENCE": "⚠",
            "ERROR": "❌",
            "TIMEOUT": "⏱",
            "EXECUTION_ERROR": "💥",
            "NO_OUTPUT": "📄",
            "UNKNOWN": "?"
        }.get(result, "?")
        
        print(f"{status_icon} {test_name}: {result}")
    
    print()
    print("ANALYSE:")
    
    # Analyser les patterns
    success_count = sum(1 for r in results.values() if r == "SUCCESS")
    difference_count = sum(1 for r in results.values() if r == "DIFFERENCE")
    error_count = sum(1 for r in results.values() if r in ["ERROR", "TIMEOUT", "EXECUTION_ERROR"])
    
    print(f"- Tests réussis: {success_count}/{len(results)}")
    print(f"- Tests avec différences: {difference_count}/{len(results)}")
    print(f"- Tests en erreur: {error_count}/{len(results)}")
    
    if difference_count > 0:
        print("\n🔍 RECOMMANDATIONS:")
        print("1. Vérifier la logique de substitution dans plot_bode")
        print("2. Comparer les expressions symbolique/numérique caractère par caractère")
        print("3. Vérifier l'ordre des substitutions")
        print("4. Analyser les simplifications automatiques")

if __name__ == "__main__":
    main()
