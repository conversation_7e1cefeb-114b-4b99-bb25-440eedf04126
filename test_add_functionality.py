#!/usr/bin/env python3
"""
Script de test pour vérifier la fonctionnalité d'ajout de termes dominants
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from bode_H1_final import *
    print("✓ Import du module bode_H1_final réussi")
except ImportError as e:
    print(f"✗ Erreur d'import: {e}")
    sys.exit(1)

def test_basic_functionality():
    """Test des fonctionnalités de base"""
    print("\n=== Test des fonctionnalités de base ===")
    
    # Test d'extraction de variables
    test_expr = "1/(R1*C1*s + 1) + R2/(R3*C2*s)"
    variables = extract_variables_from_expression(test_expr)
    print(f"Variables extraites de '{test_expr}': {variables}")
    
    # Test de création d'un évaluateur rapide
    try:
        evaluator = create_fast_evaluator(test_expr, tuple(variables))
        print("✓ Création d'évaluateur rapide réussie")
    except Exception as e:
        print(f"✗ Erreur création évaluateur: {e}")
    
    return True

def test_sympy_operations():
    """Test des opérations SymPy nécessaires"""
    print("\n=== Test des opérations SymPy ===")
    
    try:
        import sympy as sp
        
        # Test de parsing d'expression
        expr_str = "1/(R1*C1*s + 1)"
        expr = sp.sympify(expr_str)
        print(f"Expression parsée: {expr}")
        
        # Test de séparation numérateur/dénominateur
        num = sp.numer(expr)
        den = sp.denom(expr)
        print(f"Numérateur: {num}")
        print(f"Dénominateur: {den}")
        
        # Test d'extraction de termes
        if den.is_Add:
            terms = den.args
            print(f"Termes du dénominateur: {terms}")
        else:
            print(f"Dénominateur simple: {den}")
        
        # Test d'extraction de variables libres
        R1, C1, s = sp.symbols('R1 C1 s')
        free_vars = expr.free_symbols
        print(f"Variables libres: {free_vars}")
        
        print("✓ Opérations SymPy réussies")
        return True
        
    except Exception as e:
        print(f"✗ Erreur SymPy: {e}")
        return False

def test_gui_components():
    """Test des composants GUI"""
    print("\n=== Test des composants GUI ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # Créer une application Qt temporaire
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✓ Application Qt créée")
        
        # Test de création d'un bouton
        from PyQt5.QtWidgets import QPushButton
        btn = QPushButton("Test Add (0)")
        btn.setFixedWidth(80)
        print("✓ Bouton créé")
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur GUI: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=== Test de la fonctionnalité d'ajout de termes dominants ===")
    
    tests = [
        test_basic_functionality,
        test_sympy_operations,
        test_gui_components
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Erreur dans {test.__name__}: {e}")
            results.append(False)
    
    print(f"\n=== Résultats ===")
    print(f"Tests réussis: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ Tous les tests sont passés avec succès!")
        print("\nLa fonctionnalité d'ajout de termes dominants devrait fonctionner.")
        print("\nPour tester complètement:")
        print("1. Lancez bode_H1_final.py")
        print("2. Chargez une fonction de transfert")
        print("3. Lancez l'analyse de sensibilité")
        print("4. Simplifiez la fonction de transfert")
        print("5. Affichez le Bode simplifié")
        print("6. Utilisez les boutons 'Add' pour ajouter des termes")
    else:
        print("✗ Certains tests ont échoué. Vérifiez les dépendances.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
